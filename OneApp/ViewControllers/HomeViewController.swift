//  Copyright © 2018 Toyota. All rights reserved.

import AVFoundation
import Moya
import RxSwift
import SkyFloatingLabelTextField
import UIKit

class HomeViewController: ViewController, UITextFieldDelegate {
    // MARK: - Properties
    let disposeBag = DisposeBag()
    let underlay = PageUnderlayView()
    let toolbarTop = ThemedNavigationBar(frame: DUMMY_RECT)
    let mainContentView = UIView()
    let scrollView = UIScrollView()
    let contentView = UIView()
    var addVehicleViewModel = QRAddVehicleViewModel()
    var splashViewModel = SplashViewModel()

    let addVINTitleLabel: OATitleLabel = {
        let t = OATitleLabel(frame: CGRect.zero)
        t.textAlignment = NSTextAlignment.left
        t.textColor = UIColor.body
        t.font = theme.fontStyle.title3
        t.text = kAddYourVIN
        t.adjustsFontSizeToFitWidth = true
        t.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_LABEL_TITLE
        t.enableVoiceOverAsHeaderTraits()
        return t
    }()

    let addVINInfoLabel: UILabel = {
        let t = UILabel(frame: CGRect.zero)
        t.textAlignment = NSTextAlignment.left
        t.lineBreakMode = .byWordWrapping
        t.font = theme.fontStyle.body
        t.textColor = UIColor.body
        t.text = kYouCanFindThisOnTheDriverSideDashboard
        t.numberOfLines = 0
        t.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_LABEL_SUBTITLE
        return t
    }()
    let VINLabel: UILabel = {
        let label = UILabel()
        label.font = theme.fontStyle.caption1
        label.text = kVehicleIdentificationNumber
        label.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_LABEL_VIN
        return label
    }()
    var didRecieveVINErrorFromServer: Bool = false
    let VINTextField: TextFieldNoActions = {
        let textField = TextFieldNoActions()
        textField.returnKeyType = .done
        textField.enablesReturnKeyAutomatically = true
        textField.autocorrectionType = UITextAutocorrectionType.no
        textField.spellCheckingType = UITextSpellCheckingType.no
        textField.autocapitalizationType = UITextAutocapitalizationType.allCharacters
        textField.tintColor = UIColor.body
        textField.addTarget(self, action: #selector(textFieldDidChange(_:)), for: .editingChanged)
        textField.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_TEXTFIELD_VIN
        textField.enableVoiceOver()
        textField.accessibilityLabel = kAddVehicleVINTextField
        return textField
    }()
    let scanVINButton: OAButton = {
        let button = OAButton()
        button.setImage(#imageLiteral(resourceName: "AddVehicle_scan_camera"), for: .normal)
        button.contentMode = .center
        button.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_BUTTON_SCAN
        button.accessibilityLabel = kAddVehicleVINScan
        return button
    }()
    let clearTextButton: CircleCloseButton = {
        let button = CircleCloseButton()
        button.styleForTextViewClearButton()
        button.crossSize = 8.0
        button.addTarget(self, action: #selector(clickClearText(withButton:)), for: .touchUpInside)
        return button
    }()
    let textSeparatorLine: UIView = {
        let separator = UIView(frame: CGRect.zero)
        separator.isUserInteractionEnabled = false
        separator.isOpaque = true
        separator.clipsToBounds = true
        return separator
    }()
    let VINErrorLabel: OALabel = {
        let t = OALabel(frame: CGRect(x: 0, y: 0, width: 343, height: 48))
        t.textAlignment = NSTextAlignment.left
        t.numberOfLines = 0
        t.lineBreakMode = .byWordWrapping
        t.font = theme.fontStyle.caption1
        t.text = kVINisNotValid
        t.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_LABEL_ERROR
        t.accessibilityLabel = kAddVehicleInvalidVINDetailsError
        return t
    }()
    // We start in error state, in setup we will
    // switch out of error state, unanimated...
    private var isInterfaceInErrorState: Bool = true
    let addVehicleButton: GeneralButton = {
        let a = GeneralButton(type: .system)
        a.styleSetFooterPrimaryDisabled()
        a.setTitle(kAddVehicle, for: .normal)
        a.isEnabled = false
        a.addTarget(self, action: #selector(clickedAddVehicle), for: .touchUpInside)
        a.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_BUTTON_ADDVEHICLE
        return a
    }()
    let descriptionTextView: UITextView = {
        let result = UITextView()
        result.isEditable = false
        result.isScrollEnabled = false
        result.isSelectable = false
        result.bounces = false
        result.tintColor = theme.colorSecondary.color
        result.textAlignment = .left

        #if LEXUS
            let string = kAddVehiclePromptMessageLexus
            let linkStr = kAddVehiclePromptMessageHighlightedLexus
        #else
            let string = kAddVehiclePromptMessage
            let linkStr = kAddVehiclePromptMessageHighlighted
        #endif
        let attributedString = NSMutableAttributedString(
            string: string,
            attributes: [
                .font: theme.fontStyle.caption2,
                .foregroundColor: UIColor.body,
                .kern: 0.0,
            ]
        )
        attributedString.addAttributes(
            [
                .font: theme.fontStyle.callout,
                .link: getOldModelInformationLink(),
                .foregroundColor: theme.colorSecondary.color,
            ],
            range: (string as NSString).range(of: linkStr)
        )
        result.attributedText = attributedString
        result.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_TEXTVIEW_PROMPT
        result.accessibilityLabel = attributedString.string
        result.accessibilityElementsHidden = true
        result.isHidden = true
        return result
    }()
    let whereToFindYourVINLabel: OATitleLabel = {
        let label = OATitleLabel()
        label.text = kWhereToFindYourVIN
        label.textColor = UIColor.body
        label.font = theme.fontStyle.body
        label.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_LABEL_WHERE_TO_FIND_VIN
        label.enableVoiceOverAsHeaderTraits()
        label.isHidden = true
        return label
    }()
    let whereToFindYourVINArrow: UIButton = {
        let arrow = UIButton()
        arrow.setImage(#imageLiteral(resourceName: "subscription_chevronRight"), for: .normal)
        return arrow
    }()
    let whereToFindYourVINContainerView: UIView = {
        let view = UIView()
        view.clipsToBounds = true
        return view
    }()
    let whereToFindYourVINImageView: OAImageView = {
        let image = OAImageView(image: #imageLiteral(resourceName: "AddVehicle_where_to_find_vin"))
        image.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_IMAGE_WHERE_TO_FIND_VIN
        image.enableVoiceOverWithoutTraits()
        image.accessibilityLabel = kAddVehicleVINLocationImage
        return image
    }()
    let whereElseCouldIFindMyVINLabel: OATitleLabel = {
        let label = OATitleLabel()
        label.text = kWhereElseFindMyVINTitle
        label.textColor = theme.colorPrimary.colorContent.colorHint
        label.font = theme.fontStyle.caption1
        label.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_LABEL_WHERE_ELSE_TO_FIND_VIN
        label.enableVoiceOverAsHeaderTraits()
        return label
    }()
    let whereElseCouldIFindMyVINDescription1Label: UILabel = {
        let label = UILabel()
        label.text = kWhereElseFindMyVINDescription1
        label.textColor = UIColor.body
        label.font = theme.fontStyle.caption1
        label.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_LABEL_ELSE_WAYS1
        return label
    }()
    let whereElseCouldIFindMyVINDescription2Label: UILabel = {
        let label = UILabel()
        label.text = kWhereElseFindMyVINDescription2
        label.textColor = UIColor.body
        label.font = theme.fontStyle.caption1
        label.accessibilityIdentifier = OAAccessibilityID.ADDVEHICLE_LABEL_ELSE_WAYS2
        return label
    }()

    @objc
    func clickedAddVehicle() {
        Analytics.OALogEvent(.ADD_VIN)

        guard let vinNumber = VINTextField.text else {
            print("Unable to search VinNumber")
            return
        }

        addVehicleViewModel.addVehicle(vinNumber: vinNumber, view: self.view) { [weak self] error, detailDesc in
            guard let self = self, let error = error else { return }

            let errorType = AddVehicleErrorType(from: error)

            let brandNameFromTarget: String = {
                switch BrandCode.fromTarget() {
                case .toyota: return kBrandNameToyota
                case .lexus: return kBrandNameLexus
                case .subaru: return kBrandNameSubaru
                }
            }()

            let brandNameForHeader = errorType.brandName ?? brandNameFromTarget
            let resolvedDetailDesc = detailDesc ?? String(format: kProfileSavedSubTitle, brandNameForHeader)

            let sb1394Errors: Set<AddVehicleErrorType> = [.sb1394Toyota, .sb1394Lexus, .sb1394Subaru]
            let header: String

            switch errorType {
            case .appUpdate:
                self.showUpdateAlert()
                return

            case .pre17cy, .seventeenCy, .seventeenCyPlus, .twentyOneMM, .nullError,
                .sb1394Toyota, .sb1394Lexus, .sb1394Subaru:
                if sb1394Errors.contains(errorType) {
                    header = String(format: KFlaggedVinError, brandNameForHeader)
                    VINErrorLabel.textAlignment = .natural
                } else {
                    header = KVehicleNotLinked
                }

                mainStore.dispatch(
                    PushView(
                        viewState: .fleetVehicle(
                            header: header,
                            title: resolvedDetailDesc,
                            subTitle: "",
                            continueBtn: kContinueToDashboard,
                            imageName: "nonLinked"
                        )
                    )
                )
            case .unknown:
                showErrorMessage(errorMessage: error)
            }

            VINErrorLabel.text = error

        }
    }

    private func showUpdateAlert() {
        self.splashViewModel.showUpdateAvailableAlert()
    }

    private func showErrorMessage(errorMessage: String) {
        mainStore.dispatch(SetNotificationState(message: .serverError(message: errorMessage)))
        mainStore.dispatch(ShowNotificationAction())
    }

    // MARK: View Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setup()

        if checkIsDemoMode(showsDialog: false) == true {
            #if LEXUS
                VINTextField.text = kLexusDemoVIN
            #else
                VINTextField.text = kToyotaDemoVIN
            #endif
            textFieldDidChange(VINTextField)
        }
    }

    // MARK: Layout
    internal func setup() {
        if LocalizableHelper.isLexusLanguageCuttingOff() {
            whereElseCouldIFindMyVINDescription2Label.font = theme.fontStyle.footnote
        }
        view.addSubview(underlay)
        underlay.snp.makeConstraints { make in
            make.edges.equalTo(view)
        }
        underlay.setup()
        view.addSubview(mainContentView)
        view.addSubview(toolbarTop)
        if checkIsDemoMode(showsDialog: false) {
            let bottomOverlayView = UIView()
            bottomOverlayView.backgroundColor = .black
            view.addSubview(bottomOverlayView)
            bottomOverlayView.snp.makeConstraints { maker in
                maker.left.right.bottom.equalToSuperview()
                maker.height.equalTo(34)
            }

            let demoControl = DemoView(skipClosure: { [weak self] in
                self?.moveToDashboard()
            })
            view.addSubview(demoControl)
            demoControl.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom)
                make.height.equalTo(72.downwardAdjusted)
            }
            demoControl.setupDemoControl()

            mainContentView.snp.makeConstraints { make in
                make.top.equalTo(toolbarTop.snp.bottom)
                make.left.right.equalTo(self.view)
                make.bottom.equalTo(demoControl.snp.top)
            }
        } else {
            mainContentView.snp.makeConstraints { make in
                make.top.equalTo(toolbarTop.snp.bottom)
                make.left.right.bottom.equalTo(self.view)
            }
        }

        toolbarTop.pinTop(view)
        self.toolbarTop.titleLabel.disableVoiceOver()
        toolbarTop.addBackButton {
            if mainStore.state.registrationState?.vehicleList?.isEmpty != false && !isEmptyDashbaordEnabled {
                AuthenticationManager.shared.logout { success in
                    guard !success else { return }

                    AuthenticationManager.shared.completeLogout()
                }
            } else {
                mainStore.dispatch(SetVehicleState(vehicleState: nil))
                mainStore.dispatch(PopView())
            }
        }
        mainContentView.addSubview(scrollView)
        mainContentView.clipsToBounds = true
        scrollView.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.top.bottom.equalTo(scrollView)
            make.left.right.equalTo(view)  // => IMPORTANT: this makes the width of the contentview static (= size of the screen), while the contentview will stretch vertically
        }
        contentView.addSubview(addVINTitleLabel)
        addVINTitleLabel.snp.makeConstraints { make in
            make.left.right.equalTo(contentView).inset(16)
            make.top.equalTo(contentView).inset(16)
        }
        contentView.addSubview(addVINInfoLabel)
        addVINInfoLabel.snp.makeConstraints { make in
            make.left.right.equalTo(contentView).inset(16)
            make.top.equalTo(addVINTitleLabel.snp.bottom).offset(16)
        }
        contentView.addSubview(VINLabel)
        VINLabel.snp.makeConstraints { make in
            make.left.right.equalTo(contentView).inset(16)
            make.top.equalTo(addVINInfoLabel.snp.bottom).offset(27)
        }
        contentView.addSubview(VINTextField)
        VINTextField.delegate = self
        stylizeForValidation()
        VINTextField.snp.makeConstraints { make in
            make.left.equalTo(contentView.snp.left).inset(16.0)
            make.right.equalTo(contentView.snp.right).inset(16.0 + 44.0)
            make.top.equalTo(VINLabel.snp.bottom).inset(2)
            make.height.equalTo(44.0)
        }
        view.layoutIfNeeded()
        contentView.addSubview(scanVINButton)
        scanVINButton.snp.makeConstraints { make in
            make.left.equalTo(VINTextField.snp.right)
            make.centerY.equalTo(VINTextField.snp.centerY)
            make.width.height.equalTo(44)
        }
        contentView.addSubview(textSeparatorLine)
        textSeparatorLine.snp.makeConstraints { make in
            make.left.right.equalTo(contentView).inset(16)
            make.top.equalTo(VINTextField.snp.bottom).inset(4.0)
            make.height.equalTo(2.0)
        }
        contentView.addSubview(VINErrorLabel)
        VINErrorLabel.snp.makeConstraints { make in
            make.left.right.equalTo(contentView).inset(16)
            make.top.equalTo(textSeparatorLine.snp.bottom).offset(4.0)
        }
        contentView.addSubview(addVehicleButton)
        addVehicleButton.snp.makeConstraints { make in
            make.left.right.equalTo(contentView).inset(16)
            make.height.equalTo(50)
        }
        addVehicleButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: 16)
            .isActive = true  //Force bottom constraints

        let textViewHeight =
            descriptionTextView.sizeThatFits(
                CGSize(width: (mainWindow.bounds.size.width) - 32, height: CGFloat.greatestFiniteMagnitude)
            )
            .height
        contentView.addSubview(descriptionTextView)
        descriptionTextView.snp.makeConstraints { make in
            make.left.right.equalTo(contentView).inset(16)
            make.top.equalTo(addVehicleButton.snp.bottom).offset(16)
            make.height.equalTo(textViewHeight)
        }
        descriptionTextView.delegate = self
        contentView.addSubview(whereToFindYourVINLabel)
        whereToFindYourVINLabel.snp.makeConstraints { make in
            make.left.equalTo(contentView).inset(16)
            make.top.equalTo(descriptionTextView.snp.bottom).offset(32)
            make.height.equalTo(24)
        }
        contentView.addSubview(whereToFindYourVINContainerView)
        whereToFindYourVINContainerView.snp.makeConstraints { make in
            make.left.right.equalTo(contentView)
            make.height.equalTo(0)
            make.bottom.equalTo(contentView).inset(56)
        }
        whereToFindYourVINContainerView.addSubview(whereToFindYourVINImageView)
        whereToFindYourVINImageView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.top.equalToSuperview()
            #if LEXUS
                make.height.equalTo(whereToFindYourVINImageView.snp.width).multipliedBy(160.0 / 350.0)
            #else
                make.height.equalTo(whereToFindYourVINImageView.snp.width).multipliedBy(154.0 / 336.0)
            #endif
        }
        whereToFindYourVINContainerView.addSubview(whereElseCouldIFindMyVINLabel)
        whereElseCouldIFindMyVINLabel.backgroundColor = .yellow
        whereElseCouldIFindMyVINLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.top.equalTo(whereToFindYourVINImageView.snp.bottom).offset(UIScreen.main.bounds.height / 3.5)
            make.height.equalTo(0)
        }
        whereToFindYourVINContainerView.addSubview(whereElseCouldIFindMyVINDescription1Label)
        whereElseCouldIFindMyVINDescription1Label.backgroundColor = .green
        whereElseCouldIFindMyVINDescription1Label.snp.makeConstraints { make in
            make.left.equalToSuperview().inset(30)
            make.top.equalTo(whereElseCouldIFindMyVINLabel.snp.bottom).offset(5)
            make.height.equalTo(0)
        }
        whereToFindYourVINContainerView.addSubview(whereElseCouldIFindMyVINDescription2Label)
        whereElseCouldIFindMyVINDescription2Label.backgroundColor = .red
        whereElseCouldIFindMyVINDescription2Label.snp.makeConstraints { make in
            make.left.equalToSuperview().inset(30)
            make.top.equalTo(whereElseCouldIFindMyVINDescription1Label.snp.bottom).offset(0)
            make.height.equalTo(0)
        }
        scrollView.snp.makeConstraints { make in
            make.left.right.equalTo(mainContentView)
            make.top.equalTo(mainContentView)
            make.bottom.equalTo(mainContentView)
        }

        view.layoutIfNeeded()

        setIsInterfaceInErrorState(false, animated: false)

        textFieldDidChange(VINTextField)

        scanVINButton.rx.tap
            .subscribe(onNext: { [weak self] _ in
                if checkIsDemoMode(showsDialog: true) {
                    return
                }

                DispatchQueue.main.async {
                    Analytics.OALogEvent(.ADD_VEHICLE_SCAN_USING_CAMERA)
                    self?.checkCameraPermission()
                }
            })
            .disposed(by: disposeBag)
        showWhereToFindYourVinGuildance()
    }

    override func shouldShowExitDemoButton() -> Bool {
        false
    }

    func showWhereToFindYourVinGuildance() {
        var newHeight: CGFloat = 0
        var newAlpha: CGFloat = 0
        var newTransformDegree: CGFloat = 0

        newHeight = self.whereElseCouldIFindMyVINDescription2Label.frame.maxY
        newAlpha = 1
        newTransformDegree = 90
        self.whereToFindYourVINContainerView.snp.updateConstraints({ make in
            make.height.equalTo(newHeight)
        })
        self.whereToFindYourVINArrow.transform = CGAffineTransform(
            rotationAngle: (newTransformDegree * CGFloat(Double.pi)) / 180.0
        )
        self.whereToFindYourVINContainerView.alpha = newAlpha
    }

    func showVINError() {
        didRecieveVINErrorFromServer = true
        stylizeForValidation()
        setIsInterfaceInErrorState(true, animated: true)
    }

    @objc
    func clickClearText(withButton button: UIButton) {
        didRecieveVINErrorFromServer = false
        VINTextField.text = ""
        VINTextField.becomeFirstResponder()
        textFieldDidChange(VINTextField)
    }
    // MARK: - Text field event responders
    @objc
    public func textFieldDidChange(_ textField: UITextField) {
        let textString = textField.text
        textField.text = "\(textString?.prefix(17) ?? "")"
        didRecieveVINErrorFromServer = false
        if validateVinTextFieldWithText(vin: textField.text) {
            addVehicleButton.styleSetFooterPrimary()
            addVehicleButton.isEnabled = true
        } else {
            addVehicleButton.styleSetFooterPrimaryDisabled()
            addVehicleButton.isEnabled = false
        }
        stylizeForValidation()
        setIsInterfaceInErrorState(false, animated: true)
    }
    // MARK: - validation
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField == VINTextField {
            if validateVinTextFieldWithText(vin: VINTextField.text) {
                clickedAddVehicle()
            } else {
                VINTextField.resignFirstResponder()
            }
        }
        return false
    }
    func validateVinTextFieldWithText(vin: String?) -> Bool {
        guard let vin = vin else {
            return false
        }
        if vin.isEmpty {
            return false
        } else if !validateVin(vin) {
            return false
        } else {
            return true
        }
    }
    func validateVin(_ candidate: String) -> Bool {
        let regex = try? NSRegularExpression(pattern: ".*[^A-Za-z0-9].*")
        guard
            candidate.count == 17
                && (regex?.numberOfMatches(in: candidate, range: NSRange(location: 0, length: candidate.count)) ?? 0
                    == 0)
        else {
            return false
        }
        return true
    }

    func stylizeForValidation() {
        var color = UIColor.body
        if didRecieveVINErrorFromServer {
            color = theme.ERROR_TEXT
        }
        textSeparatorLine.backgroundColor = color
        VINTextField.textColor = color
        VINLabel.textColor = color
        VINErrorLabel.textColor = color
        if didRecieveVINErrorFromServer {
            VINLabel.textColor = color
        } else {
            VINLabel.textColor = theme.HINT_TEXT
        }
        if let text = VINTextField.text, !text.isEmpty {
            clearTextButton.isHidden = false
        } else {
            clearTextButton.isHidden = true
        }
    }
    func setIsInterfaceInErrorState(_ errorState: Bool, animated: Bool) {
        if errorState == isInterfaceInErrorState {
            return
        }

        isInterfaceInErrorState = errorState
        var buttonTransform = CGAffineTransform.identity
        if isInterfaceInErrorState == false {
            addVehicleButton.layer.setAffineTransform(CGAffineTransform.identity)
            buttonTransform = buttonTransform.translatedBy(x: 0.0, y: -VINErrorLabel.bounds.size.height)
        }

        let interfaceChangeBlock = {
            self.addVehicleButton.layer.setAffineTransform(buttonTransform)
            if self.isInterfaceInErrorState {
                self.VINErrorLabel.alpha = 1.0
            } else {
                self.VINErrorLabel.alpha = 0.0
            }
        }

        if animated == false {
            interfaceChangeBlock()
        } else {
            UIView.animate(
                withDuration: 0.35,
                delay: 0.0,
                options: UIView.AnimationOptions.curveEaseOut,
                animations: {
                    interfaceChangeBlock()
                },
                completion: nil
            )
        }
    }

    // MARK: - Scanner
    func showScanner() {
        DispatchQueue.main.async {
            let scanController = VINScanViewController()
            scanController.modalPresentationStyle = .fullScreen
            self.VINTextField.text = ""
            self.addVehicleButton.styleSetFooterPrimaryDisabled()
            self.addVehicleButton.isEnabled = false

            scanController.scanCompletion = { [weak self] vin in
                guard let self = self, vin.isValid() else {
                    return
                }
                self.VINTextField.text = vin
                if self.validateVinTextFieldWithText(vin: vin) == true {
                    self.addVehicleButton.styleSetFooterPrimary()
                    self.addVehicleButton.isEnabled = true
                } else {
                    self.addVehicleButton.styleSetFooterPrimaryDisabled()
                    self.addVehicleButton.isEnabled = false
                }
            }
            self.present(scanController, animated: true) {
                ActivityProgressView.hide()
            }
        }
    }
}

extension HomeViewController {
    func textField(
        _ textField: UITextField,
        shouldChangeCharactersIn range: NSRange,
        replacementString string: String
    ) -> Bool {
        guard string.textAllowedChars() else {
            return false
        }

        if string == " " {
            return false
        }

        if !string.isEmpty && range.location > 16 {
            return false
        }

        let trimmingString = string.trimmingCharacters(in: CharacterSet.whitespaces)
        if trimmingString.count + (textField.text?.count ?? 0) > 17 {
            textField.text = "\(textField.text ?? "")\(trimmingString.prefix(17))"
            return true
        }

        let lowercaseCharRange: NSRange = (string as NSString).rangeOfCharacter(from: CharacterSet.lowercaseLetters)
        if Int(lowercaseCharRange.location) != NSNotFound {
            if textField.text?.isEmpty != false {
                textField.text = (textField.text as NSString?)?
                    .replacingCharacters(in: range, with: string.uppercased())
            } else {
                let beginning: UITextPosition = textField.beginningOfDocument
                let start: UITextPosition? = textField.position(from: beginning, offset: Int(range.location))
                var end: UITextPosition?
                if let start = start {
                    end = textField.position(from: start, offset: Int(range.length))
                }
                var textRange: UITextRange?
                if let start = start, let end = end {
                    textRange = textField.textRange(from: start, to: end)
                }

                if let textRange = textRange {
                    textField.replace(textRange, withText: string.uppercased())
                }
            }
            return false
        }
        return true
    }
}

extension HomeViewController: UITextViewDelegate {
    func textView(
        _ textView: UITextView,
        shouldInteractWith URL: URL,
        in characterRange: NSRange,
        interaction: UITextItemInteraction
    ) -> Bool {
        let vc = CustomBrowserViewController()
        vc.urlStr = URL.absoluteString
        self.present(vc, animated: true, completion: nil)
        return false
    }
}

// MARK: - Accessibility Identifier
extension HomeViewController {
    private enum OAAccessibilityID {
        static let ADDVEHICLE_LABEL_TITLE = "ADDVEHICLE_LABEL_TITLE"
        static let ADDVEHICLE_LABEL_SUBTITLE = "ADDVEHICLE_LABEL_SUBTITLE"
        static let ADDVEHICLE_LABEL_VIN = "ADDVEHICLE_LABEL_VIN"
        static let ADDVEHICLE_TEXTFIELD_VIN = "ADDVEHICLE_TEXTFIELD_VIN"
        static let ADDVEHICLE_BUTTON_SCAN = "ADDVEHICLE_BUTTON_SCAN"
        static let ADDVEHICLE_BUTTON_ADDVEHICLE = "ADDVEHICLE_BUTTON_ADDVEHICLE"
        static let ADDVEHICLE_LABEL_ERROR = "ADDVEHICLE_LABEL_ERROR"
        static let ADDVEHICLE_TEXTVIEW_PROMPT = "ADDVEHICLE_TEXTVIEW_PROMPT"
        static let ADDVEHICLE_LABEL_WHERE_TO_FIND_VIN = "ADDVEHICLE_LABEL_WHERE_TO_FIND_VIN"
        static let ADDVEHICLE_IMAGE_WHERE_TO_FIND_VIN = "ADDVEHICLE_IMAGE_WHERE_TO_FIND_VIN"
        static let ADDVEHICLE_LABEL_WHERE_ELSE_TO_FIND_VIN = "ADDVEHICLE_LABEL_WHERE_ELSE_TO_FIND_VIN"
        static let ADDVEHICLE_LABEL_ELSE_WAYS1 = "ADDVEHICLE_LABEL_ELSE_WAYS1"
        static let ADDVEHICLE_LABEL_ELSE_WAYS2 = "ADDVEHICLE_LABEL_ELSE_WAYS2"
    }
}

// MARK: - Helper Methods

extension HomeViewController {
    private func checkCameraPermission() {
        PermissionManager.checkCameraPermissions { [weak self] granted in
            guard let self = self else { return }
            if granted {
                self.showScanner()
            } else {
                ActivityProgressView.hide()
                PermissionManager.openSettings(title: kSettingCameraTitle, message: kCommonPermissionMessage)
            }
        }
    }

    private func moveToDashboard() {
        mainStore.dispatch(SetRoute(route: [.dashBoard]))
    }
}
