//  Copyright © 2018 Toyota. All rights reserved.

import UIKit

#if SIRI || EXTENSION
import OACommonExtension
#else
import OACommon
#endif

// swiftlint:disable file_length
public let kShowsEngineStartRequestToast = "kShowsEngineStartRequestToast"
public let kShowsEngineStopRequestToast = "kShowsEngineStopRequestToast"
public let kShowsDoorLockRequestToast = "kShowsDoorLockRequestToast"
public let kShowsDoorUnlockRequestToast = "kShowsDoorUnlockRequestToast"
public let kShowsHazardOnRequestToast = "kShowsHazardOnRequestToast"
public let kShowsHazardOffRequestToast = "kShowsHazardOffRequestToast"
public let kShowsHornOnRequestToast = "kShowsHornOnRequestToast"
public let kShowsHornOffRequestToast = "kShowsHornOffRequestToast"
public let kShowsHeadLampOnRequestToast = "kShowsHeadLampOnRequestToast"
public let kShowsHeadLampOffRequestToast = "kShowsHeadLampOffRequestToast"
public let kShowsWindowsRequestToast = "kShowsWindowsRequestToast"
public let kShowsTrunkLockRequestToast = "kShowsTrunkLockRequestToast"
public let kShowsTrunkUnlockRequestToast = "kShowsTrunkUnlockRequestToast"
public let kShowsBuzzerRequestToast = "kShowsBuzzerRequestToast"
public let kUBIOptin = "i"
public let kUBIOptout = "o"
public let kLCFSOptin = "i"
public let kLCFSOptout = "o"
public let kEmptyVehicleVin = "empty"

let DUMMY_RECT = CGRect(x: 32.0, y: 32.0, width: 32.0, height: 32.0)

// MARK: - Cell Identifiers
public let kVehicleOverViewCell = "VehicleOverViewCell"
public let kGuestDriverSettingsCell = "GuestDriverSettingsCell"
public let kGuestDriverDetailsCell = "GuestDriverDetailsCell"
public let kCurfewDaysCell = "CurfewDaysCell"
public let kVehicleCapabilitiesViewCell = "VehicleCapabilitiesViewCell"
public let kGarageInfoCell = "GarageInfoCell"
public let kServiceTrialCell = "ServiceTrialCell"
public let kDateTimeCell = "DateTimeCell"
public let kManualAndWarrantiesInfoCell = "ManualAndWarrantiesInfoCell"
public let kServiceHistoryCell = "ServiceHistoryCell"
public let kHowToVideoCell = "HowToVideoCell"

// MARK: - Public keys and values
public let kLCFSOptInNo = false
public let kLCFSOptInYesString = "In"

// MARK: - Public keys and values
public let kSkyFloatingLabelTextField = "SkyFloatingLabelTextField"
public let kIconsArrowLeft = "icons-arrow-left"
public let kRotation = "rotation"
public let kTransformRotation = "transform.rotation"
public let kTransformRotationZ = "transform.rotation.z"
public let kToyotaDemoVIN = "4T1B11HK4KU2XXXXX"
public let kLexusDemoVIN = "JTJHZKFA8L20XXXXX"

public let kIsFromSplashScreen = "kIsFromSplashScreen"
public let kUpdateVehicleStatusSuccess = "kUpdateVehicleStatusSuccess"
public let kUpdateVehicleStatusFailed = "kUpdateVehicleStatusFailed"

public let kAPP_VERSION = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? ""
public let kToyotaAppStoreLink = "itms-apps://itunes.apple.com/app/apple-store/id1455685357?mt=8"
public let kLexusAppStoreLink = "itms-apps://itunes.apple.com/app/apple-store/id1468484450?mt=8"
public let kSubaruAppStoreLink = "itms-apps://itunes.apple.com/app/apple-store/id1612881539?mt=8"

public let kSplash_view = "splash_view"
public let kConfigurationKey = "ActiveConfiguration"
public let kSXMProductsAccessToken = "ProductAccessToken"
public let kKeepMeLoggedIn = "kKeepMeLoggedIn"
let kIsLoggedIn = "kIsLoggedIn"
public let kRemoteAuthorization = "REMOTE_AUTHORIZATION"
public let kRemoteCmdUser = "REMOTECMD_USER"
public let kDevGUID = "92217ed670cc46449b07cdcf4f365dd0"
public let kLocalPushNotificationIdentifier = "LocalPushNotificationForRemoteCommand"

public let kRemoteCommandTimeout: TimeInterval = 150.0
public let kLongPressThresholdInterval: TimeInterval = 0.5

public let kToyotaPrivacyLink_en_US = "https://www.toyota.com/privacyvts/"
public let kToyotaDynamickNavigationHelpLink = "https://toyotaaudioandconnectedservicessupport.com/toyota/home"
public let kLexusDynamickNavigationHelpLink = "https://lexusconnectedtechnologysupport.com/lexus/home"
public let kToyotaVistSupportPageLink = "https://toyotaaudioandconnectedservicessupport.com/toyota/home"
public let kLexusVistSupportPageLink = "https://lexusconnectedtechnologysupport.com/lexus/home"

public let kToyotaManagePreferencesStgBaseURL = "https://stg-tcom.cdn.cepo-proxy.tms.aws.toyota.com"
public let kLexusManagePreferencesStgBaseURL = "https://stg-lcom.cdn.cepo-proxy.tms.aws.lexus.com"
public let kToyotaManagePreferencesBaseURL = "https://toyota.com"
public let kLexusManagePreferencesBaseURL = "https://lexus.com"
public let kManagePreferencesEndpoint = "/preferences/mypreferences"

public let kLexusCustomerExperienceCenterCanada = "1-800-265-3987"
public let kToyotaCustomerExperienceCenterCanada = "1-888-869-6828"
public let kSubaruCustomerExperienceCenterCanada = "1-800-263-8802"
public let kToyotaCustomerExperienceCenterPuertoRico = "1-877-855-8377"
public let kLexusCustomerExperienceCenterPuertoRico = "1-877-539-8777"
public let kLexusCustomerExperienceCenterUS = "1-800-255-3987"
public let kToyotaCustomerExperienceCenterUS = "1-800-331-4331"
public let kSubaruCustomerExperienceCenterUS = "1-866-384-3574"
public let kToyotaCustomerExperienceCenterHI = "1-888-845-1583"
public let kLexusCustomerExperienceCenterHI = "1-888-845-1424"

public let kLexusRoadsideAssistanceCanada = "1-800-265-3987"
public let kToyotaRoadsideAssistanceCanada = "1-888-869-6828"
public let kToyotaRoadsideAssistancePuertoRico = "1-800-981-8166"
public let kLexusRoadsideAssistancePuertoRico = "1-800-773-1877"
public let kLexusRoadsideAssistanceMexico = "800-00-53987"
public let kLexusRoadsideAssistanceUS = "1-855-836-1656"
public let kToyotaRoadsideAssistanceUS = "1-800-444-4195"
public let kLexusRoadsideAssistanceServco = "1-877-525-0778"
public let kToyotaRoadsideAssistanceServco = "1-877-525-1778"

public let kToyotaLexusSVLCustomerExperienceCenterUS = "1-855-943-7768"
public let kToyotaLexusSVLCustomerExperienceCenterTCI = "1-855-943-7771"

public let Lexus_Toyota_CustomerCare_Paid = "1-800-922-0204"

public let kLexusEnformGuestSupport = "1-877-325-0985"
public let kAT_T_CustomerCare = "1-866-595-1242"

public let kAppOutage = "appoutage"
public let kVehicleSoftwareVersionUpdateCount = "1"
public let kFRConfigPrefix = "FRAuthConfig_"

// MARK: User Defaults
public let kUserDefaultsRequireAuthenticationTime = "requireAuthenticationTime"
public let kUserDefaultsFaceIdUnlockEnabled = "faceIDEnabled"
public let kUserDefaultsTimeoutTime = "timeoutTime"
public let kLegacyPaidSessionTimeStamp = "legacyPaidSessionTimeStamp"
public let kUserDefaultsHasShownTFSLinkingToast = "hasShownTFSLinkingToast"
public let kUserDefaultsNeedToUnlinkTFSNotificationPreferences = "needToUnlinkTFSNotificationPreferences"
public let kUserDefaultsNeedToLinkTFSNotificationPreferences = "needToLinkTFSNotificationPreferences"
public let kUserDefaultsTFSBiometricRequirementMet = "tfsBiometricRequirementMet"
public let kUserDefaultsAppHasLaunchedBefore = "appHasLaunchedBefore"
public let kUserDefaultsHasShownTFSDisabledError = "hasShownTFSDisabledError"
public let kUserDefaultsHasShownTFSRevalidationError = "hasShownTFSRevalidationError"
public let kUserDefaultsHasShownBiometricPrompt = "hasShownBiometricPrompt"
public let kUserDefaultsIsDemoMode = "isDemoMode"
public let kUserDefaultsDashboardCardOrderArray = "dashboardCardOrderArray"
public let kUserDefaultsDataDogEnabled = "dataDogEnabled"
public let kTimesChargeAssistAnnouncementHasShown = "timesChargeAssistAnnouncementShown"

// Service UUID for BLE connections
public let HULexusService = "16255ADF-8B4D-4E2F-80A0-E99193217028"
public let HUToyotaService = "60323118-D9F1-4BDF-AF72-ED68AF1EDCD2"
public let HUSubaruService = "7B012ADA-8D77-11EB-8DCD-0242AC130003"
public let HUWriteCharacteristic = "45FFF39A-4C31-11E9-8646-D663BD873D93"
public let HUReadCharacteristic = "7DBF6F28-4F2D-11E9-8647-D663BD873D93"
public let UPPacket1 = "packet1"
public let UPPacket2 = "packet2"
public let ToyotaManufactureData = "D2DC1EAF68ED72AFDF4BF1D918313260"
public let LexusManufactureData = "2870219391E9A0802F4E4D8BDF5A2516"
public let SubaruManufactureData = "030013AC4202CD8DEB11778DDA2A017B"
public let ToyotaProtocolString = "com.toyota.mfi.21mmhu"
public let LexusProtocolString = "com.lexus.mfi.21mmhu"
public let SubaruProtocolString = "com.subaru.mfi.21mmhu"

// MARK: Keychain
public let kKeychainVinsThatNeedTFSNotificationsLinked = "vinsThatNeedTFSNotificationsLinked"
public let kKeychainVinsThatNeedTFSNotificationsDelinked = "vinsThatNeedTFSNotificationsDelinked"
public let kKeychainTFSAccount = "TFSAccount"
public let kKeychainAllTFSVins = "AllTFSVins"
public let kGuid = "GUID"

// MARK: TFS Links
public let kLFSManageAccount = "https://www.lexusfinancial.com/us/en/consumer-web/home/<USER>"
public let kTFSManageAccount = "https://www.toyotafinancial.com/us/en/consumer-web/home/<USER>"
public let kLFSResetPasswordURL = "https://www.lexusfinancial.com/us/en/consumer-web/self-service/unlock-account"
public let kTFSResetPasswordURL = "https://www.toyotafinancial.com/us/en/consumer-web/self-service/unlock-account"
public let kLFSOnlinePoliciesURL = "https://www.lexusfinancial.com/us/en/online_policies_and_agreements.html"
public let kLFSPrivacyPolicyURL = "https://www.lexusfinancial.com/us/en/online_policies_and_agreements.html#opp"
public let kTFSOnlinePoliciesURL = "https://www.toyotafinancial.com/us/en/online_policies_and_agreements.html"
public let kTFSPrivacyPolicyURL = "https://www.toyotafinancial.com/us/en/online_policies_and_agreements.html#opp"
public let kTFSAppLink = "https://apps.apple.com/us/app/mytfs-toyota-financial/id472110881"
public let kLFSAppLink = "https://apps.apple.com/us/app/mylfs-lexus-financial/id472183447"
public let kLFSCustomerServiceNumber = "1-800-874-7050"
public let kTFSCustomerServiceNumber = "1-800-874-8822"
public let kLFSAffiliatePrivacyPreferences = "https://www.lexusfinancial.com/us/en/consumer-web/secure/my-profile/privacy-choices"
public let kTFSAffiliatePrivacyPreferences = "https://www.toyotafinancial.com/us/en/consumer-web/secure/my-profile/privacy-choices"
public let kTFSWebsite = "Toyotafinancial.com"
public let kLFSWebsite = "Lexusfinancial.com"
public let kTFSWebsiteURL = "https://www.toyotafinancial.com"
public let kLFSWebsiteURL = "https://www.lexusfinancial.com"
public let kTFSLoanPayoffURL = "https://www.toyotafinancial.com/us/en/consumer-web/secure/leaseend-payoff/summary"
public let kLFSLoanPayoffURL = "https://www.lexusfinancial.com/us/en/consumer-web/secure/leaseend-payoff/summary"

// MARK: Delete Account Links
public let kPrivacyLink = "https://privacy.toyota.com/"
public var kPrivacyDeleteLink: String {
#if LEXUS
    "https://privacy.lexus.com/"
#else
    "https://privacy.toyota.com/"
#endif
    
}


// MARK: FAQ

public let kPopularFAQ = "POPULAR_FAQ"
public let kAdditionalFAQ = "ADDITIONAL_FAQ"

// MARK: Subscriptions
public let kStatusActive = "ACTIVE"
public let kSubscriptionTerm_Monthly = "MTH"
public let kSubscriptionTerm_Yearly = "YRLY"
public let kSubscriptionCurrency_USD = "USD"
public let kSubscriptionCurrency_CAD = "CAD"
public let kSubscriptionCurrency_MEX = "MP"
public let KSafetySenseVideoConsentId = "A11"

// MARK: CAS
public let kcas = "CAS"


// MARK: Billing Address

public var kAddress_Verification: String {
    OALocalizedString("Address_Verification", comment: nil)
}
public var kAddress_Verification_Continue: String {
    OALocalizedString("Address_Verification_Continue", comment: nil)
}
public var kAddress_Verification_Description: String {
    OALocalizedString("Address_Verification_Description", comment: nil)
}
public var kAddress_Verification_No_Suggestion_Description: String {
    OALocalizedString("Address_Verification_No_Suggestion_Description", comment: nil)
}
public var kAddress_Verification_Entered_Address: String {
    OALocalizedString("Address_Verification_Entered_Address", comment: nil)
}
public var kAddress_Verification_Error: String {
    OALocalizedString("Address_Verification_Error", comment: nil)
}
public var kAddress_Verification_Suggested_Address: String {
    OALocalizedString("Address_Verification_Suggested_Address", comment: nil)
}
public var kThanksforyourpurchase: String {
    OALocalizedString("ManagePaidSubscription_Thanks_for_your_purchase", comment: nil)
}
public var kPaymentInformation: String {
    OALocalizedString("ManagePaidSubscription_Payment_Information", comment: nil)
}
public var kPaymentInfo: String {
    OALocalizedString("ManagePaidSubscription_Payment_Info", comment: nil)
}
public var kUpdatePaymentInfo: String {
    OALocalizedString("ManagePaidSubscription_Update_Payment_Info", comment: nil)
}
public var kContactSupport: String {
    OALocalizedString("ManagePaidSubscription_Contact_Support", comment: nil)
}
public var kOurApologiesPleaseTryAgain: String {
    OALocalizedString("ManagePaidSubscription_Our_apologies_please_try_again", comment: nil)
}
public var kPaymentCompleteTitle: String {
    OALocalizedString("ManagePaidSubscription_Payment_Complete_Title", comment: nil)
}
public var kPaymentSuccessDescription: String {
    OALocalizedString("ManagePaidSubscription_Payment_Success_Description", comment: nil)
}
public var kPaymentFailureDescription: String {
    OALocalizedString("ManagePaidSubscription_Payment_Failure_Description", comment: nil)
}
public var kTryAgain: String {
    OALocalizedString("ManagePaidSubscription_Try_Again", comment: nil)
}
public var kReviewYourOrderDisclaimer: String {
    OALocalizedString("ReviewYourOrder_disclaimer", comment: nil)
}

public var kBundlingReviewYourOrderDisclaimer: String {
    OALocalizedString("BundlingReviewYourOrder_disclaimer", comment: nil)
}
public var kVPNDetected: String {
    OALocalizedString("VPN_Detected", comment: nil)
}
public var kVPNMessage: String {
    OALocalizedString("VPN_Message", comment: nil)
}
public var kOutageMessage: String {
    OALocalizedString("Splash_outage", comment: nil)
}
public var kCompletePurchase: String {
    OALocalizedString("ManagePaidSubscription_Complete_Purchase", comment: nil)
}
public var kReviewYourOrder: String {
    OALocalizedString("ManagePaidSubscription_Review_Your_Order", comment: nil)
}
public var kUnableToCalculateTax: String {
    OALocalizedString("ManagePaidSubscription_Unable_to_Get_Calculate_Tax", comment: nil)
}
public var kTrialPackage: String {
    OALocalizedString("ManagePaidSubscription_Trial_Package", comment: nil)
}
public var kPaidServices: String {
    OALocalizedString("ManagePaidSubscription_Paid_Services", comment: nil)
}
public var kCreditCardNumber: String {
    OALocalizedString("PaymentPage_CreditCardNumber", comment: nil)
}
public var kExpireDate: String {
    OALocalizedString("PaymentPage_Exp_Date", comment: nil)
}
public var kInvalidCVV: String {
    OALocalizedString("PaymentPage_Invalid_CVV", comment: nil)
}

public let kReviewOrderViewCell = "reviewOrderViewCell"
public let kToyota = "toyota"
public let kLexus = "lexus"
public let kSubaru = "subaru"
public let kSubaruApp = "SubaruConnect"
public let kSXMPayment = "Payment Information"
public let kJavaScriptHandler = "jsHandler"
public let kCurrency = "USD"
public let kCountry = "US"
public let kDistanceUnitKm = "km"
public let kDistanceUnitMi = "mi"

// TODO: shall we delete these hardcoded values?
// Last Trip
let kTimeText = "7:45 AM - 8:10 AM"
let kDateText = "Thu, Mar 30, 2018"
let kMilesText = "3.5"
let kMinsText = "12"
// Driver Score
let kDriverScore = "85"
let kStartingAddress = "1234 Really Long Address, Plano, TX 75060"
let kEndingAddress = "TX-121, Allen, TX 75013"
let kAccelerationScore = "1"
let kHarshCorneringScore = "0"
let kHarshBreakingScore = "0"
// Vehicle Health Report
let kMonthText = "November 2018"

// MARK: - Subscription Bundling
let kSubscriptionBundle = "BUNDLE"

// MARK: - Public constants
// MARK: Splash
public var kAppUpdateGenericMessage: String {
    OALocalizedString("Splash_app_update_generic", comment: nil)
}
public var kUpdateNow: String {
    OALocalizedString("Splash_update_now", comment: nil)
}
public var kNewVersion: String {
    OALocalizedString("Splash_new_version", comment: nil)
}
public var kUpdate: String {
    OALocalizedString("Splash_update", comment: nil)
}
public var kLater: String {
    OALocalizedString("Splash_update_later", comment: nil)
}
public var kTakeLongerLoadingTime: String {
    OALocalizedString("Splash_taking_longer_loading_time", comment: nil)
}
public var kLoginFailedForRetryRefreshToken: String {
    OALocalizedString("Splash_failed_for_retry_refresh_token", comment: nil)
}
public var kLoginFailedForRetryRefreshTokenTitle: String {
    OALocalizedString("Splash_failed_for_retry_refresh_token_title", comment: nil)
}
// MARK: Login & Sign Up
public var kSignUp: String {
    OALocalizedString("Login_sign_up", comment: nil)
}
public var kDemoMode: String {
    OALocalizedString("Login_demo_mode", comment: nil)
}
public var kLogin: String {
    OALocalizedString("Login_login", comment: nil)
}
public var kLoginToUseBiometryText: String {
    OALocalizedString("Login_use_biometry_note", comment: nil)
}
public var KEmpty: String {
    return ""
}
public var kNoConnectionText: String {
    OALocalizedString("No_internet_connection", comment: nil)
}
public var kSuccess: String {
    OALocalizedString("Login_success", comment: nil)
}
public var kLoginSuccessful: String {
    OALocalizedString("Login_login_successful", comment: nil)
}
public var kError: String {
    OALocalizedString("Login_error", comment: nil)
}
public var kGoToSettings: String {
    OALocalizedString("Login_go_to_settings", comment: nil)
}
public var kSelectPreferLanguage: String {
    OALocalizedString("Login_select_prefer_language", comment: nil)
}
public var kSelectPreferLanguageTitle: String {
    OALocalizedString("Login_select_prefer_language_title", comment: nil)
}
public var kSelectPreferLanguageYes: String {
    OALocalizedString("Login_select_prefer_language_yes", comment: nil)
}
public var kSelectPreferLanguageNo: String {
    OALocalizedString("Login_select_prefer_language_no", comment: nil)
}
public var kChangeLanguage: String {
    OALocalizedString("Login_change_language", comment: nil)
}
public var kChangeRegion: String {
    OALocalizedString("Login_change_region", comment: nil)
}
public var kRegionUSA: String {
    OALocalizedString("Region_USA", comment: nil)
}
public var kRegionMX: String {
    OALocalizedString("Region_MX", comment: nil)
}
public var kRegionCanada: String {
    OALocalizedString("Region_Canada", comment: nil)
}
public var kRegionPR: String {
    OALocalizedString("Region_PR", comment: nil)
}
public var kLanguageEnglish: String {
    OALocalizedString("Language_English", comment: nil)
}
public var kLanguageFrench: String {
    OALocalizedString("Language_French", comment: nil)
}
public var kLanguageSpanish: String {
    OALocalizedString("Language_Spanish", comment: nil)
}
public var kTouchID: String {
    OALocalizedString("Login_touch_id", comment: nil)
}
public var kFaceID: String {
    OALocalizedString("Login_face_id", comment: nil)
}
public var kTitleKeepMeLoggedIn: String {
    OALocalizedString("Login_keep_me_logged_in", comment: nil)
}
public var kTitleOfflineMode: String {
    OALocalizedString("offline_mode_title", comment: nil)
}
public var kOfflineModeMandate: String {
    OALocalizedString("offline_mode_mandate", comment: nil)
}
public var kEnableOfflineAlertTitle: String {
    OALocalizedString("offline_enable_alert_title", comment: nil)
}
public var kEnableOfflineMessage: String {
    OALocalizedString("offline_enable_alert", comment: nil)
}
public var kGoOffline: String {
    OALocalizedString("offline_go_offline", comment: nil)
}
public var kOfflineModeNotAvailable: String {
    OALocalizedString("offline_not_available", comment: nil)
}
public var kOfflineAccessOnly: String {
    OALocalizedString("offline_access_only", comment: nil)
}
public var kOfflineAvailableTokens: String {
    OALocalizedString("offline_available_tokens", comment: nil)
}
public var kTitleLanguage: String {
    OALocalizedString("Login_language", comment: nil)
}
public var kTitleRegion: String {
    OALocalizedString("Login_region", comment: nil)
}
public var kLoginFailedWithKeepMeLoggedIn: String {
    OALocalizedString("Login_failed_keep_me_logged_in", comment: nil)
}
public var KLoginFailedStg: String {
    OALocalizedString("Login_failed_env_switch")
}
public var kLoginFailedWithBiometric: String {
    OALocalizedString("Login_failed_biometric", comment: nil)
}
public var kInvalidGrantError: String {
    OALocalizedString("Invalid_grant_error", comment: nil)
}
public var kInvalidGrantErrorForLogin: String {
    OALocalizedString("Invalid_grant_error_for_login", comment: nil)
}
// MARK: Biometric prompt

public var kSignInWithBiometrics: String {
    OALocalizedString("Signin_with_biometrics", comment: nil)
}
public var kSignInWithBiometricsFootnote: String {
    OALocalizedString("Signin_with_biometrics_footnote", comment: nil)
}

public var kServiceHistoryNoAppointments: String {
    OALocalizedString("ServiceHistoryNoAppointments", comment: nil)
}

// MARK: Vin Scan
public var kVinScanText: String {
    OALocalizedString("AddVehicle_vin_scan_text", comment: nil)
}
public var kVinScanBarcode: String {
    OALocalizedString("AddVehicle_vin_scan_barcode", comment: nil)
}
public var kVinScanCancel: String {
    OALocalizedString("AddVehicle_vin_scan_cancel", comment: nil)
}
public var kCommonPermissionMessage: String {
    OALocalizedString("Common_permission_msg", comment: nil)
}
public var kSettingCameraTitle: String {
    OALocalizedString("Setting_Camera_title", comment: nil)
}
public var kSettingPhotosTitle: String {
    OALocalizedString("Setting_Photos_title", comment: nil)
}
public var kLoading: String {
     OALocalizedString("Signin_Loading", comment: nil)
}
public var kToday: String {
    OALocalizedString("Today", comment: nil)
}
public var kTomorrow: String {
    OALocalizedString("Tomorrow", comment: nil)
}
public var kYesterday: String {
    OALocalizedString("Yesterday", comment: nil)
}
public var kAt: String {
    OALocalizedString("at", comment: nil)
}
// MARK: Add Vehicle
public var kAddYourVIN: String {
    OALocalizedString("AddVehicle_add_your_vehicle", comment: nil)
}
public var kYouCanFindThisOnTheDriverSideDashboard: String {
    OALocalizedString("AddVehicle_find_VIN", comment: nil)
}
public var kVehicleIdentificationNumber: String {
    OALocalizedString("Vehicle_identification_number", comment: nil)
}
public var kVIN: String {
    OALocalizedString("Common_VIN", comment: nil)
}
public var kAddVehicle: String {
    OALocalizedString("AddVehicle_add_vehicle", comment: nil)
}
public var kVINisNotValid: String {
    OALocalizedString("AddVehicle_error_VIN_invalid", comment: nil)
}
public var kVINisNotSupported: String {
    OALocalizedString("AddVehicle_error_VIN_notsupported", comment: nil)
}
public var kModel: String {
    OALocalizedString("AddVehicle_model", comment: nil)
}
public var kAddVehicleyear: String {
    OALocalizedString("AddVehicle_year", comment: nil)
}
public var kFinishSetup: String {
    OALocalizedString("AddVehicle_finish_setup", comment: nil)
}
public var kZipIsNotValid: String {
    OALocalizedString("AddVehicle_zip_not_valid", comment: nil)
}
public var kVehicleCapabilitiesText: String {
    OALocalizedString("AddVehicle_vehicle_capabilities_bold", comment: nil)
}
public var kVehicleCapabilities: String {
    OALocalizedString("AddVehicle_vehicle_capabilities", comment: nil)
}
public var kSaveChanges: String {
    OALocalizedString("AddVehicle_save_changes", comment: nil)
}
public var kAddVehicleNetwork3GTitle: String {
    OALocalizedString("AddVehicle_vehicle_Network3g_title", comment: nil)
}
public var kAddVehicleNetwork3GSubtitleToyota: String {
    OALocalizedString("AddVehicle_vehicle_Network3g_subTitle_Toyota", comment: nil)
}
public var kAddVehicleNetwork3GSubtitleLexus: String {
    OALocalizedString("AddVehicle_vehicle_Network3g_subTitle_Lexus", comment: nil)
}
public var kVehicleAlreadyAssociated: String {
    OALocalizedString("AddVehicle_already_added", comment: nil)
}
public var kWhereToFindYourVIN: String {
    OALocalizedString("AddVehicle_where_to_find_your_vin", comment: nil)
}
public var kWhereElseFindMyVINTitle: String {
    OALocalizedString("AddVehicle_where_else_find_my_vin_title", comment: nil)
}
public var kWhereElseFindMyVINDescription1: String {
    OALocalizedString("AddVehicle_where_else_find_my_vin_description1", comment: nil)
}
public var kWhereElseFindMyVINDescription2: String {
    OALocalizedString("AddVehicle_where_else_find_my_vin_description2", comment: nil)
}
public var kAgreement: String {
    OALocalizedString("AddVehicle_agreement", comment: nil)
}
public var kTermConditions: String {
    OALocalizedString("AddVehicle_terms_conditions", comment: nil)
}
public var kVehicleHasBeenAdded: String {
    OALocalizedString("AddVehicle_vehicle_has_been_added", comment: nil)
}
public var kAddVehiclePromptMessage: String {
    OALocalizedString("AddVehicle_prompt_description", comment: nil)
}
public var kAddVehiclePromptMessageLexus: String {
    OALocalizedString("AddVehicle_prompt_description_lexus", comment: nil)
}
public var kAddVehiclePromptMessageHighlighted: String {
    OALocalizedString("AddVehicle_prompt_description_highlight", comment: nil)
}
public var kAddVehiclePromptMessageHighlightedLexus: String {
    OALocalizedString("AddVehicle_prompt_description_highlight_lexus", comment: nil)
}
public var kAddVehicleFailToGetVehicleList: String {
    OALocalizedString("AddVehicle_fail_to_get_vehicle_list", comment: nil)
}
public var kAddVehicleVINCouldNotBeValidatedTitle: String {
    OALocalizedString("AddVehicle_VIN_cannot_be_validated_title", comment: nil)
}
public var kAddVehicleVINCouldNotBeValidatedToyotaErrorMessage: String {
    OALocalizedString("AddVehicle_VIN_cannot_be_validated_Toyota_error_message", comment: nil)
}
public var kAddVehicleVINCouldNotBeValidatedLexusErrorMessage: String {
    OALocalizedString("AddVehicle_VIN_cannot_be_validated_Lexus_error_message", comment: nil)
}
public var kAddVehicleVINCouldNotBeValidatedTransferMessage: String {
    OALocalizedString("AddVehicle_VIN_cannot_be_validated_transfer_message", comment: nil)
}
public var kAddVehicleVINTransferCancelToyotaMessage: String {
    OALocalizedString("AddVehicle_VIN_cannot_be_validated_cancel_toyota_message", comment: nil)
}
public var kAddVehicleVINTransferCancelLexusMessage: String {
    OALocalizedString("AddVehicle_VIN_cannot_be_validated_cancel_lexus_message", comment: nil)
}
public var kConfirmOwnershipTitle: String {
    OALocalizedString("AddVehicle_VIN_confirm_ownership_title", comment: nil)
}
public var kConfirmOwnershipDescription: String {
    OALocalizedString("AddVehicle_VIN_confirm_ownership_description", comment: nil)
}
public var kTransfer: String {
    OALocalizedString("AddVehicle_VIN_Transfer", comment: nil)
}
public var kVehicleNotAssociatedPartialTitle: String {
    OALocalizedString("AddVehicle_VIN_not_Associated_Partial_title", comment: nil)
}
public var kVehicleNotAssociatedPartialDescription: String {
    OALocalizedString("AddVehicle_VIN_not_Associated_Partial_description", comment: nil)
}
public var kVehicleNotAssociatedFailureTitle: String {
    OALocalizedString("AddVehicle_VIN_not_Associated_Failure_title", comment: nil)
}
public var kVehicleNotAssociatedTitleFailureDescription: String {
    OALocalizedString("AddVehicle_VIN_not_Associated_Failure_description", comment: nil)
}

public var kServiceTrialTitle: String {
    OALocalizedString("Subscription_available_services", comment: nil)
}
public var kServiceTrialTitleLexus: String {
    OALocalizedString("Subscription_available_services_lexus", comment: nil)
}
public var kServiceTrialFamilySharing: String {
    OALocalizedString("Subscription_available_services_family_sharing", comment: nil)
}
public var kServiceTrialFamilySharingSubtitle: String {
    OALocalizedString("Subscription_available_services_family_sharing_subtitle", comment: nil)
}
public var kServiceTrialInformationTitle: String {
    OALocalizedString("Subscription_available_services_information", comment: nil)
}
public var kServiceTrialInformationTitleLexus: String {
    OALocalizedString("Subscription_available_services_information_lexus", comment: nil)
}
public var kServiceTrialInformationTitleLexus21mm: String {
    OALocalizedString("Subscription_available_services_information_lexus_21mm", comment: nil)
}
public var kServiceTrialVehicleStinger: String {
    OALocalizedString("Subscription_vehicle_equipped_with_trial_package", comment: nil)
}
public var kServiceTrialVehicleStingerTCI: String {
    OALocalizedString("Subscription_vehicle_equipped_with_trial_package_tci", comment: nil)
}
public var kServiceTrialPackageTitle: String {
    OALocalizedString("Subscription_service_trial_package", comment: nil)
}
public var kVehicleAddedTitle: String {
    OALocalizedString("Subscription_added_success_title", comment: nil)
}
public var kVehicleAdded: String {
    OALocalizedString("Subscription_vehicle_added", comment: nil)
}
public var kVehicleAddedSubscriptionMessage: String {
    OALocalizedString("Subscription_vehicle_added_message", comment: nil)
}
public var kTrial: String {
    OALocalizedString("trial", comment: nil)
}
public var kPaid: String {
    OALocalizedString("paid", comment: nil)
}
public var kVehicleAddedBody: String {
    OALocalizedString("Subscription_added_success_message", comment: nil)
}
public var kVehicleAddedConnectedServices: String {
    OALocalizedString("Subscription_added_success_ConnectedServices", comment: nil)
}
public var kVehicleAddedLexusEnform: String {
    OALocalizedString("Subscription_added_success_LexusEnform", comment: nil)
}
public var kVehicleAddedLexusInterface: String {
    OALocalizedString("Subscription_added_success_LexusInterface", comment: nil)
}
public var kTitileManageSubscriptions: String {
    OALocalizedString("Subscription_manage_subscriptions", comment: nil)
}
public var kSubscriptionThirdParty: String {
    OALocalizedString("Subscription_third_party", comment: nil)
}
public var kSubscriptionMonthly: String {
    OALocalizedString("Subscription_monthly", comment: nil)
}
public var kTitleDestinationAssist: String {
    OALocalizedString("Subscription_destination_assist", comment: nil)
}
public var kTitleServicesRecordItems: String {
    OALocalizedString("Subscription_record_services", comment: nil)
}
public var kTitlePaymentMethod: String {
    OALocalizedString("Subscription_payment_method", comment: nil)
}
public var kTitleAddService: String {
    OALocalizedString("Subscription_add_service", comment: nil)
}
public var kSubscriptionAgreementAlertMessage: String {
    OALocalizedString("Subscription_agreement_alert_message", comment: nil)
}
public var kTitleSubtotal: String {
    OALocalizedString("Subscription_subtotal", comment: nil)
}
public var kTitleTax: String {
    OALocalizedString("Subscription_tax", comment: nil)
}
public var kTitleTotal: String {
    OALocalizedString("Subscription_total", comment: nil)
}
public var kTitleTaxSubtotal: String {
    OALocalizedString("Subscription_tax_subtotal", comment: nil)
}
public var kTitleRefundDue: String {
    OALocalizedString("Subscription_refund_due", comment: nil)
}
public var kTitleEstimatedTax: String {
    OALocalizedString("Subscription_estimated_tax", comment: nil)
}
public var kTitleDueAtPickup: String {
    OALocalizedString("Subscription_total_due_at_pickup", comment: nil)
}
public var kSubscriptionDescription: String {
    OALocalizedString("Subscription_description_message", comment: nil)
}
public var kTitleRenewSubscriptions: String {
    OALocalizedString("Subscription_renew_subscriptions", comment: nil)
}
public var kTitleSubScribeAgainSubscriptions: String {
    OALocalizedString("Subscription_subscribe_Again_subscriptions", comment: nil)
}
public var kTitleNoThanks: String {
    OALocalizedString("Subscription_no_thanks", comment: nil)
}
public var kTitleEnableTrialServices: String {
    OALocalizedString("Subscription_enable_trial_services", comment: nil)
}
public var kTitleTrialService: String {
    OALocalizedString("Subscription_trial_service", comment: nil)
}
public var kTitleYearly: String {
    OALocalizedString("Subscription_term_yearly", comment: nil)
}
public var kWaiveTrialPromptTitle: String {
    OALocalizedString("Subscription_waive_trial_note", comment: nil)
}
public var kWaiveTrialPromptTitleLexus: String {
    OALocalizedString("Subscription_waive_trial_note_lexus", comment: nil)
}
public var kWaiveTrialPromptTitleLexus21mm: String {
    OALocalizedString("Subscription_waive_trial_note_lexus_21mm", comment: nil)
}
public var kTitleCancelSubscription: String {
    OALocalizedString("Subscription_cancel_subscription", comment: nil)
}
public var kTitleCancelTrialPackages: String {
    OALocalizedString("Subscription_Cancel_Trial_Packages", comment: nil)
}
public var kSubscriptions: String {
    OALocalizedString("Subscription_subscriptions", comment: nil)
}
public var kSubscriptionActive: String {
    OALocalizedString("Subscription_Active", comment: nil)
}
public var kRemoteUser: String {
    OALocalizedString("Remote_remote_user", comment: nil)
}
public var kChangeExistingAuthorizedDriverAlert: String {
    OALocalizedString("Change_Existing_Authorized_Driver_Alert", comment: nil)
}
public var kNoAuthourizedRemoteDriver: String {
    OALocalizedString("No_Authourized_Remote_Driver", comment: nil)
}
public var kDefaultAuthorizedUserDescLabel: String {
    OALocalizedString("Default_Authorized_User_Description_Label", comment: nil)
}
public var kChangeRemoteDriverLabel: String {
    OALocalizedString("Change_Remote_Driver_Label", comment: nil)
}
public var kAuthourizedRemoteDriverLabel: String {
    OALocalizedString("Authorized_Remote_Driver_Label", comment: nil)
}
public var kAssignRemoteDriverFailMessage: String {
    OALocalizedString("Assign_Remote_Driver_Fail_Message", comment: nil)
}
public var kInviteAuthorizedDriverLabel: String {
    OALocalizedString("Invite_Authorized_Driver_Label", comment: nil)
}
public var kInviteDriverViaEmail: String {
    OALocalizedString("Invite_Driver_Via_Email", comment: nil)
}
public var kInviteDriverName: String {
    OALocalizedString("Invite_Driver_Name", comment: nil)
}
public var kWeCouldNotFindUserWithEmail: String {
    OALocalizedString("We_Could_Not_Find_User_With_Email", comment: nil)
}
public var kAssignMyselfLabel: String {
    OALocalizedString("Assign_Myself_Label", comment: nil)
}
public var kAssignMyselfAlertMessage: String {
    OALocalizedString("Assign_Myself_Alert_Message", comment: nil)
}
public var kInviteSameUserAlertMessage: String {
    OALocalizedString("Invite_same_user_alert_message", comment: nil)
}
public var kContinueToPurchase: String {
    OALocalizedString("Subscription_continue_to_purchase", comment: nil)
}
public var kTitlePaidServices: String {
    OALocalizedString("Subscription_paid_services", comment: nil)
}
public var kTitleVehicle: String {
    OALocalizedString("Subscription_vehicle", comment: nil)
}
public var kTitleAssistTab: String {
    OALocalizedString("Assist_Tab", comment: nil)
}
public var kTitleRentTab: String {
    OALocalizedString("Rent_Tab", comment: nil)
}
public var kTitleVehicleTab: String {
    OALocalizedString("Vehicle_Tab", comment: nil)
}
public var kTitleShopTab: String {
    OALocalizedString("Shop_Tab", comment: nil)
}
public var kTitleFindTab: String {
    OALocalizedString("Find_Tab", comment: nil)
}

public var kTitleMobility: String {
    "Mobility"
}
public var kSubscriptionFreeTrialInYear: String {
    OALocalizedString("Subscription_free_trial_in_year", comment: nil)
}
public var kSubscriptionFreeTrialInMonth: String {
    OALocalizedString("Subscription_free_trial_in_month", comment: nil)
}
public var kSubscriptionFreeTrialInDay: String {
    OALocalizedString("Subscription_free_trial_in_day", comment: nil)
}
public var kSubscriptionFreeTrialInYearAndMonth: String {
    OALocalizedString("Subscription_free_trial_in_year_and_month", comment: nil)
}
public var kSubscriptionFreeTrialInMonthAndDay: String {
    OALocalizedString("Subscription_free_trial_in_month_and_day", comment: nil)
}
public var kSubscriptionFreeTrialInYearAndDay: String {
    OALocalizedString("Subscription_free_trial_in_year_and_day", comment: nil)
}
public var kSubscriptionFreeTrialInYearMonthAndDay: String {
    OALocalizedString("Subscription_free_trial_in_Year_month_and_Day", comment: nil)
}
public var kSubscriptionTrialPackageInformationPrompt: String {
    OALocalizedString("Subscription_trial_package_information_prompt", comment: nil)
}
public var kSubscriptionTrialPackageInformationPromptLexus: String {
    OALocalizedString("Subscription_trial_package_information_prompt_lexus", comment: nil)
}
public var kSubscriptionTrialPackageInformationPromptSubaru: String {
    OALocalizedString("Subscription_trial_package_information_prompt_subaru", comment: nil)
}
public var kSubscriptionTrialPackageInformationPromptLexus21mm: String {
    OALocalizedString("Subscription_trial_package_information_prompt_lexus_21mm", comment: nil)
}
public var kSubscriptionTrialPackageInformation: String {
    OALocalizedString("Subscription_trial_package_information", comment: nil)
}
public var kSubscriptionTrialPackageInformationSubaru: String {
    OALocalizedString("Subscription_trial_package_information_subaru", comment: nil)
}
public var kSubscriptionTrialPackageInformationLexus: String {
    OALocalizedString("Subscription_trial_package_information_lexus", comment: nil)
}
public var kSubscriptionTrialPackageInformationLexus21mm: String {
    OALocalizedString("Subscription_trial_package_information_lexus_21mm", comment: nil)
}
public var kSubscriptionTrialPackageInformationHighlightToyota: String {
    OALocalizedString("Subscription_trial_package_information_highlight_toyota", comment: nil)
}
public var kSubscriptionTrialPackageInformationHighlightSubaru: String {
    OALocalizedString("Subscription_trial_package_information_highlight_subaru", comment: nil)
}
public var kSubscriptionTrialPackageInformationHighlightLexus: String {
    OALocalizedString("Subscription_trial_package_information_highlight_lexus", comment: nil)
}
public var kSubscriptionTrialPackageInformationHighlightLexusInterface: String {
    OALocalizedString("Subscription_trial_package_information_highlight_lexus_21mm", comment: nil)
}
public var kSubscriptionPackageInfo: String {
    OALocalizedString("Subscription_package_info", comment: nil)
}
public var kSubscriptionPackageInfoDetail: String {
    OALocalizedString("Subscription_package_info_detail", comment: nil)
}
public var kSubscriptionPackageInfoDetailLexus: String {
    OALocalizedString("Subscription_package_info_detail_lexus", comment: nil)
}
public var kSubscriptionPackageInfoDetailSubaru: String {
    OALocalizedString("Subscription_package_info_detail_subaru", comment: nil)
}
public var kSubscriptionAlmostDoneTitle: String {
    OALocalizedString("Subscription_almost_done_title", comment: nil)
}
public var kSubscriptionAlmostDoneDescription: String {
    OALocalizedString("Subscription_almost_done_description", comment: nil)
}
public var kSubscriptionMessageAlertRemovePaymentMethod: String {
    OALocalizedString("Subscription_message_alert_remove_payment_method", comment: nil)
}
public var kSubscriptionMessageAlertSuccessDeletedPaymentMethod: String {
    OALocalizedString("Subscription_message_alert_success_deleted_payment_method", comment: nil)
}
public var kSubscriptionCancelTrials: String {
    OALocalizedString("Subscription_cancel_trials", comment: nil)
}
public var kSubscriptionEnableAllTrials: String {
    OALocalizedString("Subscription_enable_all_trials", comment: nil)
}
public var kSubscriptionEnableTrial: String {
    OALocalizedString("Subscription_enable_trial", comment: nil)
}
public var kSubscriptionEnrollNow: String {
    OALocalizedString("Subscription_enroll_trail", comment: nil)
}
public var kSubscriptionEnableTrialTitle: String {
    OALocalizedString("Subscription_enable_trial_title", comment: nil)
}
public var kSubscriptionEnableSubscription: String {
    OALocalizedString("Subscription_enable_subscription", comment: nil)
}
public var kSubscriptionEnableTrialNote: String {
    OALocalizedString("Subscription_enable_trial_note", comment: nil)
}
public var kSubscriptionEnableTrialLearnMoreToyota: String {
    OALocalizedString("Subscription_trial_learn_more_toyota", comment: nil)
}
public var kSubscriptionEnableTrialLearnMoreSubaru: String {
    OALocalizedString("Subscription_trial_learn_more_subaru", comment: nil)
}
public var kSubscriptionEnableTrialLearnMoreLexus: String {
    OALocalizedString("Subscription_trial_learn_more_lexus", comment: nil)
}
public var kSubscriptionEnableTrialLearnMoreLexusInterface: String {
    OALocalizedString("Subscription_trial_learn_more_lexus_21mm", comment: nil)
}
public var kSubscriptionCancelQuestionNote: String {
    OALocalizedString("Subscription_cancel_question_note", comment: nil)
}
public var kSubscriptionLoseTitle: String {
    OALocalizedString("Subscription_lose_title", comment: nil)
}
public var kSubscriptionAvailableServices: String {
    OALocalizedString("Subscription_available", comment: nil)
}
public var kSubscriptionPaidServices: String {
    OALocalizedString("Subscription_paid_services", comment: nil)
}
public var kSubscriptionTrialServicesAvailable: String {
    OALocalizedString("Subscription_trial_services_available", comment: nil)
}
public var kSubscriptionSelectSubscriptionTitle: String {
    OALocalizedString("Subscription_select_subscription", comment: nil)
}
public var kSubscriptionSelectSubscriptionHeader: String {
    OALocalizedString("Subscription_select_subscription_header", comment: nil)
}
public var kSubscriptionSelectSubscriptionOnetimeHeader: String {
    OALocalizedString("Subscription_select_subscription_onetime_header", comment: nil)
}
public var kSubscriptionMonth: String {
    OALocalizedString("Subscription_month", comment: nil)
}
public var kSubscriptionYear: String {
    OALocalizedString("Subscription_year", comment: nil)
}
public var kSubscriptionStartDate: String {
    OALocalizedString("Subscription_start_date", comment: nil)
}
public var kSubscriptionPurchaseSubscription: String {
    OALocalizedString("Subscription_purchase_subscription", comment: nil)
}
public var kSubscriptionTitle: String {
    OALocalizedString("Subscription_title", comment: nil)
}
public var kSubscriptionCharges: String {
    OALocalizedString("Subscription_charges", comment: nil)
}
public var kSubscriptionOneTimePurchase: String {
    OALocalizedString("Subscription_one_time_purchase", comment: nil)
}
public var kSubscriptionSwitchToSubscription: String {
    OALocalizedString("Subscription_switch_to_subscription", comment: nil)
}
public var kSubscriptionPaymentDetails: String {
    OALocalizedString("Subscription_payment_method_details", comment: nil)
}
public var kSubscriptionPaymentMethodCard: String {
    OALocalizedString("Subscription_payment_method_card", comment: nil)
}
public var kSubscriptionPaymentMethodAccount: String {
    OALocalizedString("Subscription_payment_method_account", comment: nil)
}
public var kSubscriptionPayFrom: String {
    OALocalizedString("Subscription_pay_from", comment: nil)
}
public var kSubscriptionByVerizon: String {
    OALocalizedString("Subscription_By_Verizon", comment: nil)
}
public var kSubscriptionByATT: String {
    OALocalizedString("Subscription_By_ATT", comment: nil)
}
public var kSubscriptionCheckout: String {
    OALocalizedString("Subscription_Checkout", comment: nil)
}
public var kSubscriptionCheckoutDisclaimer: String {
    OALocalizedString("Subscription_Checkout_Disclaimer", comment: nil)
}
public var kSubscriptionTermsAndConditions: String {
    OALocalizedString("Subscription_Terms_And_Conditions", comment: nil)
}
public var kSubscriptionPriceDetails: String {
    OALocalizedString("Subscription_Price_Details", comment: nil)
}
public var kSubscriptionPurchaseSubmitted: String {
    OALocalizedString("Subscription_Purchase_Submitted", comment: nil)
}
public var kSubscriptionFailure: String {
    OALocalizedString("Subscription_Failure", comment: nil)
}
public var kSubscriptionFailed: String {
    OALocalizedString("Subscription_Failed", comment: nil)
}
public var kSubscriptionFailedMessage: String {
    OALocalizedString("Subscription_Failed_Message", comment: nil)
}
public var kSubscriptionThankYou: String {
    OALocalizedString("Subscription_Thank_You", comment: nil)
}
public var kSubscriptionConfirmCancellation: String {
    OALocalizedString("Subscription_confirm_cancellation", comment: nil)
}
public var kSubscriptionTotalRefund: String {
    OALocalizedString("Subscription_total_refund", comment: nil)
}
public var kSubscriptionCancelQuestion: String {
    OALocalizedString("Subscription_cancel_question", comment: nil)
}
public var kSubscriptionRefundAmount: String {
    OALocalizedString("Subscription_refund_amount", comment: nil)
}
public var kSubscriptionPaymentDefaultUnableToDeleteTitle: String {
    OALocalizedString("Subscription_Payment_Unable_to_Delete_title", comment: nil)
}
public var kSubscriptionPaymentDefaultUnableToDeleteDescription: String {
    OALocalizedString("Subscription_Payment_Unable_to_Delete_Description", comment: nil)
}
public var kSubscriptionExternalWifiVerizon: String {
    OALocalizedString("Subscription_exernal_wifi_verizon", comment: nil)
}
public var kSubscriptionExternalWifiATT: String {
    OALocalizedString("Subscription_exernal_wifi_at&t", comment: nil)
}
public var kSubscriptionExternalWifiVerizonInfo: String {
    OALocalizedString("Subscription_exernal_wifi_verizon_info", comment: nil)
}
public var kSubscriptionExternalWifiATTInfo: String {
    OALocalizedString("Subscription_exernal_wifi_at&t_info", comment: nil)
}
public var kSubscriptionAutoRenew: String {
    OALocalizedString("Subscription_auto_renew", comment: nil)
}
public var kSubscriptionNoServicesTitle: String {
    OALocalizedString("Subscription_No_Services_Found", comment: nil)
}
public var kSubscriptionNoServicesMessage: String {
    OALocalizedString("Subscription_No_Services_Message", comment: nil)
}
public var kSubscriptionWifiDecline: String {
    OALocalizedString("Subscription_Wifi_Decline", comment: nil)
}
public var kSubscriptionWifiDeclineTitle: String {
    OALocalizedString("Subscription_Wifi_Decline_Title", comment: nil)
}
public var kSubscriptionWifiDeclineMessage: String {
    OALocalizedString("Subscription_Wifi_Decline_Message", comment: nil)
}
public var kSubscriptionActivationMessage: String {
    OALocalizedString("Subscription_Activation_Message", comment: nil)
}
public var kSubscriptionActivationMessage21mm: String {
    OALocalizedString("Subscription_Activation_Message_21mm", comment: nil)
}
public var kSubscriptionTrailDeclinedTitle: String {
    OALocalizedString("Subscription_Trial_Services_Declined", comment: nil)
}
public var kSubscriptionTrialDeclinedMessage: String {
    OALocalizedString("Subscription_Trial_Services_Declined_Message", comment: nil)
}
public var kSubscriptionTrialDeclinedMessageCanada: String {
    OALocalizedString("Subscription_Trial_Services_Declined_Message_Canada", comment: nil)
}
public var kConnectedServicesTermsOfUseCanada: String {
    OALocalizedString("Connected_Services_Terms_of_Use_Canada", comment: nil)
}
public var kSubscriptionTrialNoteCanada: String {
    OALocalizedString("Subscription_enable_trial_note_Canada", comment: nil)
}
public var kSubscriptionTrialNotePuertoRico: String {
    OALocalizedString("Subscription_enable_trial_note_PuertoRico", comment: nil)
}
public var kSubscriptionBuyDeclinedMessage: String {
    OALocalizedString("Subscription_Buy_Services_Declined_Message", comment: nil)
}

public var kSubscriptionWaiveFooterMessage: String {
    OALocalizedString("Subscription_Waive_Footer_Message", comment: nil)
}
public var kSubscriptionDataConsentDeclineTitle: String {
    OALocalizedString("Subscription_Data_Consent_Decline_Title", comment: nil)
}
public var kSubscriptionDataConsentDeclineSubtitle: String {
    OALocalizedString("Subscription_Data_Consent_Decline_Subtitle", comment: nil)
}

// MARK: Data Consents
public var kLegalAccepted: String {
    OALocalizedString("Legal_accepted", comment: nil)
}
public var kLegalDeclined: String {
    OALocalizedString("Legal_declined", comment: nil)
}
public var kLegalTermsAndPrivacyUse: String {
    OALocalizedString("Legal_privacy_terms_of_use", comment: nil)
}
public var kLegalTermsOfUse: String {
    OALocalizedString("Legal_terms_of_use", comment: nil)
}
public var kLegalPrivacyNotice: String {
    OALocalizedString("Legal_privacy_notice", comment: nil)
}

public var kDataConsentsAccountSettingsLegal: String {
    OALocalizedString("DataConsents_account_settings_legal", comment: nil)
}
public var kDataConsentsBodyServiceConnectTitle: String {
    OALocalizedString("DataConsents_service_connect_title", comment: nil)
}
public var kDataConsentsBodyServiceConnectToyotaDescription: String {
    OALocalizedString("DataConsents_service_connect_description_toyota", comment: nil)
}
public var kDataConsentsBodyServiceConnectLexusDescription: String {
    OALocalizedString("DataConsents_service_connect_description_lexus", comment: nil)
}
public var kDataConsentsBodyServiceConnectLexusDescription21mm: String {
    OALocalizedString("DataConsents_service_connect_description_lexus_21mm", comment: nil)
}
public var kDataConsentsBodyUBITitle: String {
    OALocalizedString("DataConsents_UBI_title", comment: nil)
}
public var kWaivePromptMessage: String {
    OALocalizedString("DataConsents_waive_note", comment: nil)
}
public var kWaivePromptMessageTCI: String {
    OALocalizedString("DataConsents_waive_note_tci", comment: nil)
}
public var kWaivePromptMessageTDPR: String {
    OALocalizedString("DataConsents_waive_note_tdpr", comment: nil)
}
public var kWaivePromptMessageLexus: String {
    OALocalizedString("DataConsents_waive_note_for_lexus", comment: nil)
}
public var kWaivePromptMessageLexus21mm: String {
    OALocalizedString("DataConsents_waive_note_for_lexus_21mm", comment: nil)
}
public var kTitleAgreeAndContinue: String {
    OALocalizedString("DataConsents_agree_and_continue", comment: nil)
}
public var kAccepted: String {
    OALocalizedString("DataConsents_accepted", comment: nil)
}
public var kDataConsentDetailOfServiceConnectAndCan300Title: String {
    OALocalizedString("DataConsents_detail_serviceConnect_can300_title", comment: nil)
}

public var kDataConsentDetailOfDealerContactTitle: String {
    OALocalizedString("DataConsents_detail_dealerContact_title", comment: nil)
}
public var kDisconnectRemoteVehicleAccessAccountSettings: String {
    OALocalizedString("Disconnect_remote_vehicle_access_account_settings", comment: nil)
}
public var kDataConsentDetailOfUBITitle: String {
    OALocalizedString("DataConsents_detail_ubi_title", comment: nil)
}
public var kDataConsentDetailOfWIFITitle: String {
    OALocalizedString("DataConsents_detail_wifi_title", comment: nil)
}
public var kDataConsentDetailOfUBINoThanksTitle: String {
    OALocalizedString("DataConsents_detail_ubi_no_thanks_title", comment: nil)
}
public var kDataConsentConnectedServices: String {
    OALocalizedString("DataConsents_connected_services", comment: nil)
}
public var kDataConsentsDetailOfLCFSTitle: String {
    OALocalizedString("DataConsents_detail_lcfs_title", comment: nil)
}
public var klcfsBannerDescriptionLearnMoreTitle: String {
    OALocalizedString("Lcfs_banner_detail_learnmore_title", comment: nil)
}
public var klcfsBannerDescriptionLearnMorebody: String {
    OALocalizedString("Lcfs_banner_detail_learnmore_body", comment: nil)
}

public var klcfsBannerDescriptionLearnMorebodyLexus: String {
    OALocalizedString("Lcfs_banner_detail_learnmore_body_lexus", comment: nil)
}
public var kDataConsentDetailOfLCFSTermsTitle: String {
    OALocalizedString("DataConsents_detail_lcfs_terms_title", comment: nil)
}
public var kWIFIConnectTitle: String {
    OALocalizedString("KWIFI_Connect_Title", comment: nil)
}
public var kWIFIConnectBody: String {
    OALocalizedString("KWIFI_Connect_Body", comment: nil)
}
public var kWIFIConnectATTDescription: String {
    OALocalizedString("KWIFI_Connect_ATT_Description", comment: nil)
}
public var kWIFIConnectATTTerms: String {
    OALocalizedString("KWIFI_Connect_AT&T_Terms", comment: nil)
}
public var kWIFIConnectATTPrivacy: String {
    OALocalizedString("KWIFI_Connect_AT&T_Privacy", comment: nil)
}
public var KWIFIConnectPrivacyPolicies: String {
    OALocalizedString("KWIFI_Connect_Privacy_Policies", comment: nil)
}
public var KWIFIConnectVerizonDescription: String {
    OALocalizedString("KWIFI_Connect_Verizon_Description", comment: nil)
}
public var KWIFIConnectVerizonTerms: String {
    OALocalizedString("KWIFI_Connect_Verizon_Terms", comment: nil)
}
public var KWIFIConnectVerizonPrivacy: String {
    OALocalizedString("KWIFI_Connect_Verizon_Privacy", comment: nil)
}
public var KWIFIConnectVerizonOwnPrivacy: String {
    OALocalizedString("KWIFI_Connect_Verizon_Own_Privacy", comment: nil)
}

public var KDataConsentsDetailWifiNothanksTitle: String {
    OALocalizedString("DataConsents_detail_wifi_no_thanks_title", comment: nil)
}
public var KDataConsentsDetailWifiNothanksTitleDescription: String {
    OALocalizedString("DataConsents_detail_wifi_no_thanks_description", comment: nil)
}
public var KDataConsentsDetailWifiNothanksTitleDescriptionVerizon: String {
    OALocalizedString("DataConsents_detail_wifi_no_thanks_description_verizon", comment: nil)
}

public var KCombinedDataConsentsheaderTitle: String {
    OALocalizedString("Combined_Data_Consents_header_Title", comment: nil)
}
public var KEmptyDataConsentMessageToyota: String {
    OALocalizedString("Empty_DataConsent_Message_Toyota", comment: nil)
}
public var KEmptyDataConsentMessageLexus: String {
    OALocalizedString("Empty_DataConsent_Message_Lexus", comment: nil)
}
public var KCombinedDataConsentsButtonTitle: String {
    OALocalizedString("Combined_Data_Consents_Button_Title", comment: nil)
}
public var KCombinedDataConsentsDateTitle: String {
    OALocalizedString("Combined_Data_Consents_Date_title", comment: nil)
}
public var KCombinedDataConsentsDateTitleDefaultToEnglish: String {
    OALocalizedString("Combined_Data_Consents_Date_title_DefaultToEnglish", comment: nil)
}
public var KLegalAcceptedDefaultToEnglish: String {
    OALocalizedString("Legal_accepted_DefaultToEnglish", comment: nil)
}
public var kLegalDeclinedDefaultToEnglish: String {
    OALocalizedString("Legal_declined_DefaultToEnglish", comment: nil)
}
public var kLegacyMasterConsentDeclineAlertMessage: String {
    OALocalizedString("Combined_Data_Consents_Legacy_MasterConsent_Decline_Message", comment: nil)
}
public var kAppLicenseAgreement: String {
    OALocalizedString("App_License_Agreement", comment: nil)
}

// MARK: Consents
public var kZipCode: String {
    OALocalizedString("Consents_zip_code", comment: nil)
}

// MARK: Dashboard & Remote Service
public var kOdometer: String {
    OALocalizedString("Dashboard_odometer", comment: nil)
}
public var kDistanceToEmpty: String {
    OALocalizedString("Dashboard_distance_to_empty", comment: nil)
}
public var kRemoteServices: String {
    OALocalizedString("Dashboard_remote_services", comment: nil)
}
public var kStartEngine: String {
    OALocalizedString("Dashboard_start_engine", comment: nil)
}
public var kStopEngine: String {
    OALocalizedString("Dashboard_stop_engine", comment: nil)
}
public var kLock: String {
    OALocalizedString("Dashboard_lock", comment: nil)
}
public var kUnlock: String {
    OALocalizedString("Dashboard_unlock", comment: nil)
}
public var kHazards: String {
    OALocalizedString("Dashboard_hazards", comment: nil)
}
public var kYourVehicleStartedandWillShutOffin10minutes: String {
    OALocalizedString("Dashboard_vehicle_started", comment: nil)
}
public var kYourVehicleisNowLocked: String {
    OALocalizedString("Dashboard_vehicle_lock", comment: nil)
}
public var kYourVehicleisNowUnlocked: String {
    OALocalizedString("Dashboard_vehicle_unlock", comment: nil)
}
public var kYourHazardLightsHaveTurnedOn: String {
    OALocalizedString("Dashboard_hazard_on", comment: nil)
}
public var kYourHazardLightsHaveTurnedOff: String {
    OALocalizedString("Dashboard_hazard_off", comment: nil)
}

// MARK: - Remote Service for 21MM
public var kYourWindowsHaveClosed: String {
    OALocalizedString("Dashboard_windows_closed", comment: nil)
}
public var kYourHeadLampHaveTurnedOn: String {
    OALocalizedString("Dashboard_headLamp_on", comment: nil)
}
public var kYourHeadLampHaveTurnedOff: String {
    OALocalizedString("Dashboard_headLamp_off", comment: nil)
}
public var kHeadLamp: String {
    OALocalizedString("Dashboard_headLamp", comment: nil)
}
public var kHorn: String {
    OALocalizedString("Dashboard_horn", comment: nil)
}
public var kWindows: String {
    OALocalizedString("Dashboard_windows", comment: nil)
}
var kTrunkLock: String {
    OALocalizedString("Dashboard_trunk_lock", comment: nil)
}
var kYourTrunkIsNowLocked: String {
    OALocalizedString("Dashboard_trunk_now_locked", comment: nil)
}
var kTrunkUnlock: String {
    OALocalizedString("Dashboard_trunk_unlock", comment: nil)
}
var kYourTrunkIsNowUnlocked: String {
    OALocalizedString("Dashboard_trunk_now_unlocked", comment: nil)
}
var kBuzzer: String {
    OALocalizedString("Dashboard_buzzer", comment: nil)
}
var kYourBuzzerNowOn: String {
    OALocalizedString("Dashboard_buzzer_on", comment: nil)
}
public var kRemoteStartClimate: String {
    OALocalizedString("Remote_Start_Climate", comment: nil)
}
public var kRemoteSeatHeat: String {
    OALocalizedString("Remote_Seat_Heat", comment: nil)
}
public var kRemoteFrontDriver: String {
    OALocalizedString("Remote_Front_Driver", comment: nil)
}
public var kRemoteFrontPassenger: String {
    OALocalizedString("Remote_Front_Passenger", comment: nil)
}
public var kRemoteRearDriver: String {
    OALocalizedString("Remote_Rear_Driver", comment: nil)
}
public var kRemoteRearPassenger: String {
    OALocalizedString("Remote_Rear_Passenger", comment: nil)
}

public var kRemoteVehicleControls: String {
    OALocalizedString("Remote_Vehicle_Controls", comment: nil)
}
public var kRemoteVehicleControlsOpenTrunk: String {
    OALocalizedString("Remote_Vehicle_Controls_Open_Trunk", comment: nil)
}
public var kRemoteVehicleControlsWindows: String {
    OALocalizedString("Remote_Vehicle_Controls_Windows", comment: nil)
}
public var kRemoteVehicleControlsAllUp: String {
    OALocalizedString("Remote_Vehicle_Controls_All_Up", comment: nil)
}
public var kRemoteVehicleControlsAllDown: String {
    OALocalizedString("Remote_Vehicle_Controls_All_Down", comment: nil)
}
public var kRemoteVehicleControlsAllVent: String {
    OALocalizedString("Remote_Vehicle_Controls_All_Vent", comment: nil)
}
public var kRemoteVehicleControlsUp: String {
    OALocalizedString("Remote_Vehicle_Controls_Up", comment: nil)
}
public var kRemoteVehicleControlsDown: String {
    OALocalizedString("Remote_Vehicle_Controls_Down", comment: nil)
}
public var kRemoteVehicleControlsVent: String {
    OALocalizedString("Remote_Vehicle_Controls_Vent", comment: nil)
}
public var kRemoteVehicleControlsSeatVent: String {
    OALocalizedString("Remote_Vehicle_Controls_Seat_Vent", comment: nil)
}
public var kRemoteVehicleControlsDriverFront: String {
    OALocalizedString("Remote_Vehicle_Controls_Driver_Front", comment: nil)
}
public var kRemoteVehicleControlsPassengerFront: String {
    OALocalizedString("Remote_Vehicle_Controls_Passenger_Front", comment: nil)
}
public var kRemoteVehicleControlsDriverRear: String {
    OALocalizedString("Remote_Vehicle_Controls_Driver_Rear", comment: nil)
}
public var kRemoteVehicleControlsPassengerRear: String {
    OALocalizedString("Remote_Vehicle_Controls_Passenger_Rear", comment: nil)
}
public var kRemoteClimateControlsComfort: String {
    OALocalizedString("Remote_Climate_Controls_Comfort", comment: nil)
}
public var kRemoteClimateControlsComfortSteeringWheelHeat: String {
    OALocalizedString("Remote_Climate_Controls_Comfort_Steering_Wheel_Heat", comment: nil)
}
public var kRemoteClimateControlsComfortSteeringWheel: String {
    OALocalizedString("Remote_Climate_Controls_Comfort_Steering_Wheel", comment: nil)
}
public var kRemoteCommandsAdvancedControls: String {
    OALocalizedString("Remote_Commands_Advanced_Controls", comment: nil)
}

//gr86 Climate
public var kClimateFanSpeed: String {
    OALocalizedString("Climate_setting_fan_speed", comment: nil)
}
public var kClimateSetSpeed: String {
    OALocalizedString("Climate_setting_set_speed", comment: nil)
}
public var kClimateAirCirculation: String {
    OALocalizedString("Climate_setting_air_circulation", comment: nil)
}
public var kClimateAirInside: String {
    OALocalizedString("Climate_setting_air_inside", comment: nil)
}
public var kClimateAirOutside: String {
    OALocalizedString("Climate_setting_air_outside", comment: nil)
}
public var kClimateRearDefrost: String {
    OALocalizedString("Climate_setting_rear_defrost", comment: nil)
}
public var kAirflow: String {
    OALocalizedString("Climate_setting_air_flow", comment: nil)
}
public var kFaceFeet: String {
    OALocalizedString("Climate_setting_face_feet", comment: nil)
}
public var kFaceOnly: String {
    OALocalizedString("Climate_setting_face_only", comment: nil)
}
public var kFeetOnly: String {
    OALocalizedString("Climate_setting_feet_only", comment: nil)
}
public var kFaceDefroast: String {
    OALocalizedString("Climate_setting_face_defraost", comment: nil)
}

public var kRemoteTimerSetting: String {
    OALocalizedString("Remote_Timer_Setting", comment: nil)
}

public var kYourVehicleHealthReportisReadyToBeViewedDownloadToday: String {
    OALocalizedString("Dashboard_vehicle_health_report_ready_to_view", comment: nil)
}
public var kGetHealthReport: String {
    OALocalizedString("Dashboard_get_health_report", comment: nil)
}
public var kMessageContactingVehicle: String {
    OALocalizedString("Dashboard_contacting_vehicle", comment: nil)
}
var kAutofixPreparingToStart: String {
    OALocalizedString("Autofix_Preparing_To_Start", comment: nil)
}
var kAutofixRemoteCommandTitle: String {
    OALocalizedString("Autofix_remote_command_title", comment: nil)
}
var kAutofixRemoteCommandDescription: String {
    OALocalizedString("Autofix_remote_command_description", comment: nil)
}
var AutofixMoreInfo: String {
    OALocalizedString("Autofix_more_info", comment: nil)
}
var AutofixMoreInformation: String {
    OALocalizedString("Autofix_more_information", comment: nil)
}
var kAutofixRemoteCommandDetail: String {
    OALocalizedString("Autofix_remote_command_detail", comment: nil)
}
var kAutofixEngineCommandTitle: String {
    OALocalizedString("Autofix_engine_command_title", comment: nil)
}
var kAutofixEngineCommandDescription: String {
    OALocalizedString("Autofix_engine_command_description", comment: nil)
}
var kAutofixHoodTrunkHatchOpenDescription: String {
    OALocalizedString("Autofix_hood_command_description", comment: nil)
}
var kAutofixExceededTimeDescription: String {
    OALocalizedString("Autofix_exceeded_time_description", comment: nil)
}

public var kMessageLocking: String {
    OALocalizedString("Dashboard_locking", comment: nil)
}
public var kMessageUnlocking: String {
    OALocalizedString("Dashboard_unlocking", comment: nil)
}
public var kShowHiddenCards: String {
    OALocalizedString("Dashboard_show_hidden_cards", comment: nil)
}
public var kHideCards: String {
    OALocalizedString("Dashboard_hide_card_number", comment: nil)
}
public var kCards: String {
    OALocalizedString("dashboard_cards", comment: nil)
}
public var kCard: String {
    OALocalizedString("dashboard_card", comment: nil)
}
public var kCardHasBeenHidden: String {
    OALocalizedString("dashboard_card_has_been_hidden", comment: nil)
}
public var kNotificationsDisabledTitle: String {
    OALocalizedString("Dashboard_notification_disabled_title", comment: nil)
}
public var kNotificationsDisabledDescription: String {
    OALocalizedString("Dashboard_notification_disabled_note", comment: nil)
}
public var kTurnOnNotifications: String {
    OALocalizedString("Dashboard_turn_on_notifications", comment: nil)
}
public var kCardActions: String {
    OALocalizedString("dashboard_card_actions", comment: nil)
}
public var kCardActionsHideCardTitle: String {
    OALocalizedString("Dashboard_card_action_hide", comment: nil)
}
public var kCardActionsHideCardDescription: String {
    OALocalizedString("Dashboard_card_action_hide_description", comment: nil)
}
public var kCardActionsUnhideCardTitle: String {
    OALocalizedString("Dashboard_card_action_unhide", comment: nil)
}
public var kCardActionsUnhideCardDescription: String {
    OALocalizedString("Dashboard_card_action_unhide_description", comment: nil)
}
public var kCardActionsMoveCardTitle: String {
    OALocalizedString("Dashboard_card_action_move", comment: nil)
}
public var kCardActionsMoveCardDescription: String {
    OALocalizedString("Dashboard_card_action_move_description", comment: nil)
}
public var kContactingVehicleFailedText: String {
    OALocalizedString("Dashboard_contacting_failed", comment: nil)
}
public var kRemoteEngineStopRequestSuccessful: String {
    OALocalizedString("Dashboard_vehicle_stop", comment: nil)
}
public var kRemoteCommandTimeoutText: String {
    OALocalizedString("Dashboard_remote_timeout", comment: nil)
}
public var kRemoteCommandInviteRemoteUser: String {
    OALocalizedString("RemoteCommand_invite_remote_user", comment: nil)
}
public var kRemoteCommandSearchByEmailAddressOrPhone: String {
    OALocalizedString("RemoteCommand_search_by_email_address", comment: nil)
}
public var kCurrentVehicleLocation: String {
    OALocalizedString("Dashboard_Current_Vehicle_Location", comment: nil)
}
public var kVehicleLocation: String {
    OALocalizedString("Dashboard_Vehicle_Location", comment: nil)
}
public var kRefreshing: String {
    OALocalizedString("Dashboard_refreshing", comment: nil)
}
public var kGettingStatus: String {
    OALocalizedString("Dashboard_getting_status", comment: nil)
}
public var kLongPressPrompt: String {
    OALocalizedString("Dashboard_long_press_prompt", comment: nil)
}
public var kRemoteVehicleViewStatus: String {
    OALocalizedString("VehicleStatus_view_status", comment: nil)
}
public var kLoginContinueRegistration: String {
    OALocalizedString("Login_continue_registration", comment: nil)
}
public var kLoginHaveAccountTitleToyota: String {
    OALocalizedString("Login_have_account_title_txt", comment: nil)
}
public var kLoginHaveAccountTitleLexus: String {
    OALocalizedString("Login_have_account_title_lexus_txt", comment: nil)
}
public var kLoginHaveAccountDetailsToyota: String {
    OALocalizedString("Login_have_account_details_txt", comment: nil)
}
public var kLoginHaveAccountDetailsLexus: String {
    OALocalizedString("Login_have_account_details_lexus_txt", comment: nil)
}
public var kLoginHaveAccountDetailsLexus21mm: String {
    OALocalizedString("Login_have_account_details_lexus_txt_21mm", comment: nil)
}

// MARK: Remote Activation
var kRemoteSendActivation: String {
    OALocalizedString("RemoteActivation_send_activation", comment: nil)
}
var kRemoteNeedsActivation: String {
    OALocalizedString("RemoteActivation_needs_activate_title", comment: nil)
}
var kRemoteNeedsActivationPending: String {
    OALocalizedString("RemoteActivation_pending_title", comment: nil)
}
var kRemoteNeedsActivationFailed: String {
    OALocalizedString("RemoteActivation_failed_title", comment: nil)
}
var kRemoteNeedsActivationUnknown: String {
    OALocalizedString("RemoteActivation_unknown_title", comment: nil)
}
var kRemoteNeedsActivationExpired: String {
    OALocalizedString("RemoteActivation_expired_title", comment: nil)
}
var kRemoteNeedsActivationCanceled: String {
    OALocalizedString("RemoteActivation_canceled_title", comment: nil)
}
var kRemotePendingDescription: String {
    OALocalizedString("RemoteActivation_pending_note", comment: nil)
}
var kRemoteFailedDescription: String {
    OALocalizedString("RemoteActivation_failed_note", comment: nil)
}
var kRemoteUnknownDescription: String {
    OALocalizedString("RemoteActivation_unknown_note", comment: nil)
}
var kRemotePrimaryExpiredDescription: String {
    OALocalizedString("RemoteActivation_primary_expired_note", comment: nil)
}
var kRemotePrimaryCanceledDescription: String {
    OALocalizedString("RemoteActivation_primary_canceled_note", comment: nil)
}
var kRemoteRemoteExpiredDescription: String {
    OALocalizedString("RemoteActivation_remote_expired_note", comment: nil)
}
public var kRequireAuthenticationDescription: String {
    OALocalizedString("AccountSettings_require_authentication_description", comment: nil)
}
var kRemoteNeedsActivation_Lexus: String {
    OALocalizedString("RemoteActivation_needs_activate_title_lexus", comment: nil)
}
var kRemoteNeedsActivationDescription: String {
    OALocalizedString("RemoteActivation_needs_activate_note", comment: nil)
}
var kRemoteNeedsActivationDescription_Lexus: String {
    OALocalizedString("RemoteActivation_needs_activate_note_lexus", comment: nil)
}
var kRemoteNeedsActivationDescription_gr86: String {
    OALocalizedString("RemoteActivation_needs_activate_note_ng86", comment: nil)
}
var kHaveAnAuthCode: String {
    OALocalizedString("RemoteActivation_have_an_auth_code", comment: nil)
}
var kActivateRemoteFunctionality: String {
    OALocalizedString("RemoteActivation_active_remote_functionality", comment: nil)
}
var kRefreshRemoteFunctionality: String {
    OALocalizedString("RemoteActivation_refresh_remote_functionality", comment: nil)
}
var kReactivateRemote: String {
    OALocalizedString("RemoteActivation_reactivate", comment: nil)
}
var kContactPrimaryRemoteFunctionality: String {
    OALocalizedString("RemoteActivation_contact_Primary_remote_functionality", comment: nil)
}
var kContactSupportRemoteFunctionality: String {
    OALocalizedString("ManagePaidSubscription_Contact_Support", comment: nil)
}
var kRemoteActivation: String {
    OALocalizedString("RemoteActivation_remote_activation", comment: nil)
}
var kRemoteActivationDescription: String {
    OALocalizedString("RemoteActivation_activate_note", comment: nil)
}
var kRemoteDetailDescription: String {
    OALocalizedString("RemoteActivation_detail_description", comment: nil)
}
var krequestNewCodeButton: String {
    OALocalizedString("RemoteActivation_request_newcode", comment: nil)
}
var kRemoteActivationDescription_Lexus: String {
    OALocalizedString("RemoteActivation_activate_note_lexus", comment: nil)
}
public var kBiometricSettingsSubtitle: String {
    OALocalizedString("AccountSettings_biometric_settings_subtitle", comment: nil)
}
var kResendCodeDescription: String {
    OALocalizedString("RemoteActivation_resend_code_note", comment: nil)
}
var kResendActivationCode: String {
    OALocalizedString("RemoteActivation_resend_activation_code", comment: nil)
}
var kActivateRemote: String {
    OALocalizedString("RemoteActivation_active_remote", comment: nil)
}
var kResendActivationCodeSuccess: String {
    OALocalizedString("RemoteActivation_resend_success", comment: nil)
}
var kSOSButtonPositionPrompt: String {
    OALocalizedString("RemoteActivation_sos_position", comment: nil)
}
var kRemoteUserAuthorizedDriverInviteSent: String {
    OALocalizedString("RemoteUser_authorized_driver_invite_sent", comment: nil)
}
var kRemoteUserInviteSent: String {
    OALocalizedString("RemoteUser_invite_sent", comment: nil)
}
var kServiceProvider: String {
    OALocalizedString("Service_provider", comment: nil)
}
var kServiceDate: String {
    OALocalizedString("Service_date", comment: nil)
}
var kNotesItem: String {
    OALocalizedString("kNotes_item", comment: nil)
}
var kRemoteService_Svl_Note: String {
    OALocalizedString("RemoteActivation_svl_note", comment: nil)
}
var kRemoteService_Svl_Description_toyota: String {
    OALocalizedString("RemoteActivation_svl_description_toyota", comment: nil)
}
var kRemoteService_Svl_Description_lexus: String {
    OALocalizedString("RemoteActivation_svl_description_lexus", comment: nil)
}
var kRemoteService_Svl_Alert_title_toyota: String {
    OALocalizedString("RemoteActivation_svl_alert_title_toyota", comment: nil)
}
var kRemoteService_Svl_Alert_title_lexus: String {
    OALocalizedString("RemoteActivation_svl_alert_title_lexus", comment: nil)
}
var kRemoteService_Svl_Alert_Message: String {
    OALocalizedString("RemoteActivation_svl_alert_message", comment: nil)
}

// MARK: Account Settings
public var kChooseFromLibrary: String {
    OALocalizedString("AccountSettings_choose_from_library", comment: nil)
}
public var kRemovePhoto: String {
    OALocalizedString("AccountSettings_remove_photo", comment: nil)
}
public var kTakePhoto: String {
    OALocalizedString("AccountSettings_take_photo", comment: nil)
}
public var kMoveAndScale: String {
    OALocalizedString("AccountSettings_move_and_scale", comment: nil)
}
public var kAccountSettings: String {
    OALocalizedString("AccountSettings_header", comment: nil)
}
public var kAreYouSureToSignOut: String {
    OALocalizedString("AccountSettings_sign_out_note", comment: nil)
}
public var kAreYouSureToExitDemo: String {
    OALocalizedString("AccountSettings_exit_demo_note", comment: nil)
}
public var kSecuritySettings: String {
    OALocalizedString("AccountSettings_security_settings", comment: nil)
}
public var kRequireAuthentication: String {
    OALocalizedString("AccountSettings_require_authentication", comment: nil)
}
public var kImmediately: String {
    OALocalizedString("AccountSettings_immediately", comment: nil)
}
public var kOneMinute: String {
    OALocalizedString("AccountSettings_one_minute", comment: nil)
}
public var kFiveMinutes: String {
    OALocalizedString("AccountSettings_five_minutes", comment: nil)
}
public var kFifteenMinutes: String {
    OALocalizedString("AccountSettings_fifteen_minutes", comment: nil)
}
public var kOneHour: String {
    OALocalizedString("AccountSettings_one_hour", comment: nil)
}
public var kSixHours: String {
    OALocalizedString("AccountSettings_six_hours", comment: nil)
}
public var kTwelveHours: String {
    OALocalizedString("AccountSettings_twelve_hours", comment: nil)
}
public var kAfterOneMinute: String {
    OALocalizedString("AccountSettings_after_one_minute", comment: nil)
}
public var kAfterFiveMinutes: String {
    OALocalizedString("AccountSettings_after_five_minutes", comment: nil)
}
public var kAfterFifteenMinutes: String {
    OALocalizedString("AccountSettings_after_fifteen_minutes", comment: nil)
}
public var kAfterOneHour: String {
    OALocalizedString("AccountSettings_after_one_hour", comment: nil)
}
public var kAfterSixHours: String {
    OALocalizedString("AccountSettings_after_six_hours", comment: nil)
}
public var kAfterTwelveHours: String {
    OALocalizedString("AccountSettings_after_twelve_hours", comment: nil)
}
public var kBiometricSettings: String {
    OALocalizedString("AccountSettings_biometric_settings", comment: nil)
}
public var kEnableBiometricsTitle: String {
    OALocalizedString("AccountSettings_enable_biometrics_title", comment: nil)
}
public var kEnableBiometricsAlertTitle: String {
    OALocalizedString("AccountSettings_enable_biometrics_alert_title", comment: nil)
}
public var kRequireAuthenticationTitle: String {
    OALocalizedString("AccountSettings_require_authentication_title", comment: nil)
}
public var kAccountSettingsVehicleProfileTitle: String {
    OALocalizedString("AccountSettings_vehicle_profiles_title", comment: nil)
}
public var kAccountSettingsManageProfiles: String {
    OALocalizedString("AccountSettings_manage_saved_profiles", comment: nil)
}
public var kAccountSettingsVehicleProfileHeadTitle: String {
    OALocalizedString("AccountSettings_vehicle_profiles_head_title", comment: nil)
}
public var kAccountSettingsVehicleProfileSubTitle: String {
    OALocalizedString("AccountSettings_vehicle_profiles_subtitle", comment: nil)
}
public var kAccountSettingsVehicleProfileDescrpition: String {
    OALocalizedString("AccountSettings_vehicle_profiles_description", comment: nil)
}
public var kAccountSettingsVehicleProfileNoSavedTitle: String {
    OALocalizedString("AccountSettings_vehicle_profiles_no_saved_title", comment: nil)
}
public var kAccountSettingsVehicleProfileNoSavedDescription: String {
    OALocalizedString("AccountSettings_vehicle_profiles_no_saved_description", comment: nil)
}
public var kAccountSettingsRemoveVehicleProfilesButton: String {
    OALocalizedString("AccountSettings_remove_vehicle_profiles_button", comment: nil)
}
public var kAccountSettingsRemoveVehicleProfilesAlertTitle: String {
    OALocalizedString("AccountSettings_remove_vehicle_profiles_alert_title", comment: nil)
}
public var kAccountSettingsRemoveVehicleProfilesAlertMessage: String {
    OALocalizedString("AccountSettings_remove_vehicle_profiles_alert_message", comment: nil)
}
public var kAccountSettingsPersonalInfoTitle: String {
    OALocalizedString("AccountSettings_personalInfo_title", comment: nil)
}
public var kAccountSettingsProfileSubTitle: String {
    OALocalizedString("AccountSettings_personalInfo_subtitle", comment: nil)
}
public var kAccountSettings_manage_data: String {
    OALocalizedString("AccountSettings_manage_data", comment: nil)
}
public var kAuthenticate: String {
    OALocalizedString("AccountSettings_authenticate", comment: nil)
}
public var kEnableBiometricsMessage: String {
    OALocalizedString("AccountSettings_enable_biometrics_message", comment: nil)
}
public var kSignOut: String {
    OALocalizedString("AccountSettings_sign_out", comment: nil)
}
public var kSignOutErrorToast: String {
    OALocalizedString("AccountSettings_sign_out_error_toast", comment: nil)
}
public var kExitDemo: String {
    OALocalizedString("AccountSettings_exit_demo", comment: nil)
}
public var kSkipDemo: String {
    OALocalizedString("AccountSettings_skip_demo", comment: nil)
}
public var kPersonalInfo: String {
    OALocalizedString("AccountSettings_personal_info", comment: nil)
}
public var kLegalInfo: String {
    OALocalizedString("AccountSettings_legal_info", comment: nil)
}
public var kPersonalDetails: String {
    OALocalizedString("AccountSettings_personal_details", comment: nil)
}
public var kFirstName: String {
    OALocalizedString("AccountSettings_first_name", comment: nil)
}
public var kLastName: String {
    OALocalizedString("AccountSettings_last_name", comment: nil)
}
public var kEmailAddress: String {
    OALocalizedString("AccountSettings_email_address", comment: nil)
}
public var kPhoneNumber: String {
    OALocalizedString("AccountSettings_phone_number", comment: nil)
}
public var kPassword: String {
    OALocalizedString("password", comment: nil)
}
public var kNameCannotBeBlank: String {
    OALocalizedString("AccountSettings_name_cant_be_blank", comment: nil)
}
public var kProfileName: String {
    OALocalizedString("AccountSettings_profile_name", comment: nil)
}
public var kProfileNameInvalid: String {
    OALocalizedString("AccountSettings_profile_name_invalid", comment: nil)
}
public var kProfileNameDescription: String {
    OALocalizedString("AccountSettings_profile_name_description", comment: nil)
}
public var kProfileNameEmptyAlert: String {
    OALocalizedString("AccountSettings_profile_name_empty_alert", comment: nil)
}
public var kAddressCannotBeBlank: String {
    OALocalizedString("AccountSettings_address_cant_be_blank", comment: nil)
}

public var kCityCannotBeBlank: String {
    OALocalizedString("AccountSettings_city_cant_be_blank", comment: nil)
}

public var kNotValidZip: String {
    OALocalizedString("AccountSettings_not_valid_zip", comment: nil)
}
public var kNotValidPostalCode: String {
    OALocalizedString("AccountSettings_not_postal_code", comment: nil)
}
public var kPostalCode: String {
    OALocalizedString("PersonalInfoActivity_postal_code", comment: nil)
}
public var kPreferredLanguageUpdateError: String {
    OALocalizedString("AccountSettings_preferred_language_update_error", comment: nil)
}
public var kPreferredLanguageDescription: String {
    OALocalizedString("AccountSettings_preferred_language_description", comment: nil)
}
public var kPreferredLanguageConfirmation: String {
    OALocalizedString("AccountSettings_preferred_language_confirmation", comment: nil)
}

public var kAppFeedback: String {
    OALocalizedString("AppFeedback", comment: nil)
}
public var kSelectVehicle: String {
    OALocalizedString("AppFeedback_select_vehicle", comment: nil)
}
public var kSelectedVehicle: String {
    OALocalizedString("AppFeedback_selected_vehicle", comment: nil)
}
public var kAppFeedbackTitle: String {
    OALocalizedString("AppFeedback_title", comment: nil)
}
public var kAppFeedbackDescribe: String {
    OALocalizedString("AppFeedback_describe", comment: nil)
}
public var kAppFeedbackInclude: String {
    OALocalizedString("AppFeedback_include", comment: nil)
}
public var kAppFeedbackIncludeDescribe: String {
    OALocalizedString("AppFeedback_include_describe", comment: nil)
}
public var kAppFeedbackSubmitText: String {
    OALocalizedString("AppFeedback_submit", comment: nil)
}
public var kAppFeedbackSubmitLabel: String {
    OALocalizedString("AppFeedback_submitLabel", comment: nil)
}
public var kAppFeedbackSubmitDescription: String {
    OALocalizedString("AppFeedback_submitDescription", comment: nil)
}
public var kAppFeedbackDone: String {
    OALocalizedString("AppFeedback_done", comment: nil)
}
public var kAppFeedBackSurveyQuestion: String {
    OALocalizedString("AppFeedback_survey_question", comment: nil)
}
public var kAppFeedBackSurveyLikeText: String {
    OALocalizedString("AppFeedback_survey_like_text", comment: nil)
}
public var kAppFeedBackSurveyDisLikeText: String {
    OALocalizedString("AppFeedback_survey_dislike_text", comment: nil)
}
public var kAppFeedBackSurveySubmitFeedback: String {
    OALocalizedString("AppFeedback_survey_submit_feedback", comment: nil)
}
public var kProfileUpdateDescription: String {
    OALocalizedString("AccountSettings_profile_update_description", comment: nil)
}
public var kProfileUpdateVerificationDescription: String {
    OALocalizedString("AccountSettings_profile_update_verification_description", comment: nil)
}
public var kProfileUpdatePhoneTitle: String {
    OALocalizedString("ProfileUpdate_Phone_Title", comment: nil)
}
public var kProfileUpdateEmailTitle: String {
    OALocalizedString("ProfileUpdate_Email_Title", comment: nil)
}
public var kProfileUpdatePhoneError: String {
    OALocalizedString("ProfileUpdate_Phone_Error", comment: nil)
}
public var kProfileUpdateEmailError: String {
    OALocalizedString("ProfileUpdate_Email_Error", comment: nil)
}
public var kProfileUpdateButtonText: String {
    OALocalizedString("ProfileUpdate_Button_Text", comment: nil)
}
public var kProfileUpdateVerifyPhoneTitle: String {
    OALocalizedString("ProfileUpdate_Verify_Phone_Title", comment: nil)
}
public var kProfileUpdateVerifyEmailTitle: String {
    OALocalizedString("ProfileUpdate_Verify_Email_Title", comment: nil)
}
public var kProfileUpdateReceivedCode: String {
    OALocalizedString("ProfileUpdate_Received_Code", comment: nil)
}
public var kProfileUpdateNewCode: String {
    OALocalizedString("ProfileUpdate_New_Code", comment: nil)
}
public var kProfileUpdateVerify: String {
    OALocalizedString("ProfileUpdate_Verify", comment: nil)
}
public var kProfileUpdateTitle: String {
    OALocalizedString("ProfileUpdate_Title", comment: nil)
}
public var kProfileUpdateSuccess: String {
    OALocalizedString("ProfileUpdate_Success", comment: nil)
}
public var kProfileUpdateConfirmationHeader: String {
    OALocalizedString("ProfileUpdate_Confirmation_Header", comment: nil)
}
public var kHelpFeedback: String {
    OALocalizedString("Help_feedback", comment: nil)
}
public var kHelpFAQ: String {
    OALocalizedString("Help_app_faq", comment: nil)
}
public var kHelpVehicleSupport: String {
    OALocalizedString("Help_vehicle_support", comment: nil)
}
public var kHelpSelectVehicle: String {
    OALocalizedString("Help_select_vehicle", comment: nil)
}
public var kHelpNoVehicleInfo: String {
    OALocalizedString("Help_no_vehicle", comment: nil)
}
public var kHelpSubmitFeedback: String {
    OALocalizedString("Help_submit_feedback", comment: nil)
}
public var kHelpContactUs: String {
    OALocalizedString("Help_contact_us", comment: nil)
}
public var kHelpManagePreferences: String {
    OALocalizedString("Help_manage_preferences", comment: nil)
}
public var kHelpManagePreferencesSubtitle: String {
    OALocalizedString("Help_manage_preferences_subtitle", comment: nil)
}  
public var kHelpFAQSubtitleToyota: String {
    OALocalizedString("Help_app_faq_subtitle_toyota", comment: nil)
}
public var kHelpFAQSubtitleLexus: String {
    OALocalizedString("Help_app_faq_subtitle_lexus", comment: nil)
}
public var kHelpVehicleSupportSubtitle: String {
    OALocalizedString("Help_vehicle_support_subtitle", comment: nil)
}
public var kHelpSubmitFeedbackSubtitle: String {
    OALocalizedString("Help_submit_feedback_subtitle", comment: nil)
}
public var kHelpContactUsSubtitle: String {
    OALocalizedString("Help_contact_us_subtitle", comment: nil)
}
public var kHelpSubmitFeedbackTitle: String {
    OALocalizedString("Help_feedback_title", comment: nil)
}
public var kHelpSubmitFeedbackDesc: String {
    OALocalizedString("Help_feedback_subtitle", comment: nil)
}
public var kHelpSubmitFeedbackDescUS: String {
    OALocalizedString("Help_feedback_subtitle_US", comment: nil)
}
public var kHelpSubmitFeedbackCategory: String {
    OALocalizedString("Help_feedback_category", comment: nil)
}
public var kHelpSubmitFeedbackType: String {
    OALocalizedString("Help_feedback_type", comment: nil)
}
public var kHelpSubmitFeedbackSuccessMsg: String {
    OALocalizedString("Help_feedback_success", comment: nil)
}
public var kHelpSubmitFeedbackDescribe: String {
    OALocalizedString("Help_feedback_describe", comment: nil)
}
public var kFAQPopularTopics: String {
    OALocalizedString("Help_faq_Popular_Topics", comment: nil)
}
public var kFAQAdditionalTopics: String {
    OALocalizedString("Help_faq_Additional_Topics", comment: nil)
}
public var kVehicleRecalls: String {
    OALocalizedString("Vehicle_support_recalls", comment: nil)
}
public var kVehicleManualsWarranties: String {
    OALocalizedString("Vehicle_support_manuals_warranties", comment: nil)
}
public var kVehicleMaintenanceTimeline: String {
    OALocalizedString("Vehicle_support_maintenance_timeline", comment: nil)
}
public var kVehicleContactDealer: String {
    OALocalizedString("Vehicle_support_contact_dealer", comment: nil)
}
public var kVehicleFAQs: String {
    OALocalizedString("Vehicle_support_faqs", comment: nil)
}
public var kGenericErrorTitle: String {
    OALocalizedString("xcapp_error_title", comment: nil)
}
public var kGenericErrorSubTitle: String {
    OALocalizedString("xcapp_error_generic_message", comment: nil)
}

// MARK: Garage
public var kSelectAnotherVehicle: String {
    OALocalizedString("Garage_select_another_vehicle", comment: nil)
}
public var kSelectPreferredDealership: String {
    OALocalizedString("Garage_select_preferred_dealership", comment: nil)
}
public var kSelectDealer: String {
    OALocalizedString("Garage_select_dealer", comment: nil)
}

public var kTitleMyGarage: String {
    OALocalizedString("Garage_my_garage", comment: nil)
}
public var kVehicleDetails: String {
    OALocalizedString("Garage_vehicle_details", comment: nil)
}
public var kVehicleImageDisclaimer: String {
    OALocalizedString("Garage_vehicle_image_disclaimer_message", comment: nil)
}
public var kTitleNickName: String {
    OALocalizedString("Garage_nickname", comment: nil)
}
public var kNickName: String {
    OALocalizedString("GarageCommonNickname", comment: nil)
}
public var kUpdateNickName: String {
    OALocalizedString("Garage_update_nickname", comment: nil)
}
public var kCancel17CY: String {
    OALocalizedString("Garage_call_to_cancel", comment: nil)
}
public var kTitleDataConsents: String {
    OALocalizedString("Garage_data_consents", comment: nil)
}
public var kTitleTirePressure: String {
    OALocalizedString("Garage_tire_pressure", comment: nil)
}
public var kTitleMaintenance: String {
    OALocalizedString("Garage_maintenance", comment: nil)
}
public var kTitleFAQ: String {
    OALocalizedString("Garage_F_A_Q", comment: nil)
}
public var kShopGenuineParts: String {
    OALocalizedString("Garage_shop_genuine_parts", comment: nil)
}
public var kTitleGloveBox: String {
    OALocalizedString("Garage_glove_box", comment: nil)
}
public var kView: String {
    OALocalizedString("Dashboard_QRG_button", comment: nil)
}
public var kTitleServiceHistory: String {
    OALocalizedString("Garage_service_history", comment: nil)
}
public var kServiceHistoryAPIFailureError: String {
    OALocalizedString("Service_history_api_failure_error", comment: nil)
}
public var kTitleHowToVideos: String {
    OALocalizedString("Garage_how_to_videos", comment: nil)
}
public var kTitlePreferredDealer: String {
    OALocalizedString("Garage_preferred_dealer", comment: nil)
}
public var kTitlePreferredDealerSubtitle: String {
    OALocalizedString("Garage_preferred_dealer_subtitle", comment: nil)
}
public var kTitleManaulsWarranties: String {
    OALocalizedString("Garage_manuals_warranties", comment: nil)
}
public var kAreYouSureYouWantToRemoveTheVehicle: String {
    OALocalizedString("Garage_remove_vehicle_note", comment: nil)
}
public var kAreYouSureYouWantToRemoveYourVehicle: String {
    OALocalizedString("Garage_remove_vehicle_title", comment: nil)
}
public var kGarageRemoveVehiclePoint1: String {
    OALocalizedString("Garage_remove_vehicle_point1", comment: nil)
}
public var kGarageRemoveVehiclePoint2: String {
    OALocalizedString("Garage_remove_vehicle_point2", comment: nil)
}
public var kGarageRemoveVehiclePoint3: String {
    OALocalizedString("Garage_remove_vehicle_point3", comment: nil)
}
public var kGarageRemoveVehiclePoint4: String {
    OALocalizedString("Garage_remove_vehicle_point4", comment: nil)
}
public var kRemoveVehicleFirstDataTransmissionMessage: String {
    OALocalizedString("RemoveVehicleFirstDataTransmissionMessage", comment: nil)
}
public var kRemoveVehicleSecondDataTransmissionMessage: String {
    OALocalizedString("RemoveVehicleSecondDataTransmissionMessage", comment: nil)
}
public var kRemoveVehicleThirdDataTransmissionMessage: String {
    OALocalizedString("RemoveVehicleThirdDataTransmissionMessage", comment: nil)
}
public var kAreYouSureYouWantToCancelTheSubscription: String {
    OALocalizedString("Garage_cancel_subscription_note", comment: nil)
}
public var kAreYouSureYouWantToCancelTheSubscriptionLexus: String {
    OALocalizedString("Garage_cancel_subscription_note_lexus", comment: nil)
}
public var kAreYouSureYouWantToCancelTheSubscriptionLexus21mm: String {
    OALocalizedString("Garage_cancel_subscription_note_lexus_21mm", comment: nil)
}
public var kCancelTheSubscriptionTitle: String {
    OALocalizedString("Garage_cancel_subscription_title", comment: nil)
}
public var kCancelTheSubscriptionTitleLexus: String {
    OALocalizedString("Garage_cancel_subscription_title_lexus", comment: nil)
}
public var kCancelTheSubscriptionTitleLexus21mm: String {
    OALocalizedString("Garage_cancel_subscription_title_lexus_21mm", comment: nil)
}
public var kCancelSingleSubscriptionMessage: String {
    OALocalizedString("Garage_cancel_single_subscription_message", comment: nil)
}
public var kDefaultVehicle: String {
    OALocalizedString("GarageDefaultVehicle", comment: nil)
}
public var kSetAsDefaultConfirmation: String {
    OALocalizedString("Garage_set_as_default_confirmation", comment: nil)
}
public var kCancellationRequestInProgress: String {
    OALocalizedString("Cancellation_Request_In_Progress_note", comment: nil)
}
public var kTitleSuccessfullyRemoved: String {
    OALocalizedString("Garage_successfully_removed", comment: nil)
}
public var kActiveSubscriptionCount: String {
    OALocalizedString("Subscription_active_number", comment: nil)
}
public var kTirePressureLowNum: String {
    OALocalizedString("Garage_tire_pressure_low_number", comment: nil)
}
public var kLowTirePressure: String {
    OALocalizedString("Garage_low_tire_pressure", comment: nil)
}
public var kLowTirePressureWarningMessage: String {
    OALocalizedString("Garage_low_tire_pressure_warning_message", comment: nil)
}

// MARK: VA Settings
public var kVaSettings: String {
    return OALocalizedString("VASettings", comment: nil)
}
public var kVASettingsTitle: String {
    return OALocalizedString("VASettings_VA_Title", comment: nil)
}
public var kVASettingsDescription: String {
    return OALocalizedString("VASettings_VA_Description", comment: nil)
}
public var kVASettingsDescriptionRoutines: String {
    return OALocalizedString("VASettings_VA_Description_Routines", comment: nil)
}
public var kVASettingsDescriptionRequirements: String {
    return OALocalizedString("VASettings_VA_Description_Requirements", comment: nil)
}
public var kVASettingsDescriptionSubtitle: String {
    return OALocalizedString("VASettings_VA_Description_Subtitle", comment: nil)
}
public var kVASettingsPush: String {
    return OALocalizedString("VASettings_Push", comment: nil)
}
public var kVASettingsProductivity: String {
    return OALocalizedString("VASettings_Productivity", comment: nil)
}
public var kVASettingsProductivitySubtitle: String {
    return OALocalizedString("VASettings_Productivity_Subtitle", comment: nil)
}
public var kVASettingsMaintenanceAndFlue: String {
    return OALocalizedString("VASettings_MaintenanceAndFlue", comment: nil)
}
public var kVASettingsMaintenanceAndFlueSubtitle: String {
    return OALocalizedString("VASettings_MaintenanceAndFlue_Subtitle", comment: nil)
}
public var kVASettingsWeather: String {
    return OALocalizedString("VASettings_Weather", comment: nil)
}
public var kVASettingsWeatherSubtitle: String {
    return OALocalizedString("VASettings_Weather_Subtitle", comment: nil)
}
public var kVASettingsNavigation: String {
    return OALocalizedString("VASettings_Navigation", comment: nil)
}
public var kVASettingsNavigationSubtitle: String {
    return OALocalizedString("VASettings_Navigation_Subtitle", comment: nil)
}
public var kVASettingsFooterDescriptionSubtitle: String {
    return OALocalizedString("kVASettings_Footer_Description_Subtitle", comment: nil)
}
// MARK: - LinkedAccounts
public var kLinkedAccounts: String {
    return OALocalizedString("LinkedAccounts", comment: nil)
}
public var kLinkedAccountsApple: String {
    return OALocalizedString("LinkedAccounts_Apple", comment: nil)
}
public var kLinkedAccountsAmazon: String {
    return OALocalizedString("LinkedAccounts_Amazon", comment: nil)
}
public var kLinkedAccountsMusicTitle: String {
    return OALocalizedString("LinkedAccounts_Music_Title", comment: nil)
}
public var kLinkedAccountsMusicDescription: String {
    return OALocalizedString("LinkedAccounts_Music_Description", comment: nil)
}
public var kLinkedAccountsLinked: String {
    return OALocalizedString("LinkedAccounts_Linked", comment: nil)
}
public var kLinkedAccountsDefault: String {
    return OALocalizedString("LinkedAccounts_Default", comment: nil)
}
public var kLinkedAccountsLinks: String {
    return OALocalizedString("LinkedAccounts_Link", comment: nil)
}
public var kLinkedAccountsExplicit: String {
    return OALocalizedString("LinkedAccounts_Explicit", comment: nil)
}
public var kLinkedAccountsUnlinkAccount: String {
    return OALocalizedString("LinkedAccounts_UnlinkAccount", comment: nil)
}
public var kLinkedAccountsStreamingQuality: String {
    return OALocalizedString("LinkedAccounts_StreamingQuality", comment: nil)
}
public var kLinkedAccountsStreamingQualityLow: String {
    return OALocalizedString("LinkedAccounts_StreamingQualityLow", comment: nil)
}
public var kLinkedAccountsStreamingQualityMedium: String {
    return OALocalizedString("LinkedAccounts_StreamingQualityMedium", comment: nil)
}
public var kLinkedAccountsStreamingQualityHigh: String {
    return OALocalizedString("LinkedAccounts_StreamingQualityHigh", comment: nil)
}
public var kLinkedAccountsUnsavedAlertTitle: String {
    return OALocalizedString("LinkedAccounts_unsaved_alert_title", comment: nil)
}
public var kLinkedAccountsUnsavedAlertMessage: String {
    return OALocalizedString("LinkedAccounts_unsaved_alert_message", comment: nil)
}
public var kLinkedAccountCardTitle: String {
    OALocalizedString("Linked_account_card_title", comment: nil)
}
public var kLinkedAccountCardDescription: String {
    OALocalizedString("Linked_account_card_description", comment: nil)
}
public var kLinkedAccountDisclaimer: String {
    OALocalizedString("Linked_account_disclaimer", comment: nil)
}
public var kLinkedAccountRegionUnsupported: String {
    OALocalizedString("Linked_account_region_unsupported", comment: nil)
}
public var kLinkedAccountVehicleRegionUnsupported: String {
    OALocalizedString("Linked_account_vehicle_region_unsupported", comment: nil)
}
public var kLinkedAccountUnlinkConfirmationTitle: String {
    OALocalizedString("Linked_account_unlink_confirmation_title", comment: nil)
}
public var kLinkedAccountUnlinkConfirmationMessage: String {
    OALocalizedString("Linked_account_unlink_confirmation_message", comment: nil)
}
public var kLinkedAccountsDefaultMusicService: String {
    return OALocalizedString("LinkedAccounts_Default_MusicService", comment: nil)
}
public var kLinkedAccountsContinue: String {
    return OALocalizedString("Linked_account_continue", comment: nil)
}

// MARK: RemoteServicesExceptions
public let kFrontDriverDoorLockStatus = "frontDriverDoorLockStatus"
public let kFrontPassengerDoorLockStatus = "frontPassengerDoorLockStatus"
public let kRearDriverDoorLockStatus = "rearDriverDoorLockStatus"
public let kRearPassengerDoorLockStatus = "rearPassengerDoorLockStatus"
public let kFrontDriverDoorOpenStatus = "frontDriverDoorOpenStatus"
public let kFrontPassengerDoorOpenStatus = "frontPassengerDoorOpenStatus"
public let kRearDriverDoorOpenStatus = "rearDriverDoorOpenStatus"
public let kRearPassengerDoorOpenStatus = "rearPassengerDoorOpenStatus"
public let kFrontDriverDoorWindowStatus = "frontDriverDoorWindowStatus"
public let kFrontPassengerDoorWindowStatus = "frontPassengerDoorWindowStatus"
public let kRearDriverDoorWindowStatus = "rearDriverDoorWindowStatus"
public let kRearPassengerDoorWindowStatus = "rearPassengerDoorWindowStatus"
public let kRearHatchRearWindow = "rearHatchRearWindow"
public let kSunroof = "sunroof"
public let kMoonroof = "moonroof"
public let kTrunk = "trunk"
public let kRemoteEngineStartStop = "remoteEngineStartStop"
public let kDoorLockUnlock = "doorLockUnlock"
public let kVehicleFinder = "vehicleFinder"
public let kGuestDriver = "guestDriver"
public let kHazard = "hazard"
public let kRemoteServicesExceptionsYes = "Y"
public let kRemoteServicesExceptionsNo = "N"

// MARK: Vehicle Status

// Vehicle Status TypeName Lookup Contant
public let kVehicleStatusTypeNameHood = "Hood"
public let kVehicleStatusTypeNameConvertibleTop = "Convertible Top"
public let kVehicleStatusTypeNameSunroof = "Sunroof"
public let kVehicleStatusTypeNameMoonroof = "Moonroof"
public let kVehicleStatusTypeNameHatch = "Hatch"
public let kVehicleStatusTypeNameTrunk = "Trunk"
public let kVehicleStatusTypeNameDoor = "Door"
public let kVehicleStatusTypeNameDriverDoor = "Driver Door"
public let kVehicleStatusTypeNameRearDoor = "Rear Door"
public let kVehicleStatusTypeNamePassengerDoor = "Passenger Door"
public let kVehicleStatusTypeNameRearDriverDoor = "Rear Driver Door"
public let kVehicleStatusTypeNameRearPassengerDoor = "Rear Passenger Door"
public let kVehicleStatusTypeNameWindow = "Window"
public let kVehicleStatusTypeNameDriverWindow = "Driver Window"
public let kVehicleStatusTypeNamePassengerWindow = "Passenger Window"
public let kVehicleStatusTypeNameRearPassengerWindow = "Rear Passenger Window"
public let kVehicleStatusTypeNameRearDriverWindow = "Rear Driver Window"
public let kVehicleStatusTypeNameRearWindow = "Rear Window"
public let kVehicleStatusDataNameClosed = "Closed"
public let kVehicleStatusDataNameOpen = "Open"
public let kVehicleStatusDataNameLocked = "Locked"
public let kVehicleStatusDataNameUnlocked = "Unlocked"

public var kVehicleStatusOpen: String {
    OALocalizedString("VehicleStatus_open", comment: nil)
}
public var kVehicleStatusClosed: String {
    OALocalizedString("VehicleStatus_closed", comment: nil)
}
public var kVehicleStatusLocked: String {
    OALocalizedString("VehicleStatus_locked", comment: nil)
}
public var kVehicleStatusUnlocked: String {
    OALocalizedString("VehicleStatus_unlocked", comment: nil)
}
public var kTitleVehicleStatus: String {
    OALocalizedString("VehicleStatus_vehicle_status", comment: nil)
}
public var kVehicleStatusNoResultFound: String {
    OALocalizedString("VehicleStatus_no_result_found", comment: nil)
}
public var kTitlePassengerSide: String {
    OALocalizedString("VehicleStatus_passenger_side", comment: nil)
}
public var kTitleDriverSide: String {
    OALocalizedString("VehicleStatus_driver_side", comment: nil)
}
public var kTitleDoor: String {
    OALocalizedString("VehicleStatus_door", comment: nil)
}
public var kTitleRearDoor: String {
    OALocalizedString("VehicleStatus_rear_door", comment: nil)
}
public var kTitleWindow: String {
    OALocalizedString("VehicleStatus_window", comment: nil)
}
public var kTitleRearWindow: String {
    OALocalizedString("VehicleStatus_rear_window", comment: nil)
}
public var kTitleTrunk: String {
    OALocalizedString("VehicleStatus_trunk", comment: nil)
}
public var kTitleHatch: String {
    OALocalizedString("VehicleStatus_hatch", comment: nil)
}
public var kTitleHood: String {
    OALocalizedString("VehicleStatus_hood", comment: nil)
}
public var kTitleOther: String {
    OALocalizedString("VehicleStatus_other", comment: nil)
}
public var kTitleConvertibleTop: String {
    OALocalizedString("VehicleStatus_convertible_top", comment: nil)
}
public var kTitleSunroof: String {
    OALocalizedString("VehicleStatus_sunroof", comment: nil)
}
public var kTitleMoonroof: String {
    OALocalizedString("VehicleStatus_moonroof", comment: nil)
}
public var kVehicleStatusLastUpdated: String {
    OALocalizedString("VehicleStatus_last_updated", comment: nil)
}

// MARK: Billing Address
public var kBillingAddress: String {
    OALocalizedString("BillingAddress_billing_address", comment: nil)
}
public var kBillingHomeAddress: String {
    OALocalizedString("BillingAddress_billing_home_address", comment: nil)
}
public var kBillingDescription: String {
    OALocalizedString("BillingAddress_billing_description", comment: nil)
}
public var kSpecialCharactersNotAllowed: String {
    OALocalizedString("BillingAddress_no_special_characters", comment: nil)
}
public var kStreetAddress: String {
    OALocalizedString("BillingAddress_street_address", comment: nil)
}
public var kAptSuite: String {
    OALocalizedString("BillingAddress_apt_suite", comment: nil)
}
public var kCity: String {
    OALocalizedString("BillingAddress_city", comment: nil)
}
public var kBillingAddressCountry: String {
    OALocalizedString("BillingAddress_country", comment: nil)
}
public var kBillingZipCode: String {
    OALocalizedString("BillingAddress_zip_postal_code", comment: nil)
}
public var kState: String {
    OALocalizedString("BillingAddress_state", comment: nil)
}
public var kProvince: String {
    OALocalizedString("BillingAddress_province", comment: nil)
}
public var kPaymentPageDescription: String {
    OALocalizedString("PaymentPage_description", comment: nil)
}
public var kPaymentPageRememberCardDescription: String {
    OALocalizedString("PaymentPage_RememberCard_description", comment: nil)
}
public var kPaymentPageContinueToReview: String {
    OALocalizedString("PaymentPage_Continue_To_Review", comment: nil)
}
public var kPaymentPageNameOnCard: String {
    OALocalizedString("PaymentPage_Name_On_Card", comment: nil)
}
public var kManageSubscriptionPageDescriptionPaid: String {
    OALocalizedString("ManageSubscriptionPage_Description_Paid", comment: nil)
}
public var kManageSubscriptionPageAddPaidServicesDescription: String {
    OALocalizedString("ManageSubscriptionPage_AddPaidServices_Description", comment: nil)
}
public var kManageSubscriptionPageAddPaidServicesButtonLabel: String {
    OALocalizedString("ManageSubscriptionPage_AddPaidServices_ButtonLabel", comment: nil)
}
public var kManageSubscriptionPageNoSubscriptionsAvailable: String {
    OALocalizedString("ManageSubscriptionPage_No_Subscriptions_Available", comment: nil)
}
public var kManageSubscriptionForNonCV: String {
    OALocalizedString("ManageSubscriptionPage_For_NonCV", comment: nil)
}
public var kTrialSubscriptionPageNoSubscriptionsAvailable: String {
    OALocalizedString("TrialSubscriptionPage_No_Subscriptions_Available", comment: nil)
}
public var kTrialSubscriptionPageNoSubscriptionsPurchaseAvailable: String {
    OALocalizedString("TrialSubscriptionPage_No_Subscriptions_Available_Purchase", comment: nil)
}
public var kPaymentMethods: String {
    OALocalizedString("PaymentPage_paymentMethod_title", comment: nil)
}

public var kPaymentMethodsDescription: String {
    OALocalizedString("PaymentPage_paymentMethod_description", comment: nil)
}

public var kPaymentMethodCreditCard: String {
    OALocalizedString("PaymentPage_paymentMethod_credit_card", comment: nil)
}

public var kPaymentMethodBankAccount: String {
    OALocalizedString("PaymentPage_paymentMethod_bank_account", comment: nil)
}

public var kPaymentMethodAddCreditCard: String {
    OALocalizedString("PaymentPage_paymentMethod_add_credit_card", comment: nil)
}

public var kPaymentMethodAddBankAccount: String {
    OALocalizedString("PaymentPage_paymentMethod_add_bank_account", comment: nil)
}

public var kManageSubscriptionPageDuplicateAutoRenewOnMessage: String {
    OALocalizedString("ManageSubscriptionPage_duplicate_autorenewOn_message", comment: nil)
}
public var kManageSubscriptionPageDuplicateAutoRenewOffMessage: String {
    OALocalizedString("ManageSubscriptionPage_duplicate_autorenewOff_message", comment: nil)
}
// MARK: Guest Profile
public var kGuestDriverSettings: String {
    OALocalizedString("GuestProfile_guest_driver_settings", comment: nil)
}
public var kGuestDriverAccess: String {
    OALocalizedString("GuestProfile_guest_driver_access", comment: nil)
}
public var kGuestProfileName: String {
    OALocalizedString("GuestProfile_profile_name", comment: nil)
}
public var kEnable: String {
    OALocalizedString("GuestProfile_enable", comment: nil)
}
public var kEnableSpeedLimit: String {
    OALocalizedString("GuestProfile_enable_speed_limit", comment: nil)
}
public var kSetMaxSpeedLimit: String {
    OALocalizedString("GuestProfile_set_max_speed_limit", comment: nil)
}
public var kMaxSpeedLimit: String {
    OALocalizedString("GuestProfile_max_speed_limit", comment: nil)
}
public var kSpeedLimit: String {
    OALocalizedString("GuestProfile_speed_limit", comment: nil)
}
public var kSetLimit: String {
    OALocalizedString("GuestProfile_set_limit", comment: nil)
}
public var kEnableCurfew: String {
    OALocalizedString("GuestProfile_enable_curfew", comment: nil)
}
public var kSetStartAndEndTime: String {
    OALocalizedString("GuestProfile_set_start_and_end_time", comment: nil)
}
public var kStartTime: String {
    OALocalizedString("GuestProfile_start_time", comment: nil)
}
public var kEndTime: String {
    OALocalizedString("GuestProfile_end_time", comment: nil)
}
public var kDaysOfTheWeek: String {
    OALocalizedString("GuestProfile_days_of_the_week", comment: nil)
}
public var kSetCurfew: String {
    OALocalizedString("GuestProfile_set_curfew", comment: nil)
}
public var kEnableTotalMilesLimit: String {
    OALocalizedString("GuestProfile_enable_total_miles_limit", comment: nil)
}
public var kMaxMilesAndResetTime: String {
    OALocalizedString("GuestProfile_max_miles_and_reset_time", comment: nil)
}
public var kMaxMiles: String {
    OALocalizedString("GuestProfile_max_miles", comment: nil)
}
public var kReseTime: String {
    OALocalizedString("GuestProfile_reset_time", comment: nil)
}
public var kWhenResetTimeisSetYourTotalMilesLimitWillResetAtThisTime: String {
    OALocalizedString("GuestProfile_total_mile_reset_time_note", comment: nil)
}
public var kTotalMilesLimit: String {
    OALocalizedString("GuestProfile_total_miles_limit", comment: nil)
}
public var kCurfew: String {
    OALocalizedString("GuestProfile_curfew", comment: nil)
}
public var kAreaLimit: String {
    OALocalizedString("GuestProfile_area_limit", comment: nil)
}
public var kTotalTimeLimit: String {
    OALocalizedString("GuestProfile_total_time_limit", comment: nil)
}
public var kIgnitionON: String {
    OALocalizedString("GuestProfile_ignition_on", comment: nil)
}
public var kEnableAreaLimit: String {
    OALocalizedString("GuestProfile_enable_area_limit", comment: nil)
}
public var kDriverScoreOptIn: String {
    OALocalizedString("Driver_Score_Opt_In", comment: nil)
}
public var kDriverScoreOptInCardDescription: String {
    OALocalizedString("Driver_Score_Opt_In_Card_Description", comment: nil)
}
public var kDriverScoreOptInPageDescription: String {
    OALocalizedString("Driver_Score_Opt_In_Page_Description", comment: nil)
}
public var kDriverScoreOPTOUT: String {
    OALocalizedString("Driver_Score_Opt_Out", comment: nil)
}
public var kDriverScoreOPTOUTHeader: String {
    OALocalizedString("Driver_Score_Opt_Out_Header", comment: nil)
}
public var kDriverScoreOPTOUTSubheader: String {
    OALocalizedString("Driver_Score_Opt_Out_Sub_Header", comment: nil)
}
public var kDriverScoreOptOutPageDescription: String {
    OALocalizedString("Driver_Score_Opt_Out_Page_Description", comment: nil)
}
public var kDriverScoreOPTOUTLearnmore: String {
    OALocalizedString("Driver_Score_Opt_Out_Learn_More", comment: nil)
}
public var kDriverScoreOPTOUTAboutTitle: String {
    OALocalizedString("Driver_Score_Opt_Out_About_Title", comment: nil)
}
public var kDriverScoreOPTOUTAboutHeader: String {
    OALocalizedString("Driver_Score_Opt_Out_About_Header", comment: nil)
}
public var kDriverScoreOPTOUTAboutDescriptionHeader: String {
    OALocalizedString("Driver_Score_Opt_Out_About_Description_Header", comment: nil)
}
public var kDriverScoreOPTOUTAboutDescriptionHeader_BoldContent1: String {
    OALocalizedString("Driver_Score_Opt_Out_About_Description_Header_BoldContent1", comment: nil)
}
public var kDriverScoreOPTOUTAboutDescriptionHeader_BoldContent2: String {
    OALocalizedString("Driver_Score_Opt_Out_About_Description_Header_BoldContent2", comment: nil)
}
public var kDriverScoreOptOutAboutFAQButton: String {
    OALocalizedString("Driver_Score_Opt_Out_About_FAQ_Button", comment: nil)
}
public var kSunday: String {
    OALocalizedString("GuestProfile_sunday", comment: nil)
}
public var kMonday: String {
    OALocalizedString("GuestProfile_monday", comment: nil)
}
public var kTuesday: String {
    OALocalizedString("GuestProfile_tuesday", comment: nil)
}
public var kWednesday: String {
    OALocalizedString("GuestProfile_wednesday", comment: nil)
}
public var kThursday: String {
    OALocalizedString("GuestProfile_thursday", comment: nil)
}
public var kFriday: String {
    OALocalizedString("GuestProfile_friday", comment: nil)
}
public var kSaturday: String {
    OALocalizedString("GuestProfile_saturday", comment: nil)
}
public var kEnterAddress: String {
    OALocalizedString("GuestProfile_enter_address", comment: nil)
}
public var kNoResultsFound: String {
    OALocalizedString("GuestProfile_no_results_found", comment: nil)
}
public var kEnableTotalTimeLimit: String {
    OALocalizedString("GuestProfile_enable_total_time_limit", comment: nil)
}
public var kMaxTimeAndResetTime: String {
    OALocalizedString("GuestProfile_max_time_and_reset_time", comment: nil)
}
public var kMaxTime: String {
    OALocalizedString("GuestProfile_max_time", comment: nil)
}
public var kResetPromtText: String {
    OALocalizedString("GuestProfile_total_time_reset_time_note", comment: nil)
}
public var kSetHours: String {
    OALocalizedString("GuestProfile_set_hours", comment: nil)
}

// MARK: Schedule Maintenance Appointment
public var kTitleAnyAdvisor: String {
    OALocalizedString("Appointment_any_advisor", comment: nil)
}
public var kServiceAppointmentTitle: String {
    OALocalizedString("Appointment_service_appointment", comment: nil)
}
public var kAppointmentManualServiceDetails: String {
    OALocalizedString("Appointment_manual_service_details", comment: nil)
}
public var kAppointmentManualServiceProviderErrorMessage: String {
    OALocalizedString("Appointment_manual_service_Provider_errormessage", comment: nil)
}
public var kAppointmentManualServiceNotesErrorMessage: String {
    OALocalizedString("Appointment_manual_notes_errormessage", comment: nil)
}
public var kAppointmentDetailsTitle: String {
    OALocalizedString("Appointment_appointment_details", comment: nil)
}
public var kRepairsServicesText: String {
    OALocalizedString("Subscription_services", comment: nil)
}
public var kRepairOrderTitle: String {
    OALocalizedString("Appointment_repair_order", comment: nil)
}
public var kDashboardViewServiceHistory: String {
    OALocalizedString("Dashboard_view_service_history", comment: nil)
}
public var KServiceHistoryAddRecord: String {
    OALocalizedString("Service_history_add_record", comment: nil)
}
public var kAppointmentNoServices: String {
    OALocalizedString("Appointment_no_previous_services", comment: nil)
}
public var kAppointmentErrorDescription: String {
    OALocalizedString("Appointment_error_description")
}
public var kAppointmentErrorBody: String {
    OALocalizedString("Appointment_error_body")
}
public var kDashboard_service_history_content: String {
    OALocalizedString("Dashboard_service_history_content", comment: nil)
}
public var kAppointmentCancelText: String {
    OALocalizedString("Appointment_cancel_appointment_note", comment: nil)
}
public var kCancelAppointment: String {
    OALocalizedString("Appointment_cancel_appointment", comment: nil)
}
public var kClearYourLocation: String {
    OALocalizedString("Appointment_clear_location", comment: nil)
}
public var kNoDealershipsNear: String {
    OALocalizedString("Appointment_no_dealerships_near", comment: nil)
}
public var kScheduleAppointmentDetailsTitleText: String {
    OALocalizedString("Appointment_schedule_appointment_title", comment: nil)
}
public var kScheduleAppointmentDetailsText: String {
    OALocalizedString("Appointment_schedule_appointment_description", comment: nil)
}
public var kRepairsOrServicesText: String {
    OALocalizedString("Appointment_repairs_or_services", comment: nil)
}
public var kViewAllServicesText: String {
    OALocalizedString("Appointment_view_all_services", comment: nil)
}
public var kSuggestedTimesText: String {
    OALocalizedString("Appointment_suggested_times", comment: nil)
}
public var kViewAllAvailableTimesText: String {
    OALocalizedString("Appointment_view_all_available_times", comment: nil)
}
public var kAppointmentAllAvailableDealershipTimeZoneText: String {
    OALocalizedString("Appointment_view_all_available_dealership_time_zone", comment: nil)
}
public var kServiceAdvisorText: String {
    OALocalizedString("Appointment_service_advisor", comment: nil)
}
public var kTransportationText: String {
    OALocalizedString("Appointment_transportation", comment: nil)
}
public var kContinueToConfirmationText: String {
    OALocalizedString("Appointment_continue_to_confirmation", comment: nil)
}
public var kScheduleMaintenanceText: String {
    OALocalizedString("Appointment_schedule_maintenance", comment: nil)
}
public var kScheduleText: String {
    OALocalizedString("Appointment_schedule_service", comment: nil)
}
public var kHistoryText: String {
    OALocalizedString("Appointment_service_history", comment: nil)
}
public var kConfirmScreen: String {
    OALocalizedString("Appointment_confirm_appointment", comment: nil)
}
public var kGeneralRepairServiceTypeTitle: String {
    OALocalizedString("Appointment_repair_service_type_general", comment: nil)
}
public var kDealerRepairServiceTypeTitle: String {
    OALocalizedString("Appointment_repair_service_type_dealer", comment: nil)
}
public var kFactoryRepairServiceTypeTitle: String {
    OALocalizedString("Appointment_repair_service_type_factory", comment: nil)
}
public var kCurrentLocation: String {
    OALocalizedString("Appointment_current_location", comment: nil)
}
public var kSearchPlaceholder: String {
    OALocalizedString("Appointment_search_placeholder", comment: nil)
}
public var kAppointmentTitle: String {
    OALocalizedString("Appointment_confirm_title", comment: nil)
}
public var kAppointmentDetail: String {
    OALocalizedString("Appointment_confirm_detail", comment: nil)
}
public var kChangeLocation: String {
    OALocalizedString("Appointment_change_location", comment: nil)
}
public var kDealerWebsite: String {
    OALocalizedString("Appointment_dealer_website", comment: nil)
}
public var kContinueToAppointmentDetails: String {
    OALocalizedString("Appointment_continue_to_appointment_details", comment: nil)
}
public var kCantFindWhatYouNeed: String {
    OALocalizedString("Appointment_cant_find_what_you_need", comment: nil)
}
public var kYourServiceAppointmentHasBeenScheduled: String {
    OALocalizedString("Appointment_scheduled_success", comment: nil)
}
public var kAConfirmationEmailHasBeenSent: String {
    OALocalizedString("Appointment_schedule_success_note", comment: nil)
}
public var kDateTime: String {
    OALocalizedString("Appointment_date_Time", comment: nil)
}
public var kCalendarTitle: String {
    OALocalizedString("Appointment_Calendar_title", comment: nil)
}
public var kCalendarSunday: String {
    OALocalizedString("Appointment_Calendar_sunday", comment: nil)
}
public var kCalendarMonday: String {
    OALocalizedString("Appointment_Calendar_monday", comment: nil)
}
public var kCalendarTuesday: String {
    OALocalizedString("Appointment_Calendar_tuesday", comment: nil)
}
public var kCalendarWednesday: String {
    OALocalizedString("Appointment_Calendar_wednesday", comment: nil)
}
public var kCalendarThursday: String {
    OALocalizedString("Appointment_Calendar_thursday", comment: nil)
}
public var kCalendarFriday: String {
    OALocalizedString("Appointment_Calendar_friday", comment: nil)
}
public var kCalendarSaturday: String {
    OALocalizedString("Appointment_Calendar_saturday", comment: nil)
}
public var kAppointmentNoSlotsAvailable: String {
    OALocalizedString("Appointment_No_Available_Slots", comment: nil)
}
public var kAppointmentNoSlotsAvailableSorry: String {
    OALocalizedString("Appointment_No_Available_Slots_Sorry", comment: nil)
}
public var kConfirmYourAppointment: String {
    OALocalizedString("Appointment_confirm_your_appointment", comment: nil)
}
public var kConfirmAppointmentPromptText: String {
    OALocalizedString("Appointment_confirm_your_appointment_detail", comment: nil)
}
public var kEstimatedTime: String {
    OALocalizedString("Appointment_estimated_time", comment: nil)
}
public var kAddToCalendar: String {
    OALocalizedString("Appointment_add_to_calendar", comment: nil)
}
public var kAddedToCalendar: String {
    OALocalizedString("Appointment_added_to_calendar", comment: nil)
}
public var kCalendarNotes: String {
    OALocalizedString("Appointment_calendar_notes", comment: nil)
}
public var kViewDetails: String {
    OALocalizedString("Appointment_view_details", comment: nil)
}
public var kBookAppointment: String {
    OALocalizedString("Appointment_book_appointment", comment: nil)
}
public var kEnterCurrentMileage: String {
    OALocalizedString("Appointment_enter_current_mileage", comment: nil)
}
public var kEnterPhoneNumber: String {
    OALocalizedString("Appointment_enter_phone_number", comment: nil)
}
public var kContinueToAppoinment: String {
    OALocalizedString("Appointment_continue_to_appointment", comment: nil)
}
public var kContactDealderForPricing: String {
    OALocalizedString("Appointment_contact_dealership_for_pricing", comment: nil)
}
public var kScheduleApppointment: String {
    OALocalizedString("Appointment_schedule_appointment", comment: nil)
}
public var kSelectTransportation: String {
        OALocalizedString("Appointment_select_transportation", comment: nil)
}
public var kServiceAppointmentCanceled: String {
    OALocalizedString("Appointment_service_appointment_canceled", comment: nil)
}
public var kServiceAppointmentSelect: String {
    OALocalizedString("Appointment_service_select", comment: nil)
}
public var kServiceAppointmentNoTimes: String {
    OALocalizedString("Appointment_service_no_times", comment: nil)
}
public var kAppointmentAddedToCalendar: String {
    OALocalizedString("Appointment_appointment_added_to_calendar", comment: nil)
}

// MARK: Maintenance Timeline

public var kMaintenanceTimeline: String {
    OALocalizedString("Maintenance_Timeline", comment: nil)
}
public var kMaintenanceTimelineAddMileage: String {
    OALocalizedString("Maintenance_Timeline_Add_Mileage", comment: nil)
}
public var kMaintenanceTimelineAddMileageError: String {
    OALocalizedString("Maintenance_Timeline_Add_Mileage_Error", comment: nil)
}
public var kMaintenanceTimelineLexusCardDescription: String {
    OALocalizedString("Maintenance_Timeline_Lexus_Card_Description", comment: nil)
}
public var kMaintenanceTimelineToyotaCardDescription: String {
    OALocalizedString("Maintenance_Timeline_Toyota_Card_Description", comment: nil)
}
public var kMaintenanceTimelineErrorDescription: String {
    OALocalizedString("Maintenance_Timeline_Error_Description", comment: nil)
}
public var kMaintenanceTimelineErrorTitle: String {
    OALocalizedString("Maintenance_Timeline_Error_Title", comment: nil)
}
public var kMaintenanceTimelineLastRecordedMileage: String {
    OALocalizedString("Maintenance_Timeline_Last_Recorded_Mileage", comment: nil)
}
public var kMaintenanceTimelineLexusNoServicesNote: String {
    OALocalizedString("Maintenance_Timeline_Lexus_No_Services_Note", comment: nil)
}
public var kMaintenanceTimelineNoServices: String {
    OALocalizedString("Maintenance_Timeline_No_Services", comment: nil)
}
public var kMaintenanceTimelineRecommendedService: String {
    OALocalizedString("Maintenance_Timeline_Recommended_Service", comment: nil)
}
public var kMaintenanceTimelineRecommendedServiceDetails: String {
    OALocalizedString("Maintenance_Timeline_Recommended_Service_Details", comment: nil)
}
public var kMaintenanceTimelineTitle: String {
    OALocalizedString("Maintenance_Timeline_Title", comment: nil)
}
public var kMaintenanceTimelineToyotaNoServicesNote: String {
    OALocalizedString("Maintenance_Timeline_Toyota_No_Services_Note", comment: nil)
}

// MARK: Service Campaign
public var kTitleSafetyRecall: String {
    OALocalizedString("ServiceCampaign_safety_recall", comment: nil)
}
public var kTitleSpecialServiceCampaign: String {
    OALocalizedString("ServiceCampaign_special_service_campaign", comment: nil)
}
public var kTitleLimitedServiceCampaign: String {
    OALocalizedString("ServiceCampaign_limited_service_campaign", comment: nil)
}
public var kCampaignAvailableUntil: String {
    OALocalizedString("ServiceCampaign_available_until", comment: nil)
}
public var kGetMoreInformation: String {
    OALocalizedString("ServiceCampaign_get_more_information", comment: nil)
}

// MARK: Last Trip
var kPreviousTripTitle: String {
    OALocalizedString("LastTrip_recent_trip", comment: nil)
}
var kMilesLabel: String {
    OALocalizedString("LastTrip_miles", comment: nil)
}
var kTripDisclLabel: String {
    OALocalizedString("LastTrip_disclaimer_message", comment: nil)
}
var kMinsLabel: String {
    OALocalizedString("LastTrip_mins", comment: nil)
}
var kAccelerationLabel: String {
    OALocalizedString("LastTrip_fast_acceleration", comment: nil)
}
var kHarshCorneringScoreLabel: String {
    OALocalizedString("LastTrip_harsh_cornering", comment: nil)
}
var kHarshBreakingScoreLabel: String {
    OALocalizedString("LastTrip_harsh_braking", comment: nil)
}
var kTripDetail: String {
    OALocalizedString("LastTrip_trip_detail", comment: nil)
}
var kTripNoLocation: String {
    OALocalizedString("LastTrip_no_location", comment: nil)
}
var kclearRecentTripHistory: String {
    OALocalizedString("LastTrip_Clear_Recent_Trip_History", comment: nil)
}
public var kAreYouSureYouWantToClearRecentTripHistory: String {
    OALocalizedString("LastTrip_Clear_Recent_Trip_History_Note", comment: nil)
}

// MARK: Driver Score
var kDriverScoreTiltle: String {
    OALocalizedString("DriverScore_driver_score", comment: nil)
}
var kNoDriverScoreHistoryInfoText: String {
    OALocalizedString("DriverScore_no_history", comment: nil)
}
var kDriverScoreTabName1Week: String {
    OALocalizedString("DriverScore_tab_name_1_week", comment: nil)
}
var kDriverScoreTabName1Month: String {
    OALocalizedString("DriverScore_tab_name_1_month", comment: nil)
}
var kDriverScoreTabName6Months: String {
    OALocalizedString("DriverScore_tab_name_6_months", comment: nil)
}
var kDriverScoreDisclosure: String {
    OALocalizedString("DriverScore_disclosure", comment: nil)
}
var kDriverScoreDisclosureText: String {
    OALocalizedString("DriverScore_disclosure_text", comment: nil)
}

// MARK: Vehicle Health Report
var kHealthReportTitle: String {
    OALocalizedString("VHR_vehicle_health_report", comment: nil)
}
var kVehicleDiagnosticData: String {
    OALocalizedString("VHR_vehicle_diagnostic_data", comment: nil)
}
var kRecallNoticesHeader: String {
    OALocalizedString("VHR_recall_notices", comment: nil)
}
var kSafetyRecallDetailNoData: String {
    OALocalizedString("VHR_safety_recalls_detail_no_data", comment: nil)
}

var kServiceCompaignHeader: String {
    OALocalizedString("VHR_service_campaigns", comment: nil)
}
public var kSubaruServiceCompaignHeader: String {
    OALocalizedString("VHR_subaru_service_campaigns", comment: nil)
}
var kNoOpenServiceCompaign: String {
    OALocalizedString("VHR_no_service_campaign", comment: nil)
}
var kNoOpenServiceCompaignLexus: String {
    OALocalizedString("VHR_no_service_campaign_lexus", comment: nil)
}
var kNoOpenVehicleAlerts: String {
    OALocalizedString("VHR_no_vehicle_alerts", comment: nil)
}
var kCurrentVehicleAlert: String {
    OALocalizedString("VehicleHealth_current_vehicle_alerts", comment: nil)
}
var kMaintenance: String {
    OALocalizedString("VHR_maintenance_status", comment: nil)
}
public var kSubaruMaintenanceStatusDescription: String {
    OALocalizedString("VHR_subaru_maintenance_status_description", comment: nil)
}
public var kSubaruServiceCampaingDescription: String {
    OALocalizedString("VHR_subaru_service_campaigns_description", comment: nil)
}
var VHRMaintenanceStatusDescription: String {
    OALocalizedString("VHR_maintenance_status_description", comment: nil)
}
var VHRMaintenanceStatusTitle: String {
    OALocalizedString("VHR_maintenance_status_title", comment: nil)
}

var kThankyouTitle: String {
    OALocalizedString("VHR_thank_you_title", comment: nil)
}
var kThankYouDetail: String {
    OALocalizedString("VHR_thank_you_detail", comment: nil)
}
public var kSubaruThankyouDetail: String {
    OALocalizedString("VHR_subaru_thank_you_detail", comment: nil)
}
var kThankYouDetailLexus: String {
    OALocalizedString("VHR_thank_you_detail_lexus", comment: nil)
}
var kDisclosure: String {
    OALocalizedString("VHR_disclosure", comment: nil)
}
var kDisclosureLexus: String {
    OALocalizedString("VHR_disclosure_lexus", comment: nil)
}
var kQuantityOfEngineOilTitle: String {
    OALocalizedString("VHR_quantity_of_engine_oil", comment: nil)
}
var kQuantityOfEngineOilDetailRequired: String {
    OALocalizedString("VHR_quantity_of_engine_oil_detail_required", comment: nil)
}
var kVHRHealthReportHistory: String {
    OALocalizedString("VHR_health_report_history")
}
var kVHRDiagnosticReportHistory: String {
    OALocalizedString("VHR_diagnostic_report_history")
}
var kVHRHealthDiagnosticAccess: String {
    OALocalizedString("VHR_health_and_diagnostic_access")
}
var kVHRNoReports: String {
    OALocalizedString("VHR_no_reports")
}
var kVHREmailReport: String {
    OALocalizedString("VHR_email_report")
}
var kVHREmailReportConsentTitle: String {
    OALocalizedString("VHR_email_report_consent_title")
}
var kVHREmailReportConsentDescription1: String {
    OALocalizedString("VHR_email_report_consent_description1")
}
var kVHREmailReportConsentDescription2: String {
    OALocalizedString("VHR_email_report_consent_description2")
}
var kVHREmailReportSuccess: String {
    OALocalizedString("VHR_email_report_success")
}
var kVHRHealthAnddiagnosticAccessHeader: String {
    OALocalizedString("VHR_health_and_diagnostic_access_header")
}
var kVHRNoAccessTitle: String {
    OALocalizedString("VHR_no_access_title")
}
var kVHRNoAccessDescription: String {
    OALocalizedString("VHR_no_access_description")
}
var kVHRAddServiceLocation: String {
    OALocalizedString("VHR_add_service_location")
}
var kVHRAddServiceLocationTitle: String {
    OALocalizedString("VHR_add_service_location_title")
}
var kVHRSearchServiceLocation: String {
    OALocalizedString("VHR_search_service_locations")
}
var kVHRNoServiceLocationTitle: String {
    OALocalizedString("VHR_no_service_location_title")
}
var kVHRNoServiceLocationDescription: String {
    OALocalizedString("VHR_no_service_location_description")
}
var kVHRGrantAccess: String {
    OALocalizedString("VHR_grant_access")
}
var kVHRGrantConsentDescription: String {
    OALocalizedString("VHR_grant_consent_description")
}
var kVHRGrantSuccess: String {
    OALocalizedString("VHR_grant_access_success")
}
var kVHRRevokeAccess: String {
    OALocalizedString("VHR_revoke_access")
}
var kVHRRevokeSuccess: String {
    OALocalizedString("VHR_revoke_access_success")
}
var KVHRSmartKeyBatteryTitle: String {
    OALocalizedString("VHR_smart_key_battery")
}

// MARK: Notification History
public var kNotifications: String {
    OALocalizedString("Notification_notifications", comment: nil)
}
public var kNoNotifications: String {
    OALocalizedString("Notification_no_notifications", comment: nil)
}
public var kNoNotificationsDetail: String {
    OALocalizedString("Notification_no_notifications_detail", comment: nil)
}
public var kActiveRecallNotices: String {
    OALocalizedString("Notification_recall_active_number", comment: nil)
}
public var kNotificationsAllVehicles: String {
    OALocalizedString("Notification_all_vehicles", comment: nil)
}
public var kNotificationsAllNotifications: String {
    OALocalizedString("Notification_all_notifications", comment: nil)
}

// MARK: Notification Settings
public var kNotificationSettings: String {
    OALocalizedString("Notification_notification_settings", comment: nil)
}
public var kNotificationSettingsAppointmentMessage: String {
    OALocalizedString("Notification_appointment_message", comment: nil)
}
public var kNotificationSettingsServiceCouponTitle: String {
    OALocalizedString("Notification_appointment_title", comment: nil)
}
public var kNotificationSettingsServiceCouponMessage: String {
    OALocalizedString("Notification_appointment_message", comment: nil)
}
public var kNotificationSettingsRowPushNotifications: String {
    OALocalizedString("Notification_push_notification", comment: nil)
}
public var kNotificationSettingsRowEmail: String {
    OALocalizedString("Notification_email", comment: nil)
}
public var kNotificationSettingsRowTextMessage: String {
    OALocalizedString("Notification_text_message", comment: nil)
}
public var kNotificationSettingsDisabledButton: String {
    OALocalizedString("Notification_push_notification_disabled", comment: nil)
}
public var kNotificationSettingsTrialPackage: String {
    OALocalizedString("Notification_trial_package", comment: nil)
}
public var kNotificationSettingsPaidServices: String {
    OALocalizedString("Subscription_paid_services", comment: nil)
}
public var kNotificationSettingsPaidServicesDescription: String {
    OALocalizedString("Subscription_paid_services_description", comment: nil)
}
public var kNotificationErrorAuthenticationFailed: String {
    OALocalizedString("Notification_authentication_failed", comment: nil)
}
public var kNotificationErrorDefault: String {
    OALocalizedString("Notification_default_error", comment: nil)
}

public var kNotificationSettingsRowAutomatedCall: String {
    OALocalizedString("Notification_automated_call", comment: nil)
}

// MARK: Biometry
var kBiometryNotAvailableReason: String {
    OALocalizedString("Biometry_not_available_note", comment: nil)
}
/// ****************  Touch ID  ****************** ///
var kTouchIdAuthenticationReason: String {
    OALocalizedString("Biometry_touchid_authenticate_reason", comment: nil)
}
var kTouchIdPasscodeAuthenticationReason: String {
    OALocalizedString("Biometry_touchid_failed_enter_passcode", comment: nil)
}
var kSetPasscodeToUseTouchID: String {
    OALocalizedString("Biometry_set_passcode_to_use_touchid", comment: nil)
}
var kNoFingerprintEnrolled: String {
    OALocalizedString("Biometry_touchid_not_enrolled", comment: nil)
}
var kDefaultTouchIDAuthenticationFailedReason: String {
    OALocalizedString("Biometry_touchid_failed_reason", comment: nil)
}
var kDefaultLoginToOneAppReason: String {
    OALocalizedString("Biometry_login_to_OneApp", comment: nil)
}
/// ****************  Face ID  ****************** ///
var kFaceIdAuthenticationReason: String {
    OALocalizedString("Biometry_faceid_authenticate_reason", comment: nil)
}
var kFaceIdPasscodeAuthenticationReason: String {
    OALocalizedString("Biometry_faceid_failed_enter_passcode", comment: nil)
}
var kSetPasscodeToUseFaceID: String {
    OALocalizedString("Biometry_set_passcode_to_use_faceid", comment: nil)
}
var kNoFaceIdentityEnrolled: String {
    OALocalizedString("Biometry_faceid_not_enrolled", comment: nil)
}
var kDefaultFaceIDAuthenticationFailedReason: String {
    OALocalizedString("Biometry_faceid_failed_reason", comment: nil)
}
// MARK: Trouble Code
var kErrorCode: String {
    OALocalizedString("TroubleCode_error_code", comment: nil)
}
var kDescription: String {
    OALocalizedString("Common_description", comment: nil)
}
var hVehicleErrors: String {
    OALocalizedString("TroubleCode_vehicle_errors", comment: nil)
}
var hVehicleError: String {
    OALocalizedString("TroubleCode_vehicle_error", comment: nil)
}
var hVehicle: String {
    OALocalizedString("TroubleCode_your_vehicle_has", comment: nil)
}
var hViewError: String {
    OALocalizedString("TroubleCode_view_vehicle_error", comment: nil)
}
var hViewErrors: String {
    OALocalizedString("TroubleCode_view_all_vehicle_errors", comment: nil)
}
var kChangingYourTires: String {
    OALocalizedString("TroubleCode_changing_your_tires", comment: nil)
}
var kUnderstandingTiresPressure: String {
    OALocalizedString("TroubleCode_understanding_tires_pressure", comment: nil)
}
var kLowEngineOilPressure: String {
    OALocalizedString("TroubleCode_low_engine_oil_pressure", comment: nil)
}

// MARK: UBI
public var kUBIConsentLearnMoreText: String {
    OALocalizedString("UBI_learn_more_detail", comment: nil)
}
public var kUBICardTitleText: String {
    OALocalizedString("UBI_card_title", comment: nil)
}
public var kUBICardSponserSubTitleText: String {
    OALocalizedString("UBI_card_sponser_subtitle", comment: nil)
}
public var kUBICardDetailText: String {
    OALocalizedString("UBI_card_detail", comment: nil)
}
public var kUBICardSignUpButtonText: String {
    OALocalizedString("UBI_share_vehicle_information", comment: nil)
}
public var kUBIShareVehicleInformationWithToyotaSuccessText: String {
    OALocalizedString("UBI_you_are_all_set", comment: nil)
}
public var kUBIOfferNotAvailableTitle: String {
    OALocalizedString("UBI_OfferNot_available_title", comment: nil)
}
public var kUBIOfferNotAvailableDescription: String {
    OALocalizedString("UBI_OfferNot_available_description", comment: nil)
}


// MARK: LCFS
public var kLCFSCardTitleText: String {
    OALocalizedString("LCFS_card_title", comment: nil)
}
public var kLCFSDescriptionButtonTitleText: String {
    OALocalizedString("LCFS_desc_button_title", comment: nil)
}
public var kLCFSProgramOverviewButtonTitle: String {
    OALocalizedString("LCFS_PO_button_title", comment: nil)
}
public var kLCFSCardDetailText: String {
    OALocalizedString("LCFS_card_detail", comment: nil)
}

public var kLexusLCFSCardDetailText: String {
    OALocalizedString("Lexus_LCFS_card_detail", comment: nil)
}
public var kLCFSDescDetailText: String {
    OALocalizedString("LCFS_desc_detail", comment: nil)
}

public var kLCFSCardSignUpButtonText: String {
    OALocalizedString("LCFS_share_vehicle_information", comment: nil)
}
public var kToyotaLCFSConsentSubHeadline: String {
    OALocalizedString("LCFS_card_detail_subtitle", comment: nil)
}
public var kToyotaLCFSDetailHeadline: String {
    OALocalizedString("LCFS_card_detail_title", comment: nil)
}
public var kLCFSAgreeContinueDescSuccessText: String {
    OALocalizedString("LCFS_you_are_all_set", comment: nil)
}
public var kLCFSDeepLinkAlertText: String {
    OALocalizedString("LCFS_deeplink_alert_text", comment: nil)
}

// MARK: GPS Offset
public var kProceed: String {
    OALocalizedString("Common_proceed", comment: nil)
}
public var kContactDealer: String {
    OALocalizedString("Contact_Dealer", comment: nil)
}

// MARK: Roadside Assistance
public var kRoadSideAssistanceTitle: String {
    OALocalizedString("Roadside_Assistance", comment: nil)
}
public var kRoadsideAssistanceSubHeader: String {
    OALocalizedString("Roadside_assistance_get_the_answers_to_all_of_your_questions", comment: nil)
}
public var kRoadsideAssistanceLearnMore: String {
    OALocalizedString("Common_learn_more", comment: nil)
}
public var kRoadsideAssistanceCallLexus: String {
    OALocalizedString("Roadside_Assistance_call_roadside_assistance_lexus", comment: nil)
}
public var kRoadsideAssistanceCall: String {
    OALocalizedString("Roadside_Assistance_call_roadside_assistance", comment: nil)
}
public var KRoadsideAssistanceDetails: String {
    OALocalizedString("Roadside_Assistance_details", comment: nil)
}
public var KRoadsideAssistance24Hour: String {
    OALocalizedString("Roadside_Assistance_24_hour")
}
public var KRoadsideAssistanceDetailPrep: String {
    OALocalizedString("Roadside_Assistance_detail_prep")
}
// MARK: Sirius XM
public var kSiriusXMButtonTitle: String {
    OALocalizedString("Common_learn_more", comment: nil)
}
public var kSiriusXMExpiredTitle: String {
    OALocalizedString("SXM_Expired_Title", comment: nil)
}
public var kSiriusXMRadioIDTitle: String {
    OALocalizedString("SXM_Radio_ID_Title", comment: nil)
}
public var kSiriusXMRadioPackageTitle: String {
    OALocalizedString("SXM_Radio_Package_Title", comment: nil)
}
public var kSiriusXMStatusTitle: String {
    OALocalizedString("SXM_Status_Title", comment: nil)
}
public var kSiriusXMTrialEndDateTitle: String {
    OALocalizedString("SXM_Trial_End_Date_Title", comment: nil)
}

// MARK: Vehicle Software

public var kVehicleSoftware: String {
    OALocalizedString("VehicleSoftware", comment: nil)
}
public var kVehicleSoftware_HistoryTitle: String {
    OALocalizedString("VehicleSoftware_HistoryTitle", comment: nil)
}
public var kVehicleSoftware_History_Installed_Time: String {
    OALocalizedString("VehicleSoftware_History_Installed_Time", comment: nil)
}
public var kVehicleSoftware_History_Released_Time: String {
    OALocalizedString("VehicleSoftware_History_Released_Time", comment: nil)
}
public var kVehicleSoftware_Installed: String {
    OALocalizedString("VehicleSoftware_Installed", comment: nil)
}
public var kVehicleSoftware_Instructions: String {
    OALocalizedString("VehicleSoftware_Instructions", comment: nil)
}
public var kVehicleSoftware_PreviousUpdate: String {
    OALocalizedString("VehicleSoftware_PreviousUpdate", comment: nil)
}
public var kVehicleSoftware_Status: String {
    OALocalizedString("VehicleSoftware_Status", comment: nil)
}
public var kVehicleSoftware_Status_Available: String {
    OALocalizedString("VehicleSoftware_Status_Available", comment: nil)
}
public var kVehicleSoftware_Status_Complete: String {
    OALocalizedString("VehicleSoftware_Status_Complete", comment: nil)
}
public var kVehicleSoftware_Status_Failure: String {
    OALocalizedString("VehicleSoftware_Status_Failure", comment: nil)
}
public var kVehicleSoftware_Status_Failure_Message: String {
    OALocalizedString("VehicleSoftware_Status_Failure_Message", comment: nil)
}
public var kVehicleSoftware_Status_Initiated: String {
    OALocalizedString("VehicleSoftware_Status_Initiated", comment: nil)
}
public var kVehicleSoftware_Status_Install_Time: String {
    OALocalizedString("VehicleSoftware_Status_Install_Time", comment: nil)
}
public var kVehicleSoftware_UpToDate: String {
    OALocalizedString("VehicleSoftware_UpToDate", comment: nil)
}
public var kVehicleSoftware_Update: String {
    OALocalizedString("VehicleSoftware_Update", comment: nil)
}
public var kVehicleSoftware_UpdateAvailable: String {
    OALocalizedString("VehicleSoftware_UpdateAvailable", comment: nil)
}
public var kVehicleSoftware_Version: String {
    OALocalizedString("VehicleSoftware_Version", comment: nil)
}
public var kVehicleSoftware_Version_Number: String {
    OALocalizedString("VehicleSoftware_Version_Number", comment: nil)
}
public var kVehicleSoftware_ViewDetails: String {
    OALocalizedString("VehicleSoftware_ViewDetails", comment: nil)
}
public var kVehicleSoftware_ViewUpdate: String {
    OALocalizedString("VehicleSoftware_ViewUpdate", comment: nil)
}
public var kVehicleSoftware_ViewHistory: String {
    OALocalizedString("VehicleSoftware_ViewHistory", comment: nil)
}
public var kVehicleSoftware_WhatsNew: String {
    OALocalizedString("VehicleSoftware_WhatsNew", comment: nil)
}
public var kVehicleSoftware_Working_Time: String {
    OALocalizedString("VehicleSoftware_Working_Time", comment: nil)
}

// MARK: Dynamic Naviagtion
public var kActivation_Number: String {
    OALocalizedString("Activation_Number", comment: nil)
}
public var kActivation_Number_Invalid: String {
    OALocalizedString("Activation_Number_Invalid", comment: nil)
}
public var kActivation_Number_Mismatch: String {
    OALocalizedString("Activation_Number_Mismatch", comment: nil)
}
public var kBegin_Install: String {
    OALocalizedString("Begin_Install", comment: nil)
}
public var kCancelling_Dynamic_Navigation: String {
    OALocalizedString("Cancelling_Dynamic_Navigation", comment: nil)
}
public var kCancelling_Dynamic_Navigation_Description: String {
    #if LEXUS
    return OALocalizedString("Cancelling_Dynamic_Navigation_Lexus_Description", comment: nil)
    #else
    return OALocalizedString("Cancelling_Dynamic_Navigation_Description", comment: nil)
    #endif
}
public var kCheck_Update_Process_Duration_Info: String {
    OALocalizedString("Check_Update_Process_Duration_Info", comment: nil)
}
public var kContact_Us: String {
    OALocalizedString("Contact_Us", comment: nil)
}
public var kDynamic_Navigation: String {
    OALocalizedString("Dynamic_Navigation", comment: nil)
}
public var kDynamic_Naviagtion_Offers: String {
    OALocalizedString("Dynamic_Naviagtion_Offers", comment: nil)
}
public var kDynamic_Navigation_Check_Expiry_Description: String {
    OALocalizedString("Dynamic_Navigation_Check_Expiry_Description", comment: nil)
}
public var kDynamic_Navigation_Dont_Show: String {
    OALocalizedString("Dynamic_Navigation_Dont_Show", comment: nil)
}
public var kDynamic_Navigation_Dont_Show_Message: String {
    OALocalizedString("Dynamic_Navigation_Dont_Show_Message", comment: nil)
}
public var kDynamic_Navigation_Expiration_Date: String {
    OALocalizedString("Dynamic_Navigation_Expiration_Date", comment: nil)
}
public var kDynamic_Navigation_Help_Cancel: String {
    OALocalizedString("Dynamic_Navigation_Help_Cancel", comment: nil)
}
public var kDynamic_Navigation_Help_I_Want: String {
    OALocalizedString("Dynamic_Navigation_Help_I_Want", comment: nil)
}
public var kDynamic_Navigation_Help_Installation_Support: String {
    OALocalizedString("Dynamic_Navigation_Help_Installation_Support", comment: nil)
}
public var kDynamic_Navigation_Help_Support_Issue: String {
    OALocalizedString("Dynamic_Navigation_Help_Support_Issue", comment: nil)
}
public var kDynamic_Navigation_Help_To_Update: String {
    OALocalizedString("Dynamic_Navigation_Help_To_Update", comment: nil)
}
public var kDynamic_Navigation_Install_Help: String {
    OALocalizedString("Dynamic_Navigation_Install_Help", comment: nil)
}
public var kDynamic_Navigation_License_Key_Error: String {
    OALocalizedString("Dynamic_Navigation_License_Key_Error", comment: nil)
}
public var kDynamic_Navigation_More_Options: String {
    OALocalizedString("Dynamic_Navigation_More_Options", comment: nil)
}
public var kDynamic_Navigation_My_Map_Information: String {
    OALocalizedString("Dynamic_Navigation_My_Map_Information", comment: nil)
}
public var kDynamic_Navigation_Purchase_Begin_Install_Description: String {
    OALocalizedString("Dynamic_Navigation_Purchase_Begin_Install_Description", comment: nil)
}
public var kDynamic_Navigation_Purchase_Update_Description: String {
    OALocalizedString("Dynamic_Navigation_Purchase_Update_Description", comment: nil)
}
public var kDynamic_Navigation_Read_Privacy_Policy: String {
    OALocalizedString("Dynamic_Navigation_Read_Privacy_Policy", comment: nil)
}
public var kDynamic_Navigation_Remind_In_Week: String {
    OALocalizedString("Dynamic_Navigation_Remind_In_Week", comment: nil)
}
public var kDynamic_Navigation_Remind_Later: String {
    OALocalizedString("Dynamic_Navigation_Remind_Later", comment: nil)
}
public var kDynamic_Navigation_Remind_On: String {
    OALocalizedString("Dynamic_Navigation_Remind_On", comment: nil)
}
public var kDynamic_Navigation_Remind_Tomorrow: String {
    OALocalizedString("Dynamic_Navigation_Remind_Tomorrow", comment: nil)
}
public var kDynamic_Navigation_Renewal: String {
    OALocalizedString("Dynamic_Navigation_Renewal", comment: nil)
}
public var kDynamic_Navigation_Renewal_Delaer_Description: String {
    OALocalizedString("Dynamic_Navigation_Renewal_Delaer_Description", comment: nil)
}
public var kDynamic_Navigation_Support: String {
    OALocalizedString("Dynamic_Navigation_Support", comment: nil)
}
public var kDynamic_Navigation_Support_Description: String {
    OALocalizedString("Dynamic_Navigation_Support_Description", comment: nil)
}
public var kDynamic_Navigation_Troubleshooting: String {
    OALocalizedString("Dynamic_Navigation_Troubleshooting", comment: nil)
}
public var kDynamic_Navigation_Troubleshooting_Description: String {
    OALocalizedString("Dynamic_Navigation_Troubleshooting_Description", comment: nil)
}
public var kDynamic_Navigation_Update_Description: String {
    OALocalizedString("Dynamic_Navigation_Update_Description", comment: nil)
}
public var kDynamic_Navigation_Update_Step: String {
    OALocalizedString("Dynamic_Navigation_Update_Step", comment: nil)
}
public var kDynamic_Navigation_Vehicle_License_Key: String {
    OALocalizedString("Dynamic_Navigation_Vehicle_License_Key", comment: nil)
}
public var kCheck_Update_Eligibility: String {
    OALocalizedString("Check_Update_Eligibility", comment: nil)
}
public var kCheckUpdate_Expiration_Date: String {
    OALocalizedString("Expiration_Date", comment: nil)
}
public var kCheckUpdate_Expiration_Date_Invalid: String {
    OALocalizedString("Map_Expiration_Date_Invalid", comment: nil)
}
public var kCheckUpdate_Expiration_Date_Mismatch: String {
    OALocalizedString("Map_Expiration_Date_Mismatch", comment: nil)
}
public var kCheckUpdate_Map_Update_ID: String {
    OALocalizedString("Map_Update_ID", comment: nil)
}
public var kCheckUpdate_Map_Update_ID_Invalid: String {
    OALocalizedString("Map_Update_ID_Invalid", comment: nil)
}
public var kCheckUpdate_Map_Update_ID_Mismatch: String {
    OALocalizedString("Map_Update_ID_Mismatch", comment: nil)
}
public var kCheckUpdate_Map_Version: String {
    OALocalizedString("Map_Version", comment: nil)
}
public var kCheckUpdate_Map_Version_Invalid: String {
    OALocalizedString("Map_Version_Invalid", comment: nil)
}
public var kCheckUpdate_Map_Version_Mismatch: String {
    OALocalizedString("Map_Version_Mismatch", comment: nil)
}
public var kCheckUpdate_MapUpdatedSuccessfully: String {
    OALocalizedString("CheckUpdate_MapUpdatedSuccessfully", comment: nil)
}
public var kCheckUpdate_NoNewUpdates: String {
    OALocalizedString("CheckUpdate_NoNewUpdates", comment: nil)
}
public var kCheckUpdate_NoNewUpdates_Description: String {
    OALocalizedString("CheckUpdate_NoNewUpdates_Description", comment: nil)
}
public var kCheckUpdate_ReadyToUpdate: String {
    OALocalizedString("CheckUpdate_ReadyToUpdate", comment: nil)
}
public var kCheckUpdate_ReadyToUpdateDescription: String {
    OALocalizedString("CheckUpdate_ReadyToUpdateDescription", comment: nil)
}
public var kCheckUpdate_StepOne_Alert_Message: String {
    OALocalizedString("CheckUpdate_StepOne_Alert_Message", comment: nil)
}
public var kCheckUpdate_StepOne_Description: String {
    OALocalizedString("CheckUpdate_StepOne_Description", comment: nil)
}
public var kCheckUpdate_StepOne_Info: String {
    OALocalizedString("CheckUpdate_StepOne_Info", comment: nil)
}
public var kCheckUpdate_StepTwo_Description: String {
    OALocalizedString("CheckUpdate_StepTwo_Description", comment: nil)
}
public var kCheckUpdate_StepTwo_ImageDescription_Toyota: String {
    OALocalizedString("CheckUpdate_StepTwo_ImageDescription_Toyota", comment: nil)
}
public var kCheckUpdate_StepTwo_ImageDescription_Lexus: String {
    OALocalizedString("CheckUpdate_StepTwo_ImageDescription_Lexus", comment: nil)
}
public var kCheckUpdate_StepThree_Description: String {
    OALocalizedString("CheckUpdate_StepThree_Description", comment: nil)
}
public var kCheckUpdate_StepThree_ImageDescription: String {
    OALocalizedString("CheckUpdate_StepThree_ImageDescription", comment: nil)
}
public var kCheckUpdate_StepThree_Info: String {
    OALocalizedString("CheckUpdate_StepThree_Info", comment: nil)
}
public var kCheckUpdate_StepFour_Description: String {
    OALocalizedString("CheckUpdate_StepFour_Description", comment: nil)
}
public var kCheckUpdate_StepFour_ImageDescription: String {
    OALocalizedString("CheckUpdate_StepFour_ImageDescription", comment: nil)
}
public var kCheckUpdate_StepFive_Description: String {
    OALocalizedString("CheckUpdate_StepFive_Description", comment: nil)
}
public var kCheckUpdate_StepFive_WarningMessage: String {
    OALocalizedString("CheckUpdate_StepFive_WarningMessage", comment: nil)
}
public var kCheckUpdate_StepSix_Description: String {
    OALocalizedString("CheckUpdate_StepSix_Description", comment: nil)
}
public var kCheckUpdate_StepSeven_Description: String {
    OALocalizedString("CheckUpdate_StepSeven_Description", comment: nil)
}
public var kCheckUpdate_StepEight_Description: String {
    OALocalizedString("CheckUpdate_StepEight_Description", comment: nil)
}
public var kCheckUpdate_StepEight_ImageDescription: String {
    OALocalizedString("CheckUpdate_StepEight_ImageDescription", comment: nil)
}
public var kCheckUpdate_StepEight_WarningMessage: String {
    OALocalizedString("CheckUpdate_StepEight_WarningMessage", comment: nil)
}
public var kCheckUpdate_StepNine_Description: String {
    OALocalizedString("CheckUpdate_StepNine_Description", comment: nil)
}
public var kInstallUpdate_Intro_Description: String {
    OALocalizedString("InstallUpdate_Intro_Description", comment: nil)
}
public var kInstallUpdate_Intro_ProcessDurationInfo: String {
    OALocalizedString("InstallUpdate_Intro_ProcessDurationInfo", comment: nil)
}
public var kInstallUpdate_StepOne_Description: String {
    OALocalizedString("InstallUpdate_StepOne_Description", comment: nil)
}
public var kInstallUpdate_StepOne_WarningMessage: String {
    OALocalizedString("InstallUpdate_StepOne_WarningMessage", comment: nil)
}
public var kInstallUpdate_StepTwo_Description: String {
    OALocalizedString("InstallUpdate_StepTwo_Description", comment: nil)
}
public var kFollow_Steps_For_Check_Eligibility: String {
    OALocalizedString("Follow_Steps_For_Check_Eligibility", comment: nil)
}
public var kInstallUpdate_StepFour_Description: String {
    OALocalizedString("InstallUpdate_StepFour_Description", comment: nil)
}
public var kInstallUpdate_StepFour_ImageDescription: String {
    OALocalizedString("InstallUpdate_StepFour_ImageDescription", comment: nil)
}
public var kInstallUpdate_StepFour_Info: String {
    OALocalizedString("InstallUpdate_StepFour_Info", comment: nil)
}
public var kInstallUpdate_StepFive_WarningMessage: String {
    OALocalizedString("InstallUpdate_StepFive_WarningMessage", comment: nil)
}
public var kInstallUpdate_StepEight_Description: String {
    OALocalizedString("InstallUpdate_StepEight_Description", comment: nil)
}
public var kInstallUpdate_StepNine_Description: String {
    OALocalizedString("InstallUpdate_StepNine_Description", comment: nil)
}
public var kInstallUpdate_StepTen_AlertMessage_LicenseKey: String {
    OALocalizedString("InstallUpdate_StepTen_AlertMessage_LicenseKey", comment: nil)
}
public var kInstallUpdate_StepTen_AlertMessage_DataServices: String {
    OALocalizedString("InstallUpdate_StepTen_AlertMessage_DataServices", comment: nil)
}
public var kInstallUpdate_StepTen_Description: String {
    OALocalizedString("InstallUpdate_StepTen_Description", comment: nil)
}
public var kInstallUpdate_StepTen_ImageDescription: String {
    OALocalizedString("InstallUpdate_StepTen_ImageDescription", comment: nil)
}
public var kInstallUpdate_StepEleven_Description: String {
    OALocalizedString("InstallUpdate_StepEleven_Description", comment: nil)
}
public var kInstallUpdate_StepEleven_Description_Restart: String {
    OALocalizedString("InstallUpdate_StepEleven_Description_Restart", comment: nil)
}
public var kInstallUpdate_StepEleven_InfoMessage_Duration: String {
    OALocalizedString("InstallUpdate_StepEleven_InfoMessage_Duration", comment: nil)
}
public var kInstallUpdate_StepEleven_InfoMessage_RemainInVehicle: String {
    OALocalizedString("InstallUpdate_StepEleven_InfoMessage_RemainInVehicle", comment: nil)
}
public var kInstallUpdate_StepEleven_WarningMessage: String {
    OALocalizedString("InstallUpdate_StepEleven_WarningMessage", comment: nil)
}
public var kInstallUpdate_StepTwelve_Description: String {
    OALocalizedString("InstallUpdate_StepTwelve_Description", comment: nil)
}
public var kInstallUpdate_StepTwelve_InfoMessage_Duration: String {
    OALocalizedString("InstallUpdate_StepTwelve_InfoMessage_Duration", comment: nil)
}
public var kInstallUpdate_StepTwelve_InfoMessage_RemainInVehicle: String {
    OALocalizedString("InstallUpdate_StepTwelve_InfoMessage_RemainInVehicle", comment: nil)
}
public var kInstallUpdate_Complete_Title: String {
    OALocalizedString("InstallUpdate_Complete_Title", comment: nil)
}
public var kInstallUpdate_Complete_Description: String {
    OALocalizedString("InstallUpdate_Complete_Description", comment: nil)
}
public var kInstallUpdate_Complete_ImageDescription: String {
    OALocalizedString("InstallUpdate_Complete_ImageDescription", comment: nil)
}
public var kGetStarted: String {
    OALocalizedString("Get_Started", comment: nil)
}
public var kImproved_Routing: String {
    OALocalizedString("Improved_Routing", comment: nil)
}
public var kLatest_maps_from_the_cloud: String {
    OALocalizedString("Latest_maps_from_the_cloud", comment: nil)
}
public var kNavigation_Update: String {
    OALocalizedString("Navigation_Update", comment: nil)
}
public var kPurchase: String {
    OALocalizedString("Purchase", comment: nil)
}
public var kPurchase_Dynamic_Navigation: String {
    OALocalizedString("Purchase_Dynamic_Navigation", comment: nil)
}
public var kRemain_In_The_Vehicle_Warning: String {
    OALocalizedString("Remain_In_The_Vehicle_Warning", comment: nil)
}
public var kRenewing_Dynamic_Navigation: String {
    OALocalizedString("Renewing_Dynamic_Navigation", comment: nil)
}
public var kUpdated_Points_of_Interest: String {
    OALocalizedString("Updated_Points_of_Interest", comment: nil)
}
public var kVehicle_Must_Be_Turned_On_Warning: String {
    OALocalizedString("Vehicle_Must_Be_Turned_On_Warning", comment: nil)
}

// MARK: - Dashboard Lights
public var kDashboardLightsWarningLights: String {
    OALocalizedString("DashboardLights_warning_lights", comment: nil)
}
public var kDashboardLights: String {
    OALocalizedString("DashboardLights", comment: nil)
}
public var kSearch: String {
    OALocalizedString("Search", comment: nil)
}
public var kDashboardLightsNoLights: String {
    OALocalizedString("DashboardLights_no_lights", comment: nil)
}
public var kManageSubscription: String {
    OALocalizedString("Marketing_Manage_Subscription", comment: nil)
}
public var kVisitSupportPage: String {
    OALocalizedString("Visit_Support_Page", comment: nil)
}
public var kNeedMoreAssistance: String {
    OALocalizedString("Need_More_Assistance", comment: nil)
}
public var kVisitSupportPageDescription: String {
    OALocalizedString("Visit_Support_Page_Description", comment: nil)
}
public var kDisclaimer: String {
    OALocalizedString("Dashboard_Card_Disclaimer", comment: nil)
}
public var kDisclaimerDescription: String {
    OALocalizedString("Dashboard_Card_Disclaimer_Discription", comment: nil)
}

#if !(SMARTWATCH || SIRI || EXTENSION)
extension UBIOfferOptinViewController {
    public enum Constants {
        static var kUBIOfferZipTitle: String {
            OALocalizedString("Consents_enter_your_zip_code", comment: nil)
        }
        static var kUBIOfferTitle: String {
            OALocalizedString("Consents_special_offer", comment: nil)
        }
    }
}

extension UBIOptinViewController {
    public enum Constants {
        static let kTitle = OALocalizedString("Consents_special_offer", comment: nil)
        static let kToyotaConsentHeadline = OALocalizedString("UBI_card_title", comment: nil)
        static let kToyotaConsentSubHeadline = OALocalizedString("UBI_card_sub_title", comment: nil)
        static let kToyotaConsentBody = OALocalizedString("UBI_consents_body", comment: nil)
        static let kToyotaConsentButtonTitle = OALocalizedString("UBI_share_vehicle_information", comment: nil)
        static let kToyotaConsentTermsTitle = OALocalizedString("DataConsents_terms_and_conditions", comment: nil)
    }
}
#endif

// MARK: Remote Service Exceptions Disabled Error
var kShowsRemoteServiceDisabledToast: String {
    OALocalizedString("Common_remote_service_disabled_error", comment: nil)
}

var kShowsRemoteServiceReferCardToast: String {
    OALocalizedString("RemoteUser_refer_remote_card", comment: nil)
}

var kShowsRemoteServiceNotCapableToast: String {
    OALocalizedString("RemoteUser_remote_card_not_capable", comment: nil)
}

var kShowsRemoteServiceOtherUserCardToast: String {
    OALocalizedString("RemoteUser_remote_card_other_user", comment: nil)
}

// MARK: Connection Error
var kInternetConnectionNotFound: String {
    OALocalizedString("Common_connection_error", comment: nil)
}
var kLimitedInternetConnection: String {
    OALocalizedString("Common_limited_connection_error", comment: nil)
}
var kNetowrkWithoutInternet: String {
    OALocalizedString("Common_network_without_internet_error", comment: nil)
}
var kUnableToConnectToOurGateway: String {
    OALocalizedString("Common_unable_to_Connect_to_Gateway", comment: nil)
}

// MARK: Tire Pressure

public var kTirePressure_Title: String {
    OALocalizedString("TirePressure_Title", comment: nil)
}

public var kTirePressure_Description: String {
    OALocalizedString("TirePressure_Description", comment: nil)
}

public var kTirePressure_Description_No_TPMS: String {
    OALocalizedString("TirePressure_Description_No_TPMS", comment: nil)
}

// MARK: Vehicle Specs

public var kVehicleSpecs: String {
    OALocalizedString("VehicleSpecs_title", comment: nil)
}

// MARK: Common
public var kRemoveVehicle: String {
    OALocalizedString("Common_remove_vehicle", comment: nil)
}
public var kFilterBy: String {
    OALocalizedString("Common_filter_by", comment: nil)
}
public var kRemove: String {
    OALocalizedString("Common_remove", comment: nil)
}
public var kCancel: String {
    OALocalizedString("Common_cancel", comment: nil)
}
public var kDelete: String {
    OALocalizedString("Common_delete", comment: nil)
}
public var kChoose: String {
    OALocalizedString("Common_choose", comment: nil)
}
public var kBack: String {
    OALocalizedString("Common_back", comment: nil)
}
public var kOkay: String {
    OALocalizedString("Common_okay", comment: nil)
}
public var kSettings: String {
    OALocalizedString("Common_settings", comment: nil)
}
public var kNsA: String {
    OALocalizedString("Common_N/A", comment: nil)
}
public var kNotInfAvailable: String {
    OALocalizedString("Common_NoInfAvailable", comment: nil)
}
public var kDash: String {
    OALocalizedString("Common_Dashes", comment: nil)
}
public var kEdit: String {
    OALocalizedString("common_edit", comment: nil)
}
public var kDone: String {
    OALocalizedString("common_done", comment: nil)
}
public var kSave: String {
    OALocalizedString("Common_save", comment: nil)
}
public var kOff: String {
    OALocalizedString("Common_off", comment: nil)
}
public var kOn: String {
    OALocalizedString("Common_on", comment: nil)
}
public var kTitleNo: String {
    OALocalizedString("Common_no", comment: nil)
}
public var kTitleYes: String {
    OALocalizedString("Common_yes", comment: nil)
}
public var kTitleOk: String {
    OALocalizedString("Common_ok", comment: nil)
}
public var kTitleCall: String {
    OALocalizedString("Common_call", comment: nil)
}
public var kTitleOr: String {
    OALocalizedString("Common_or", comment: nil)
}
public var kNone: String {
    OALocalizedString("Common_none", comment: nil)
}
public var kConfirm: String {
    OALocalizedString("Common_confirm", comment: nil)
}
public var kDate: String {
    OALocalizedString("Common_date", comment: nil)
}
public var kLocation: String {
    OALocalizedString("Common_location", comment: nil)
}
public var kTitleDemoModeAlert: String {
    OALocalizedString("common_not_support_in_demo", comment: nil)
}
public var kWindowDoesNotExist: String {
    OALocalizedString("Common_no_window", comment: nil)
}
public var kLearnMore: String {
    OALocalizedString("Common_learn_more", comment: nil)
}
public var kMore: String {
    OALocalizedString("Common_more", comment: nil)
}
public var kLess: String {
    OALocalizedString("Common_less", comment: nil)
}
public var kUndo: String {
    OALocalizedString("Common_undo", comment: nil)
}
public var kTitleDescription: String {
    OALocalizedString("Common_description", comment: nil)
}
public var kSend: String {
    OALocalizedString("Common_send", comment: nil)
}
public var kClose: String {
    OALocalizedString("Common_close", comment: nil)
}
public var kDay: String {
    OALocalizedString("Common_day", comment: nil)
}
public var kBrandNameToyota: String {
    OALocalizedString("Common_brand_name_toyota", comment: nil)
}
public var kBrandNameLexus: String {
    OALocalizedString("Common_brand_name_lexus", comment: nil)
}
public var kBrandNameSubaru: String {
    OALocalizedString("Common_brand_name_subaru", comment: nil)
}
public var kContinue: String {
    OALocalizedString("Common_continue", comment: nil)
}
public var kAddNew: String {
    OALocalizedString("Common_add_new", comment: nil)
}
public var kAdd: String {
    OALocalizedString("Common_add", comment: nil)
}
public var kSelect: String {
    OALocalizedString("Common_select", comment: nil)
}
public var kSaveInfoAndContinue: String {
    OALocalizedString("Save_Info_and_Continue", comment: nil)
}
public var kSaveInfoAndCheckForUpdates: String {
    OALocalizedString("Save_Info_and_Check_For_Updates", comment: nil)
}
public var kExit: String {
    OALocalizedString("Common_exit", comment: nil)
}
public var kGetHelp: String {
    OALocalizedString("Common_get_help", comment: nil)
}
public var kHelp: String {
    OALocalizedString("Common_help", comment: nil)
}
public var kDefault: String {
    OALocalizedString("Common_default", comment: nil)
}
public var kReturnToDashboard: String {
    OALocalizedString("Common_return_to_dashboard", comment: nil)
}
public var kGoToDashboard: String {
    OALocalizedString("Common_go_to_dashboard", comment: nil)
}
public var kLoan: String {
    OALocalizedString("Common_loan", comment: nil)
}
public var kViewMore: String {
    OALocalizedString("Common_view_more", comment: nil)
}
public var kLease: String {
    OALocalizedString("Common_lease", comment: nil)
}
public var kMon: String {
    OALocalizedString("Common_mon", comment: nil)
}
public var kTue: String {
    OALocalizedString("Common_tue", comment: nil)
}
public var kWed: String {
    OALocalizedString("Common_wed", comment: nil)
}
public var kThu: String {
    OALocalizedString("Common_thu", comment: nil)
}
public var kFri: String {
    OALocalizedString("Common_fri", comment: nil)
}
public var kSat: String {
    OALocalizedString("Common_sat", comment: nil)
}
public var kSun: String {
    OALocalizedString("Common_sun", comment: nil)
}
public var kCommonBackToDashboard: String {
    OALocalizedString("Common_back_to_dashboard", comment: nil)
}
public var kGeneral: String {
    OALocalizedString("Common_general", comment: nil)
}
public var kCloseApp: String {
    OALocalizedString("Common_Close_App", comment: nil)
}
public var kUpdateAvailable: String {
    OALocalizedString("Common_Update_Available", comment: nil)
}
public var kMax: String {
    OALocalizedString("Common_max", comment: nil)
}
public var kOf: String {
    OALocalizedString("Common_Of", comment: nil)
}
public var kMi: String {
    OALocalizedString("Common_mi", comment: nil)
}
public var kMinutes: String {
    OALocalizedString("Common_minutes", comment: nil)
}
public var kStep: String {
    OALocalizedString("Common_step", comment: nil)
}

// MARK: - Critical Message
public var kViewAnnouncement: String {
    OALocalizedString("Announcement_field", comment: nil)
}
public var kViewAnnouncements: String {
    OALocalizedString("Announcement_fields", comment: nil)
}
public var kAppUpdateAvailable: String {
    OALocalizedString("AppUpdate_available", comment: nil)
}
public var kAppUpdateRequiredAvailableTitle: String {
    OALocalizedString("AppUpdate_required_title", comment: nil)
}
public var kAppUpdateRequiredAvailableMessage: String {
    OALocalizedString("AppUpdate_required_message", comment: nil)
}
public var kExpirationDate: String {
    OALocalizedString("Expiration_Date:", comment: nil)
}
// MARK: - Collision Assistance

public var kCollisionAssitanceHeaderTitle: String {
    OALocalizedString("Collision_Assitance_Header_Title", comment: nil)
}
public var kCollisionAssitanceVCTitle: String {
    OALocalizedString("Collision_Assitance_VC_Title", comment: nil)
}
public var kCollisionAssitanceVCDescription: String {
    OALocalizedString("Collision_Assitance_VC_Description", comment: nil)
}
public var kCollisionAssitanceFileClaimTitle: String {
    OALocalizedString("Collision_Assitance_FileClaim_Title", comment: nil)
}
public var kCollisionAssitanceFileClaimDescription: String {
    OALocalizedString("Collision_Assitance_FileClaim_Description", comment: nil)
}
public var kCollisionAssitanceStatusDescription: String {
    OALocalizedString("Collision_Assitance_Status_Description", comment: nil)
}
public var kCollisionAssitanceStatusSubtitle: String {
    OALocalizedString("Collision_Assitance_Status_Subtitle", comment: nil)
}
public var kCollisionAssitanceNotificationFeatureCheck: String {
    OALocalizedString("kCollision_Assitance_Notification_Feature_Check", comment: nil)
}

public var kRequestFailureMessage: String {
    OALocalizedString("Request_Failure_Message", comment: nil)
}

// In-App Marketing
public var kMarketingConsentCardTitle: String {
    OALocalizedString("kMarketing_Consent_Card_Title", comment: nil)
}
public var kMarketingConsentCardDescription: String {
    OALocalizedString("kMarketing_Consent_Card_Description", comment: nil)
}
public var kMarketingConsentCardButtonTitle: String {
    OALocalizedString("kMarketing_Consent_Card_Button_Title", comment: nil)
}
public var kInAppMarketingSuccessMessage: String {
    OALocalizedString("kInApp_Marketing_Success_Message", comment: nil)
}
public var kMarketingConsentCardConfirmationTitle: String {
    OALocalizedString("kMarketing_Consent_Card_Confirmation_Title", comment: nil)
}
// MARK: - Amber Alert Consent
public var kAmberAlertConsentCardTitle: String {
    OALocalizedString("kAmberAlert_Consent_Card_Title", comment: nil)
}
public var kAmberAlertConsentCardDescription: String {
    OALocalizedString("kAmberAlert_Consent_Card_Description", comment: nil)
}
public var kAmberAlertConsentCardButtonTitle: String {
    OALocalizedString("kAmberAlert_Consent_Card_Button_Title", comment: nil)
}
public var kAmberAlertSuccessMessage: String {
    OALocalizedString("kAmberAlert_Success_Message", comment: nil)
}
public var kAmberAlertDeclinedMessage: String {
    OALocalizedString("kAmberAlert_Declined_Message", comment: nil)
}
public var kAmberAlertConsentCardConfirmationTitle: String {
    OALocalizedString("kAmberAlert_Consent_Card_Confirmation_Title", comment: nil)
}
public var kAmberAlertConsentCardConfirmationDeclinedTitle: String {
    OALocalizedString("kAmberAlert_Consent_Card_Confirmation_Declined_Title", comment: nil)
}

// MARK: - ALERTS SUBSCRIPTIONS
public var KLearnMoreAboutAlerts: String {
    OALocalizedString("Learn_More_About_Alerts", comment: nil)
}
public var KLearnMoreAboutAlertsFAQ: String {
    OALocalizedString("Learn_More_About_Alerts_FAQ", comment: nil)
}

// MARK: Service Record
public var KAddServiceRecordTitle: String {
    OALocalizedString("Add_Service_Record_Title", comment: nil)
}
public var KServiceRecordTitle: String {
    OALocalizedString("Service_Record_Title", comment: nil)
}
public var KAddServiceRecordSubTitle: String {
    OALocalizedString("Add_Service_Record_SubTitle", comment: nil)
}
public var KDeleteServiceRecord: String {
    OALocalizedString("Delete_Service_Record", comment: nil)
}
public var KDeleteServiceRecordAlertMessage: String {
    OALocalizedString("Delete_Service_Record_Alert_Message", comment: nil)
}

// MARK: TFS
public var kMakePaymentDueDate: String {
    OALocalizedString("MakePayment_due_date", comment: nil)
}
public var kMakePaymentDueString: String {
    OALocalizedString("MakePayment_due_string", comment: nil)
}
public var kMakePaymentAmountDue: String {
    OALocalizedString("MakePayment_amount_due", comment: nil)
}
public var kMakePaymentTotalAmountDue: String {
    OALocalizedString("MakePayment_total_amount_due", comment: nil)
}
public var kMakePaymentPastDue: String {
    OALocalizedString("MakePayment_past_due", comment: nil)
}
public var kUnlinkAccount: String {
    OALocalizedString("MakePayment_unlink_account", comment: nil)
}
public var kUnlinkAccountMessage: String {
    OALocalizedString("MakePayment_unlink_account_message", comment: nil)
}
public var kMakePaymentTitle: String {
    OALocalizedString("MakePayment_title", comment: nil)
}
public var kMakePaymentChooseAmountTitle: String {
    OALocalizedString("MakePayment_choose_amount_title", comment: nil)
}
public var kMakePaymentCustomAmount: String {
    OALocalizedString("MakePayment_custom_amount", comment: nil)
}
public var kMakePaymentEnterCustomAmount: String {
    OALocalizedString("MakePayment_enter_custom_amount", comment: nil)
}
public var kMakePaymentCustomAmountLoanReminder: String {
    OALocalizedString("MakePayment_custom_amount_loan_reminder", comment: nil)
}
public var kMakePaymentCustomAmountFirstLeaseReminder: String {
    OALocalizedString("MakePayment_custom_amount_first_lease_reminder", comment: nil)
}
public var kMakePaymentCustomAmountSecondLeaseReminder: String {
    OALocalizedString("MakePayment_custom_amount_second_lease_reminder", comment: nil)
}
public var kMakePaymentEnterValidAmount: String {
    OALocalizedString("MakePayment_enter_valid_amount", comment: nil)
}
public var kMakePaymentChooseDateTitle: String {
    OALocalizedString("MakePayment_choose_date_title", comment: nil)
}
public var kMakePaymentCustomDate: String {
    OALocalizedString("MakePayment_custom_date", comment: nil)
}
public var kMakePaymentCustomDateReminder: String {
    OALocalizedString("MakePayment_custom_date_reminder", comment: nil)
}
public var kMakePaymentSelectDate: String {
    OALocalizedString("MakePayment_select_date", comment: nil)
}
public var kMakePaymentAccount: String {
    OALocalizedString("MakePayment_account", comment: nil)
}
public var kMakePaymentPayFrom: String {
    OALocalizedString("MakePayment_pay_from", comment: nil)
}
public var kMakePaymentAmount: String {
    OALocalizedString("MakePayment_amount", comment: nil)
}
public var kMakePaymentPayOn: String {
    OALocalizedString("MakePayment_pay_on", comment: nil)
}
public var kMakePaymentTermsConditions: String {
    OALocalizedString("MakePayment_terms_conditions", comment: nil)
}
public var kMakePaymentAgreement: String {
    OALocalizedString("MakePayment_agreement", comment: nil)
}
public var kMakePaymentVisitApp: String {
    OALocalizedString("MakePayment_visit_app", comment: nil)
}
public var kMakePaymentReturn: String {
    OALocalizedString("MakePayment_return", comment: nil)
}
public var kMakePaymentNotice: String {
    OALocalizedString("MakePayment_notice", comment: nil)
}
public var kMakePaymentScreenshot: String {
    OALocalizedString("MakePayment_screenshot", comment: nil)
}
public var kEnableScreenshotTitle: String {
    OALocalizedString("Enable_screenshot_title", comment: nil)
}
public var kEnableScreenshotMessage: String {
    OALocalizedString("Enable_screenshot_message", comment: nil)
}
public var kScreenshotError: String {
    OALocalizedString("Screenshot_error", comment: nil)
}
public var kMakePaymentViewTransaction: String {
    OALocalizedString("MakePayment_view_transaction", comment: nil)
}
public var kPaymentComplete: String {
    OALocalizedString("MakePayment_payment_complete", comment: nil)
}
public var kMakePaymentConfirmationNumber: String {
    OALocalizedString("MakePayment_confirmation_number", comment: nil)
}
public var kMakePaymentPaidFrom: String {
    OALocalizedString("MakePayment_paid_from", comment: nil)
}
public var kMakePaymentScheduledFor: String {
    OALocalizedString("MakePayment_scheduled_for", comment: nil)
}
public var kMakePaymentScheduled: String {
    OALocalizedString("MakePayment_scheduled", comment: nil)
}
public var kMakePaymentScreenshotSaved: String {
    OALocalizedString("MakePayment_screenshot_saved", comment: nil)
}
public var kUnlinkAccountDescription: String {
    OALocalizedString("MakePayment_unlink_account_description", comment: nil)
}
public var kHere: String {
    OALocalizedString("Here", comment: nil)
}
public var kManageTFSAccount: String {
    OALocalizedString("MakePayment_manage_account", comment: nil)
}
public var kMakePaymentScheduledPaymentsTitle: String {
    OALocalizedString("MakePayment_scheduled_payments_title", comment: nil)
}
public var kMakePaymentNoScheduledPayments: String {
    OALocalizedString("MakePayment_no_scheduled_payments", comment: nil)
}
public var kMakePaymentScheduledPayments: String {
    OALocalizedString("MakePayment_scheduled_payments", comment: nil)
}
public var kMakePaymentScheduledPaymentsNotice: String {
    OALocalizedString("MakePayment_scheduled_payments_notice", comment: nil)
}
public var kMakePaymentMyScheduledPayments: String {
    OALocalizedString("MakePayment_my_scheduled_payments", comment: nil)
}
public var kMakePaymentFrequency: String {
    OALocalizedString("MakePayment_frequency", comment: nil)
}
public var kMakePaymentOneTime: String {
    OALocalizedString("MakePayment_one_time", comment: nil)
}
public var kMakePaymentRecurring: String {
    OALocalizedString("MakePayment_recurring", comment: nil)
}
public var kMakePaymentTransactionDetails: String {
    OALocalizedString("MakePayment_transaction_details", comment: nil)
}
public var kMakePaymentTransactionReminder: String {
    OALocalizedString("MakePayment_transaction_reminder", comment: nil)
}
public var kMakePaymentTransactionChannel: String {
    OALocalizedString("MakePayment_transaction_channel", comment: nil)
}
public var kMakePaymentPostingDate: String {
    OALocalizedString("MakePayment_posting_date", comment: nil)
}
public var kMakePaymentEditPayment: String {
    OALocalizedString("MakePayment_edit_payment", comment: nil)
}
public var kMakePaymentChannelApp: String {
    OALocalizedString("MakePayment_channel_app", comment: nil)
}
public var kProceedWithPayment: String {
    OALocalizedString("MakePayment_proceed", comment: nil)
}
public var kEnterCode: String {
    OALocalizedString("MakePayment_enter_code", comment: nil)
}
public var kActivationCode: String {
    OALocalizedString("MakePayment_activation_code", comment: nil)
}
public var kToyotaFinancialServices: String {
    OALocalizedString("MakePayment_tfs", comment: nil)
}
public var kLexusFinancialServices: String {
    OALocalizedString("MakePayment_lfs", comment: nil)
}
public var kLinkAccount: String {
    OALocalizedString("MakePayment_Link_Account", comment: nil)
}
public var kLinkAccountTitle: String {
    OALocalizedString("MakePayment_link_title", comment: nil)
}
public var kLinkAccountDescription: String {
    OALocalizedString("MakePayment_link_description", comment: nil)
}
public var kUsername: String {
    OALocalizedString("Username", comment: nil)
}
public var kTapToReset: String {
    OALocalizedString("MakePayment_tap_to_reset", comment: nil)
}
public var kLinkAccountForgotCredentials: String {
    OALocalizedString("MakePayment_forgot_credentials", comment: nil)
}
public var kPrivacyPolicy: String {
    OALocalizedString("PrivacyPolicy", comment: nil)
}
public var kLegalTerms: String {
    OALocalizedString("LegalTerms", comment: nil)
}
public var kLinkAccountAgreement: String {
    OALocalizedString("MakePayment_link_account_agreement", comment: nil)
}
public var kAccountLinked: String {
    OALocalizedString("MakePayment_account_linked", comment: nil)
}
public var kEnterValidCode: String {
    OALocalizedString("MakePayment_enter_valid_code", comment: nil)
}
public var kRequestNewCode: String {
    OALocalizedString("MakePayment_request_new_code", comment: nil)
}
public var kDidntReceive: String {
    OALocalizedString("MakePayment_didnt_receive", comment: nil)
}
public var kTFSOnlinePolicies: String {
    OALocalizedString("MakePayment_online_policies", comment: nil)
}
public var kTFSPrivacyPolicy: String {
    OALocalizedString("MakePayment_privacy_policy", comment: nil)
}
public var kLinkingRequired: String {
    OALocalizedString("MakePayment_linking_required", comment: nil)
}
public var kLinkingDescription: String {
    OALocalizedString("MakePayment_linking_description", comment: nil)
}
public var kLinkNow: String {
    OALocalizedString("MakePayment_link_now", comment: nil)
}
public var kTFSInitialLinkingToast: String {
    OALocalizedString("MakePayment_initial_linking_toast", comment: nil)
}
public var kTFSAutoLinkingToast: String {
    OALocalizedString("MakePayment_auto_linking_toast", comment: nil)
}
public var kIncorrectSignIn: String {
    OALocalizedString("MakePayment_incorrect_sign_in", comment: nil)
}
public var kGenericError: String {
    OALocalizedString("generic_error", comment: nil)
}
public var kCodeResent: String {
    OALocalizedString("MakePayment_code_resent", comment: nil)
}
public var kCouldntResendOTP: String {
    OALocalizedString("MakePayment_couldnt_resend_otp", comment: nil)
}
public var kAccountLocked: String {
    OALocalizedString("MakePayment_account_locked", comment: nil)
}
public var kAccountLockedMessage: String {
    OALocalizedString("MakePayment_account_locked_message", comment: nil)
}
public var kAccountHardLockedMessage: String {
    OALocalizedString("MakePayment_account_hard_locked_message", comment: nil)
}
public var kNoMatchingVinsTitle: String {
    OALocalizedString("MakePayment_no_matching_vins_title", comment: nil)
}
public var kNoMatchingVins: String {
    OALocalizedString("MakePayment_no_matching_vins", comment: nil)
}
public var kGenericPaymentError: String {
    OALocalizedString("MakePayment_generic_payment_error", comment: nil)
}
public var kGenericPaymentErrorTitle: String {
    OALocalizedString("MakePayment_generic_payment_error_title", comment: nil)
}
public var kTFSVisitSite: String {
    OALocalizedString("MakePayment_visit_site", comment: nil)
}
public var kTFSPaymentErrorTitle: String {
    OALocalizedString("MakePayment_payment_error_title", comment: nil)
}
public var kTFSInvalidAmountError: String {
    OALocalizedString("MakePayment_invalid_amount_error", comment: nil)
}
public var kTFSDateInPastError: String {
    OALocalizedString("MakePayment_past_date_error", comment: nil)
}
public var kTFSFutureDateError: String {
    OALocalizedString("MakePayment_future_date_error", comment: nil)
}
public var kDuplicatePaymentError: String {
    OALocalizedString("MakePayment_duplicate_payment_error", comment: nil)
}
public var kDuplicatePayment: String {
    OALocalizedString("MakePayment_duplicate_payment", comment: nil)
}
public var kTFSGenericAuthError: String {
    OALocalizedString("MakePayment_generic_auth_error", comment: nil)
}
public var kTFSAccountDataError: String {
    OALocalizedString("MakePayment_account_data_error", comment: nil)
}
public var kAccountDisabled: String {
    OALocalizedString("MakePayment_account_disabled", comment: nil)
}
public var kTFSDisabledVerify: String {
    OALocalizedString("MakePayment_disabled_verify", comment: nil)
}
public var kTFSDisabledFirstBullet: String {
    OALocalizedString("MakePayment_disabled_first_bullet", comment: nil)
}
public var kTFSDisabledPrivacyPreferences: String {
    OALocalizedString("MakePayment_disabled_privacy_preferences", comment: nil)
}
public var kTFSDisabledSecondBullet: String {
    OALocalizedString("MakePayment_disabled_second_bullet", comment: nil)
}
public var kTFSDisabledFurtherAssistance: String {
    OALocalizedString("MakePayment_disabled_further_assistance", comment: nil)
}
public var kTFSGoTo: String {
    OALocalizedString("MakePayment_go_to", comment: nil)
}
public var kAccountDisabledDescription: String {
    OALocalizedString("MakePayment_account_disabled_description", comment: nil)
}
public var kLFSAppName: String {
    OALocalizedString("MakePayment_lfs_app_name", comment: nil)
}
public var kTFSAppName: String {
    OALocalizedString("MakePayment_tfs_app_name", comment: nil)
}
public var kDisabledAccountError: String {
    OALocalizedString("MakePayment_disabled_account_error", comment: nil)
}
public var kTFSUnlinkingToast: String {
    OALocalizedString("MakePayment_unlinking_toast", comment: nil)
}
public var kIncorrectRelinkingAccount: String {
    OALocalizedString("MakePayment_incorrect_relinking_account", comment: nil)
}
public var kManageBankAccount: String {
    OALocalizedString("MakePayment_manage_bank_account", comment: nil)
}
public var kManageBankAccountMessage: String {
    OALocalizedString("MakePayment_manage_bank_account_message", comment: nil)
}
public var kMakePaymentRevalidateTitle: String {
    OALocalizedString("MakePayment_revalidate_title", comment: nil)
}
public var kMakePaymentRevalidateMessage: String {
    OALocalizedString("MakePayment_revalidate_message", comment: nil)
}
public var kTFSRevalidationSuccessToast: String {
    OALocalizedString("MakePayment_revalidation_success_toast", comment: nil)
}
public var kTFSRevalidationFailedToastMessage: String {
    OALocalizedString("MakePayment_revalidation_failed_toast_message", comment: nil)
}
public var kPaymentChannelWebsite: String {
    OALocalizedString("MakePayment_website", comment: nil)
}
public var kPaymentChannelPhone: String {
    OALocalizedString("MakePayment_phone", comment: nil)
}
public var kSwipeToPay: String {
    OALocalizedString("MakePayment_swipe_to_pay", comment: nil)
}
public var kProcessingPayment: String {
    OALocalizedString("MakePayment_processing", comment: nil)
}
public var kTFSAccountTypeMessage: String {
    OALocalizedString("MakePayment_account_type_message", comment: nil)
}
public var kMakePaymentPayFromMessage: String {
    OALocalizedString("MakePayment_pay_from_message", comment: nil)
}
public var kPaymentTransactionsButtonTitle: String {
    OALocalizedString("MakePayment_transactions_button_title", comment: nil)
}

// MARK: - Dealer Services
public var kDealerServicesStepText: String {
    OALocalizedString("DealerServices_step_text", comment: nil)
}
public var kDealerServicesChooseWork: String {
    OALocalizedString("DealerServices_choose_work", comment: nil)
}
public var kDealerServicesChooseAdvisor: String {
    OALocalizedString("DealerServices_choose_advisor", comment: nil)
}
public var kDealerServicesAnyAdvisor: String {
    OALocalizedString("DealerServices_any_advisor", comment: nil)
}
public var kDealerServicesChooseTransport: String {
    OALocalizedString("DealerServices_choose_transport", comment: nil)
}

// MARK: - demo
public var kDemoSignUp_Apple: String {
    "Sign in with Apple"
}
public var kDemoSignUp_Google: String {
    "Google"
}
public var kDemoSignUp_Facebook: String {
    "Facebook"
}
public var kDemoSign_In: String {
    OALocalizedString("Demo_sign_in", comment: nil)
}
public var kDemoCanadaTitle: String {
    OALocalizedString("Demo_canada_demoTitle", comment: nil)
}
public var kDemoCreate_an: String {
    OALocalizedString("Demo_Create_an", comment: nil)
}
public var kDemoUse_your: String {
    OALocalizedString("Demo_Use_your", comment: nil)
}
public var kDemoOr_sign: String {
    OALocalizedString("Demo_Or_sign", comment: nil)
}
public var kDemoAlready_have: String {
    OALocalizedString("Demo_Already_have", comment: nil)
}
public var kDemoVerify_your: String {
    OALocalizedString("Demo_Verify_your", comment: nil)
}
public var kDemoWe_have: String {
    OALocalizedString("Demo_We_have", comment: nil)
}
public var kDemoOnce_you: String {
    OALocalizedString("Demo_Once_you", comment: nil)
}
public var kDemoActivation_Code: String {
    OALocalizedString("Demo_Activation_Code", comment: nil)
}
public var kDemoHaven_t: String {
    OALocalizedString("Demo_Haven_t", comment: nil)
}
public var kDemoRequestCode: String {
    OALocalizedString("Login_demo_request_not_received2", comment: nil)
}
public var kDemoVerify_Account: String {
    OALocalizedString("Demo_Verify_Account", comment: nil)
}
public var kDemoFirstName: String {
    OALocalizedString("Demo_FirstName", comment: nil)
}
public var KDemo_LastName: String {
    OALocalizedString("Demo_LastName", comment: nil)
}
public var kDemoEmail: String {
    OALocalizedString("Demo_Email", comment: nil)
}
public var kDemo_Terms: String {
    OALocalizedString("Demo_Terms", comment: nil)
}
public var kDemo_Privacy: String {
    OALocalizedString("Demo_Privacy", comment: nil)
}

// Auto Renew Subscription
public var kAutoRenew: String {
    OALocalizedString("autorenew", comment: nil)
}
public var kAutoRenewMessageAutoRenewOn: String {
    OALocalizedString("autorenew_message_autorenew_on", comment: nil)
}
public var kAutoRenewMessageAutoRenewOff: String {
    OALocalizedString("autorenew_message_autorenew_off", comment: nil)
}
public var kAutoRenewTitleUpdatedSuccessfully: String {
    OALocalizedString("autorenew_title_updated_successfully", comment: nil)
}
public var kAutoRenewTitleUpdatedFail: String {
    OALocalizedString("autorenew_title_updated_fail", comment: nil)
}
public var kAutoRenewMessageUpdatedFail: String {
    OALocalizedString("autorenew_message_updated_fail", comment: nil)
}
public var kAutoRenewSubscription: String {
    OALocalizedString("autorenew_subscription", comment: nil)
}
public var kUpdateSubscription: String {
    OALocalizedString("update_subscription", comment: nil)
}
public var kCancelSubscription: String {
    OALocalizedString("cancel_subscription", comment: nil)
}
public var kRefundPreviewCustomerFriendlyReason: String {
    OALocalizedString("RefundPreview_customer_friendly_reason", comment: nil)
}
public var kAutoRenewOn: String {
    OALocalizedString("autorenew_on", comment: nil)
}
public var kAutoRenewOff: String {
    OALocalizedString("autorenew_off", comment: nil)
}
public var kRemovePayment: String {
    OALocalizedString("Remove", comment: nil)
}
// RefundPreview && RefundPreviewConfirmation
public var kRefundPreviewReasonForCancelation: String {
    OALocalizedString("RefundPreview_reason_for_cancellation", comment: nil)
}
public var kRefundPreviewTitleCancellationSuccessfully: String {
    OALocalizedString("RefundPreview_title_cancellation_successfully", comment: nil)
}
public var kRefundPreviewTitleUnableProcessRefund: String {
    OALocalizedString("RefundPreview_title_unable_process_refund", comment: nil)
}
public var kRefundPreviewMessageCancellationSuccessWithRefund: String {
    OALocalizedString("RefundPreview_message_cancellation_success_with_refund", comment: nil)
}
public var kRefundPreviewMessageCancellationSuccessWithoutRefund: String {
    OALocalizedString("RefundPreview_message_cancellation_success_without_refund", comment: nil)
}
public var kRefundPreviewMessageCancellationFail: String {
    OALocalizedString("RefundPreview_message_cancellation_fail", comment: nil)
}
public var kRefundPreviewMessageCancelGenericMessage: String {
    OALocalizedString("RefundPreview_message_cancel_generic_message", comment: nil)
}
public var kRefundPreviewMessageCancelRefund: String {
    OALocalizedString("RefundPreview_message_cancel_refund", comment: nil)
}
public var kRefundPreviewConfirmationTitleUpdateComplete: String {
    OALocalizedString("RefundPreviewConfirmation_title_update_complete", comment: nil)
}
public var kRefundPreviewConfirmationTitleUpdateIncomplete: String {
    OALocalizedString("RefundPreviewConfirmation_title_update_incomplete", comment: nil)
}
public var kRefundPreviewConfirmationTitleCancellationComplete: String {
    OALocalizedString("RefundPreviewConfirmation_title_cancellation_complete", comment: nil)
}
public var kRefundPreviewConfirmationTitleCancellationIncomplete: String {
    OALocalizedString("RefundPreviewConfirmation_title_cancellation_incomplete", comment: nil)
}

public var KVHR_Miles_Driven_Title: String {
    OALocalizedString("VHR_Miles_Driven_Title", comment: nil)
}

public var KVHR_Kilometers_Driven_Title: String {
    OALocalizedString("VHR_Kilometers_Driven_Title", comment: nil)
}

// MARK: - Payment Methods
public var kPaymentMethodsSelectService: String {
    OALocalizedString("PaymentMethod_select_service", comment: nil)
}
public var kPaymentMethodsSelectServiceDescription: String {
    OALocalizedString("PaymentMethod_select_service_description", comment: nil)
}
public var kPaymentMethodsSubscriptionsPayments: String {
    OALocalizedString("PaymentMethod_subscriptions_payments", comment: nil)
}
public var kPaymentMethodsUpdated: String {
    OALocalizedString("PaymentMethod_payment_method_updated", comment: nil)
}
public var kPaymentMethodsManageCardOptions: String {
    OALocalizedString("PaymentMethod_manage_card_options", comment: nil)
}
public var kPaymentMethodsEditCard: String {
    OALocalizedString("PaymentMethod_edit_card", comment: nil)
}
public var kPaymentMethodsMakeDefault: String {
    OALocalizedString("PaymentMethod_make_default", comment: nil)
}
public var kPaymentMethodsDefaultCard: String {
    OALocalizedString("PaymentMethod_default_card", comment: nil)
}
public var kPaymentMethodsFor: String {
    OALocalizedString("PaymentMethod_payment_method_for", comment: nil)
}
public var kPaymentMethodsAlertTitle: String {
    OALocalizedString("PaymentMethod_no_payments_alert_title", comment: nil)
}
public var kPaymentMethodsAlert: String {
    OALocalizedString("PaymentMethod_no_payments_alert", comment: nil)
}
// MARK: - enum

public var kRequestInProgress: String {
    OALocalizedString("Common_request_in_progress", comment: nil)
}
public var kNotSupportedOnJailBrokenDevices: String {
    OALocalizedString("Common_Not_Supported_On_This_Device", comment: nil)
}
public var kNotSupported: String {
    OALocalizedString("Common_Not_Supported", comment: nil)
}
public let kRemote_services_disabled = "remote_services_disabled"
public let kRemote_services_disabled_spanish = "remote_services_disabled_es"
public let kRemote_services_disabled_french = "remote_services_disabled_fr"
public let kRemote_services_disabled_unknownFailed = "remote_services_disabled_unknownFailed"
public let kRemote_services_disabled_pending = "remote_services_disabled_pending"
public let kRemote_services_disabled_expired = "remote_services_disabled_expired"
public let kRemote_services_svl = "remote_services_svl"

// MARK: Analytics
public let kAnalyticsFlowCorrelationId = "flowCorrelationId"
public let kTFSAccountType = "tfsAccountType"
public let kTFSPaymentAmount = "paymentAmount"
public let kSelectedVehicleBrand = "selectedVehicleBrand"
public let kTFSSignInType = "signInType"
public let kTFSignInTypeLinkValue = "link"
public let kTFSSignInTypeFirstLinkValue = "first_link"
public let kTFSSignInTypeValidateValue = "validate"
public let kEventName = "eventName"
public let kEventError = "eventErrorName"

// MARK: Analytics Param Strings
// any changes to these may lose match to previous data
public let kBiometricSignIn = "biometric-Signin"
public let kInvalidGrant = "InvalidGrant"
public let kDefaultError = "default-error"


// MARK: Toyota for Families Model Key
public let kLinkKey = "link"
public let kNameKey = "name"

// MARK: - Parking
public let KParkingConsentId = "A7"

public var kParkingTopBarTitle: String {
    OALocalizedString("Parking_TopBarTitle", comment: nil)
}
public var kParkingListHeaderSubTitleNoData: String {
    OALocalizedString("Parking_ListHeaderSubTitle_NoData", comment: nil)
}
public var kParkingFormWhere: String {
    OALocalizedString("Parking_Form_Where", comment: nil)
}
public var kParkingFormStartTime: String {
    OALocalizedString("Parking_Form_StartTime", comment: nil)
}
public var kParkingFormDuration: String {
    OALocalizedString("Parking_Form_Duration", comment: nil)
}
public var kParkingFind: String {
    OALocalizedString("Parking_Find", comment: nil)
}
public var kParkingStartTimePickerTitle: String {
    OALocalizedString("Parking_StartTimePickerTitle", comment: nil)
}
public var kParkingStartTimePickerButtonTitle: String {
    OALocalizedString("Parking_StartTimePickerButtonTitle", comment: nil)
}
public var kParkingEndTimePickerTitle: String {
    OALocalizedString("Parking_EndTimePickerTitle", comment: nil)
}
public var kParkingEndTimePickerButtonTitle: String {
    OALocalizedString("Parking_EndTimePickerButtonTitle", comment: nil)
}
public var kParkingTimeNow: String {
    OALocalizedString("Parking_TimeNow", comment: nil)
}
public var kParkingLocationNearMe: String {
    OALocalizedString("Parking_LocationNearMe", comment: nil)
}
public var kParkingSearchNotFound: String {
    OALocalizedString("Parking_Search_Not_Found", comment: nil)
}
public var kParkingFilterNotFound: String {
    OALocalizedString("Parking_Filter_Not_Found", comment: nil)
}
public var kParkingSearchPlaceholder: String {
    OALocalizedString("Parking_SearchPlaceholder", comment: nil)
}
// Parking Terms & Conditions
public var kParkingTermsAndConditionsAgreeAndContinue: String {
    OALocalizedString("Parking_Terms&Conditions_Agree&Continue", comment: nil)
}
public var kParkingTermsAndConditionsTopBarTitle: String {
    OALocalizedString("Parking_Terms&Conditions_TopBarTitle", comment: nil)
}
public var kParkingTermsAndConditionsTitle: String {
    OALocalizedString("Parking_Terms&Conditions_Title", comment: nil)
}
public var kParkingBrandingPoweredBy: String {
    OALocalizedString("Parking_Branding_Poweredby", comment: nil)
}
// Parking Reserve Spot
public var kParkingManageSpotTopBarTitle: String {
    OALocalizedString("Parking_ManageSpot_TopBarTitle", comment: nil)
}
public var kParkingCardSavedAlertTitle: String {
    OALocalizedString("Parking_CardSaved_AlertTitle", comment: nil)
}

// Parking Duration Picker
public var kParkingDurationPickerTitle: String {
    OALocalizedString("Parking_DurationPickerTitle", comment: nil)
}
public var kParkingDurationPickerButtonTitle: String {
    OALocalizedString("Parking_DurationPickerButtonTitle", comment: nil)
}
// Parking Location Details
public var kParkingLocationDetailsTopBarTitle: String {
    OALocalizedString("Parking_LocationDetails_TopBarTitle", comment: nil)
}
public var kParkingLocationDetailsReserveButtonTitle: String {
    OALocalizedString("Parking_LocationDetails_ReserveButtonTitle", comment: nil)
}
public var kParkingLocationDetailsAway: String {
    OALocalizedString("Parking_LocationDetails_Away", comment: nil)
}
public var kParkingLocationDetailsMiles: String {
    OALocalizedString("Parking_LocationDetails_Miles", comment: nil)
}
public var kParkingLocationDetailsFeet: String {
    OALocalizedString("Parking_LocationDetails_Feet", comment: nil)
}
public var kParkingLocationDetailsKm: String {
    OALocalizedString("Parking_LocationDetails_Km", comment: nil)
}
public var kParkingLocationDetailsMeter: String {
    OALocalizedString("Parking_LocationDetails_Meter", comment: nil)
}
public var kParkingLocationDetailsInstructions: String {
    OALocalizedString("Parking_LocationDetails_Instructions", comment: nil)
}
public var kParkingLocationDetailsTotalPrice: String {
    OALocalizedString("Parking_LocationDetails_TotalPrice", comment: nil)
}
public var kParkingLocationDetailsUnavailableMessage: String {
    OALocalizedString("Parking_LocationDetails_UnavailableMessage", comment: nil)
}
public var kParkingLocationDetailsNonRevervableMessage: String {
    OALocalizedString("Parking_LocationDetails_NonReservableMessage", comment: nil)
}
public var kParkingLocationDetailsStartTime: String {
    OALocalizedString("Parking_LocationDetails_StartTime", comment: nil)
}
public var kParkingLocationDetailsEndTime: String {
    OALocalizedString("Parking_LocationDetails_EndTime", comment: nil)
}
public var kParkingLocationDetailsPricing: String {
    OALocalizedString("Parking_LocationDetails_Pricing", comment: nil)
}
public var kParkingLocationDetailsHoursOfOperation: String {
    OALocalizedString("Parking_LocationDetails_HoursOfOperation", comment: nil)
}
public var kParkingLocationDetailsStartTimeBeforeEndTime: String {
    OALocalizedString("Parking_LocationDetails_StartTimeBeforeEndTime", comment: nil)
}
public var kParkingLocationDetailsDurationBeyondAllowed: String {
    OALocalizedString("Parking_LocationDetails_DurationBeyondAllowed", comment: nil)
}
// Parking Reservation Details
public var kParkingReservationDetailsDirections: String {
    OALocalizedString("Parking_ReservationDetails_Directions", comment: nil)
}
public var kParkingReservationDetailsAlerts: String {
    OALocalizedString("Parking_ReservationDetails_Alerts", comment: nil)
}
public var kParkingReservationDetailsID: String {
    OALocalizedString("Parking_ReservationDetails_ID", comment: nil)
}
public var kParkingReservationDetailsAddress: String {
    OALocalizedString("Parking_ReservationDetails_Address", comment: nil)
}
public var kParkingReservationDetailsRestrictions: String {
    OALocalizedString("Parking_ReservationDetails_Restrictions", comment: nil)
}
public var kParkingReservationDetailsCancel: String {
    OALocalizedString("Parking_ReservationDetails_Cancel", comment: nil)
}
public var kParkingReservationDetailsCancelationCondition: String {
    OALocalizedString("Parking_ReservationDetails_CancelationCondition", comment: nil)
}
public var kParkingReservationDetailsCancelationNotAllowed: String {
    OALocalizedString("Parking_ReservationDetails_CancelationNotAllowed", comment: nil)
}
public var kParkingReservationDetailsTotalPrice: String {
    OALocalizedString("Parking_ReservationDetails_TotalPrice", comment: nil)
}
public var kParkingReservationDetailsParkingPass: String {
    OALocalizedString("Parking_ReservationDetails_ParkingPass", comment: nil)
}
public var kParkingPass: String {
    OALocalizedString("Parking_ParkingPass", comment: nil)
}
public var kParkingReservationDetailsSetAlert: String {
    OALocalizedString("Parking_ReservationDetails_SetAlert", comment: nil)
}
public var kParkingReservationDetailsNoAlert: String {
    OALocalizedString("Parking_ReservationDetails_NoAlert", comment: nil)
}
public var kParkingReservationDetailsAlertBeforeEnd: String {
    OALocalizedString("Parking_ReservationDetails_AlertBeforeEnd", comment: nil)
}
public var kParkingReservationDetailsAlertTitle: String {
    OALocalizedString("Parking_ReservationDetails_AlertTitle", comment: nil)
}
public var kParkingReservationDetailsAlertMessage: String {
    OALocalizedString("Parking_ReservationDetails_AlertMessage", comment: nil)
}
public var kParkingReservationDetailsAlertMinutes: String {
    OALocalizedString("Parking_ReservationDetails_AlertMinutes", comment: nil)
}
public var kParkingReservationDetailsAlertHour: String {
    OALocalizedString("Parking_ReservationDetails_AlertHour", comment: nil)
}
public var kParkingReservationDetailsAlertHours: String {
    OALocalizedString("Parking_ReservationDetails_AlertHours", comment: nil)
}
public var kParkingReservationDetailsAlertDay: String {
    OALocalizedString("Parking_ReservationDetails_AlertDay", comment: nil)
}
public var kParkingReservationDetailsAlertCancel: String {
    OALocalizedString("Parking_ReservationDetails_AlertCancel", comment: nil)
}
public var kParkingReservationDetailsConfirmCancel: String {
    OALocalizedString("Parking_ReservationDetails_ConfirmCancel", comment: nil)
}
public var kParkingReservationDetailsCancelReservation: String {
    OALocalizedString("Parking_ReservationDetails_CancelReservation", comment: nil)
}
public var kParkingReservationDetailsGoBack: String {
    OALocalizedString("Parking_ReservationDetails_GoBack", comment: nil)
}
public var kParkingReservationDetailsCancelled: String {
    OALocalizedString("Parking_ReservationDetails_Cancelled", comment: nil)
}
public var kParkingHour: String {
    OALocalizedString("Parking_Hour", comment: nil)
}
public var kParkingHours: String {
    OALocalizedString("Parking_Hours", comment: nil)
}
public var kParkingDay: String {
    OALocalizedString("Parking_Day", comment: nil)
}
public var kParkingDays: String {
    OALocalizedString("Parking_Days", comment: nil)
}
public var kParkingMin: String {
    OALocalizedString("Parking_Min", comment: nil)
}
public var kParkingMins: String {
    OALocalizedString("Parking_Mins", comment: nil)
}
public var kParkingNotificationTitle: String {
    OALocalizedString("Pakring_Notification_Title", comment: nil)
}
public var kParkingNotificationEndDescription: String {
    OALocalizedString("Pakring_Notification_End_Description", comment: nil)
}
public var kParkingNotificationStartDescription: String {
    OALocalizedString("Pakring_Notification_Start_Description", comment: nil)
}
// Parking Location List
public var kParkingLocationListReservable: String {
    OALocalizedString("Parking_Location_List_Reservable", comment: nil)
}
public var kParkingLocationListReserve: String {
    OALocalizedString("Parking_Location_List_Reserve", comment: nil)
}
// Parking Add License Plate
public var kParkingAddLicensePlateTopBarTitle: String {
    OALocalizedString("Parking_AddLicensePlate_TopBarTitle", comment: nil)
}
public var kParkingAddLicensePlateTitle: String {
    OALocalizedString("Parking_AddLicensePlate_Title", comment: nil)
}
public var kParkingAddLicensePlateDescription: String {
    OALocalizedString("Parking_AddLicensePlate_Description", comment: nil)
}
public var kParkingAddLicensePlateNickname: String {
    OALocalizedString("Parking_AddLicensePlate_Nickname", comment: nil)
}
public var kParkingAddLicensePlateNumber: String {
    OALocalizedString("Parking_AddLicensePlate_Number", comment: nil)
}
public var kParkingAddLicensePlateState: String {
    OALocalizedString("Parking_AddLicensePlate_State", comment: nil)
}
public var kParkingAddLicensePlateAddButton: String {
    OALocalizedString("Parking_AddLicensePlate_AddButton", comment: nil)
}
// Parking Checkout
public var kParkingCheckoutTopBarTitle: String {
    OALocalizedString("Parking_Checkout_TopBarTitle", comment: nil)
}
public var kParkingCheckoutStartTime: String {
    OALocalizedString("Parking_Checkout_StartTime", comment: nil)
}
public var kParkingCheckoutEndTime: String {
    OALocalizedString("Parking_Checkout_EndTime", comment: nil)
}
public var kParkingCheckoutLicensePlate: String {
    OALocalizedString("Parking_Checkout_LicensePlate", comment: nil)
}
public var kParkingCheckoutPaymentMethod: String {
    OALocalizedString("Parking_Checkout_PaymentMethod", comment: nil)
}
public var kParkingCheckoutCharges: String {
    OALocalizedString("Parking_Checkout_Charges", comment: nil)
}
public var kParkingCheckoutConfirmButton: String {
    OALocalizedString("Parking_Checkout_ConfirmButton", comment: nil)
}
public var kParkingCheckoutNoPaymentSelected: String {
    OALocalizedString("Parking_Checkout_NoPaymentSelected", comment: nil)
}
public var kParkingCheckoutNoPaymentAlertTitle: String {
    OALocalizedString("Parking_Checkout_NoPaymentAlertTitle", comment: nil)
}
public var kParkingCheckoutNoPaymentAlertMessage: String {
    OALocalizedString("Parking_Checkout_NoPaymentAlertMessage", comment: nil)
}
public var kParkingCheckoutNoLicensePlateSelected: String {
    OALocalizedString("Parking_Checkout_NoLicensePlateSelected", comment: nil)
}
public var kParkingCheckoutCardEnding: String {
    OALocalizedString("Parking_Checkout_CardEnding", comment: nil)
}
public var kParkingCheckoutSuccess: String {
    OALocalizedString("Parking_Checkout_Success", comment: nil)
}

// Parking payment methods
public var kParkingPaymentMethodsTopbarTitle: String {
    OALocalizedString("Parking_PaymentMethods_TopBarTitle", comment: nil)
}
public var kParkingPaymentMethodsTitle: String {
    OALocalizedString("Parking_PaymentMethods_Title", comment: nil)
}
public var kParkingPaymentMethodsAddNewCardButton: String {
    OALocalizedString("Parking_PaymentMethods_AddNewCardButton", comment: nil)
}
public var kParkingPaymentMethodsRemoveButton: String {
    OALocalizedString("Parking_PaymentMethods_RemoveButton", comment: nil)
}
public var kParkingPaymentMethodsRemoveMessage: String {
    OALocalizedString("Parking_PaymentMethods_RemoveMessage", comment: nil)
}
public var kParkingPaymentMethodsAddCard: String {
    OALocalizedString("Parking_PaymentMethods_AddCard", comment: nil)
}
public var kParkingPaymentMethodsNoPaymentTitle: String {
    OALocalizedString("Parking_PaymentMethods_NoPayment_Title", comment: nil)
}
public var kParkingPaymentMethodsNoPaymentMessage: String {
    OALocalizedString("Parking_PaymentMethods_NoPayment_Message", comment: nil)
}
public var kParkingPaymentMethodsRemovedSuccessMessage: String {
    OALocalizedString("Parking_PaymentMethods_RemovedSuccess_Message", comment: nil)
}

// Parking license plates
public var kParkingLicensePlatesTopbarTitle: String {
    OALocalizedString("Parking_LicensePlates_TopBarTitle", comment: nil)
}
public var kParkingLicensePlatesTitle: String {
    OALocalizedString("Parking_LicensePlates_Title", comment: nil)
}
public var kParkingLicensePlatesAddLicenseButton: String {
    OALocalizedString("Parking_LicensePlates_AddLicenseButton", comment: nil)
}
public var kParkingLicensePlatesRemoveButton: String {
    OALocalizedString("Parking_LicensePlates_RemoveButton", comment: nil)
}
public var kParkingLicensePlatesRemoveMessage: String {
    OALocalizedString("Parking_LicensePlates_RemoveMessage", comment: nil)
}
public var kParkingLicensePlatesAddLicense: String {
    OALocalizedString("Parking_LicensePlates_AddLicense", comment: nil)
}
public var kParkingLicensePlatesNoLicenseTitle: String {
    OALocalizedString("Parking_LicensePlates_NoLicense_Title", comment: nil)
}
public var kParkingLicensePlatesNoLicenseMessage: String {
    OALocalizedString("Parking_LicensePlates_NoLicense_Message", comment: nil)
}
public var kParkingLicensePlatesRemoveSuccessMessage: String {
    OALocalizedString("Parking_LicensePlates_RemovedSuccess_Message", comment: nil)
}
// Parking Home
public var kParkingReservationsEmptyListTitle: String {
    OALocalizedString("Parking_Reservations_Empty_List_Title", comment: nil)
}
public var kParkingReservationsListTitle: String {
    OALocalizedString("Parking_Reservations_List_Title", comment: nil)
}
public var kParkingTabFindParking: String {
    OALocalizedString("Parking_Tab_FindParking", comment: nil)
}
public var kParkingTabReservations: String {
    OALocalizedString("Parking_Tab_Reservations", comment: nil)
}
// Parking Past Reservation Details
public var kParkingPastReservationDetailsAddress: String {
    OALocalizedString("Parking_Past_Reservation_Details_Address", comment: nil)
}
public var kParkingPastReservationDetailsID: String {
    OALocalizedString("Parking_Past_Reservation_Details_ID", comment: nil)
}
public var kParkingPastReservationDetailsDuration: String {
    OALocalizedString("Parking_Past_Reservation_Details_Duration", comment: nil)
}
public var kParkingPastReservationDetailsTotalCharges: String {
    OALocalizedString("Parking_Past_Reservation_Details_TotalCharges", comment: nil)
}
public var kParkingPastReservationDetailsTopBarTitle: String {
    OALocalizedString("Parking_Past_Reservation_Details_TopBarTitle", comment: nil)
}
// Parking location list
public var kParkingLocationListAvailable: String {
    OALocalizedString("Parking_LocationList_Available", comment: nil)
}
public var kParkingLocationListUnavailable: String {
    OALocalizedString("Parking_LocationList_Unavailable", comment: nil)
}
public var kParkingLocationListTotalPrice: String {
    OALocalizedString("Parking_LocationList_TotalPrice", comment: nil)
}

// Parking Filter
public var kParkingLocationListFilter: String {
    OALocalizedString("Parking_Location_List_Filter", comment: nil)
}
public var kParkingLocationFilterApplyButtonTitle: String {
    OALocalizedString("Parking_Filter_Apply_Button_Title", comment: nil)
}
public var kParkingFilterAvailability: String {
    OALocalizedString("Parking_Filter_Availability", comment: nil)
}
public var kParkingFilterAvailabilityAvailable: String {
    OALocalizedString("Parking_Filter_Availability_Available", comment: nil)
}
public var kParkingFilterAvailabilityAll: String {
    OALocalizedString("Parking_Filter_Availability_All", comment: nil)
}
public var kParkingFilterPriceRange: String {
    OALocalizedString("Parking_Filter_Price_Range", comment: nil)
}

// Parking Help
public var kParkingHelpOptionsTopBarTitle: String {
    OALocalizedString("Parking_Help_Options_TopBarTitle", comment: nil)
}
public var kParkingHelpOptionsInstruction: String {
    OALocalizedString("Parking_Help_Options_Instruction", comment: nil)
}
public var kParkingHelpOptionsContactParkopedia: String {
    OALocalizedString("Parking_Help_Options_Contact_Parkopedia", comment: nil)
}
public var kParkingHelpNeedAdditionalHelp: String {
    OALocalizedString("Parking_Help_Need_Additional_Help", comment: nil)
}
public var kParkingHelpExploreText: String {
    OALocalizedString("Parking_Help_Explore_Text", comment: nil)
}
public var kParkingHelp: String {
    OALocalizedString("Parking_Help", comment: nil)
}
// Prking Help Reservations
public var kParkingHelpReservationsTopBarTitle: String {
    OALocalizedString("Parking_Help_Reservations_TopBarTitle", comment: nil)
}
public var kParkingSkip: String {
    OALocalizedString("Parking_Skip", comment: nil)
}
public var kParkingHelpReservationsTitle: String {
    OALocalizedString("Parking_Help_Reservations_Title", comment: nil)
}
public var kParkingHelpReservationsSubTitle: String {
    OALocalizedString("Parking_Help_Reservations_SubTitle", comment: nil)
}
// Parking Help Topics
public var kParkingHelpTopicsTitle: String {
    OALocalizedString("Parking_Help_Topics_Title", comment: nil)
}
public var kParkingHelpTopicsSubTitle: String {
    OALocalizedString("Parking_Help_Topics_SubTitle", comment: nil)
}
// Parking Help Review
public var kParkingHelpReviewTitle: String {
    OALocalizedString("Parking_Help_Review_Title", comment: nil)
}
public var kParkingHelpReviewSelectReservation: String {
    OALocalizedString("Parking_Help_Review_Select_Reservation", comment: nil)
}
public var kParkingHelpReviewSelectTopic: String {
    OALocalizedString("Parking_Help_Review_Select_Topic", comment: nil)
}
public var kParkingHelpReviewEmail: String {
    OALocalizedString("Parking_Help_Review_Email", comment: nil)
}
public var kParkingHelpReviewTellUsMore: String {
    OALocalizedString("Parking_Help_Review_Tell_Us_More", comment: nil)
}
public var kParkingHelpReviewSend: String {
    OALocalizedString("Parking_Help_Review_Send", comment: nil)
}
// Parking Help Confirmation
public var kParkingHelpConfirmationTitle: String {
    OALocalizedString("Parking_Help_Confirmation_Title", comment: nil)
}
public var kParkingHelpConfirmationMessage: String {
    OALocalizedString("Parking_Help_Confirmation_Message", comment: nil)
}
public var kParkingHelpConfirmationReturnButton: String {
    OALocalizedString("Parking_Help_Confirmation_Return_Button", comment: nil)
}
public var kConnectedServicesTitle: String {
    OALocalizedString("kConnected_Services_Title", comment: nil)
}
public var kPrivacyVehicleListSubTitle: String {
    OALocalizedString("kPrivacy_Vehicle_List_SubTitle", comment: nil)
}
public var kprivacyDateAccepted: String {
    OALocalizedString("kprivacy_Date_Accepted", comment: nil)
}
public var kprivacyDateDeclined: String {
    OALocalizedString("kprivacy_Date_Declined", comment: nil)
}
public var kNewRefundPreviewMessageCancellationSuccessWithRefund: String {
    OALocalizedString("KNewRefundBanner_message_cancellation_success_with_refund", comment: nil)
}

// Parking Dashboard Card
public var kActiveParkingTimeRemainingTitle: String {
    OALocalizedString("Parking_Dashboard_Card_Active_Time_Remaining", comment: nil)
}

public var kActiveParkingTimeRemainingDaysSubtext: String {
    OALocalizedString("Parking_Dashboard_Card_Active_Days_Subtext", comment: nil)
}

public var kActiveParkingTimeRemainingDaySubtext: String {
    OALocalizedString("Parking_Dashboard_Card_Active_Day_Subtext", comment: nil)
}

public var kActiveParkingTimeRemainingHoursSubtext: String {
    OALocalizedString("Parking_Dashboard_Card_Active_Hours_Subtext", comment: nil)
}

public var kActiveParkingTimeRemainingHourSubtext: String {
    OALocalizedString("Parking_Dashboard_Card_Active_Hour_Subtext", comment: nil)
}

public var kActiveParkingTimeRemainingMinutesSubtext: String {
    OALocalizedString("Parking_Dashboard_Card_Active_Minutes_Subtext", comment: nil)
}

public var kUpcomingParkingCardDetailTitle: String {
    OALocalizedString("Parking_Dashboard_Card_Upcoming_Parking_Title", comment: nil)
}

// MARK: Hidden Dashboard Cards
public var kHiddenDashboardCardsTopBarTitle: String {
    OALocalizedString("Hidden_Dashboard_Cards_TopBarTitle", comment: nil)
}

public var kEmptyHiddenDashbordCardsMessage: String {
    OALocalizedString("Empty_Hidden_Dashboard_Cards_Message", comment: nil)
}

// MARK: - Create Reset PIN
public var kPINCreateTitle: String {
    OALocalizedString("PIN_create_title", comment: nil)
}
public var kPINCreateNew: String {
    OALocalizedString("PIN_create_new", comment: nil)
}
public var kPINCreateDescription: String {
    OALocalizedString("PIN_create_description", comment: nil)
}
public var kPINCreateDescriptionSubaru: String {
    OALocalizedString("PIN_create_description_subaru", comment: nil)
}
public var kPINConfirmTitle: String {
    OALocalizedString("PIN_confirm_title", comment: nil)
}
public var kPINConfirmDescription: String {
    OALocalizedString("PIN_confirm_description", comment: nil)
}
public var kPINNotMutchError: String {
    OALocalizedString("PIN_not_mutch_error", comment: nil)
}
public var kPINSuccessfullyCreated: String {
    OALocalizedString("PIN_successfully_created", comment: nil)
}

public var kPINSuccessfullyReset: String {
    OALocalizedString("PIN_successfully_reset", comment: nil)
}

public var kPINFailingCreated: String {
    OALocalizedString("PIN_failing_created", comment: nil)
}
public var kPINConfirmTitleReset: String {
    OALocalizedString("PIN_confirm_title_reset", comment: nil)
}
public var kPINConfirmDescriptionReset: String {
    OALocalizedString("PIN_confirm_description_reset", comment: nil)
}
public var kPINCreateTitleReset: String {
    OALocalizedString("PIN_create_title_reset", comment: nil)
}
public var kPINCreateDescriptionReset: String {
    OALocalizedString("PIN_create_description_reset", comment: nil)
}
public var kPINResetButtonTitle: String {
    OALocalizedString("PIN_reset_PIN_button_title", comment: nil)
}
public var kPINResetFlowTitle: String {
    OALocalizedString("PIN_reset_flow_title", comment: nil)
}
public var kPINSetupFlowTitle: String {
    OALocalizedString("PIN_setup_flow_title", comment: nil)
}
public var kPINResetInfoTitle: String {
    OALocalizedString("PIN_reset_info_title", comment: nil)
}
public var kPINResetInfoDescription: String {
    OALocalizedString("PIN_reset_info_description", comment: nil)
}
public var kPINConfirmationFailedAttempts: String {
    OALocalizedString("PIN_confirmation_failed_attempts", comment: nil)
}
public var kPINResetConfirmationFailedAttempts: String {
    OALocalizedString("PIN_reset_confirmation_failed_attempts", comment: nil)
}
public var kPINUserIdNotMuch: String {
    OALocalizedString("PIN_userId_not_much", comment: nil)
}
public var kPINSecuritySettingsTitle: String {
    OALocalizedString("PIN_Account_Settings_Title", comment: nil)
}
public var kPINSecuritySettingsDescription: String {
    OALocalizedString("PIN_Account_Settings_Description", comment: nil)
}
public var kPINResetInstruction1: String {
    OALocalizedString("PIN_reset_instruction1", comment: nil)
}
public var kPINResetInstruction2: String {
    OALocalizedString("PIN_reset_instruction2", comment: nil)
}
public var kPINResetInstruction3: String {
    OALocalizedString("PIN_reset_instruction3", comment: nil)
}
public var kPINAlertMessage: String {
    OALocalizedString("PIN_alert_message", comment: nil)
}
public var kPINVerificationNeededTitle: String {
    OALocalizedString("PIN_verification_needed_title", comment: nil)
}
public var kPINVerificationNeededMessage: String {
    OALocalizedString("PIN_verification_needed_message", comment: nil)
}
// MARK: - Accessibility Voice Over Text
// MARK: Singup Screen
public var kToggle: String {
    OALocalizedString("Toggle", comment: nil)
}
public var kMode: String {
    OALocalizedString("mode", comment: nil)
}
public var kSelector: String {
    OALocalizedString("Selector", comment: nil)
}
public var kSelected: String {
    OALocalizedString("Selected", comment: nil)
}
public var kUnselected: String {
    OALocalizedString("Unselected", comment: nil)
}
public var kIs: String {
    OALocalizedString("Is", comment: nil)
}
public var kVehicleStatus: String {
    OALocalizedString("Vehicle status", comment: nil)
}
public var kVehicle: String {
    OALocalizedString("Vehicle", comment: nil)
}
public var kTab: String {
    OALocalizedString("Tab", comment: nil)
}
public var kApp: String {
    OALocalizedString("App", comment: nil)
}
public var kViewProfileImage: String {
    OALocalizedString("View profile image", comment: nil)
}
public var kEditProfileImage: String {
    OALocalizedString("Edit profile image", comment: nil)
}
public var kFeedbackType: String {
    OALocalizedString("Feedback type", comment: nil)
}
public var kFeedbackCategory: String {
    OALocalizedString("Feedback category", comment: nil)
}
public var kReceived: String {
    OALocalizedString("Received", comment: nil)
}
public var kNotification: String {
    OALocalizedString("Notification", comment: nil)
}
public var kEnter: String {
    OALocalizedString("Enter", comment: nil)
}

// MARK: Add Vehicle Screen
public var kAddVehicleVINScan: String {
    OALocalizedString("AddVehicle_VIN_Scan", comment: nil)
}
public var kAddVehicleVINTextField: String {
    OALocalizedString("AddVehicle_VIN_TextField", comment: nil)
}
public var kAddVehicleVINLocationImage: String {
    OALocalizedString("AddVehicle_VIN_Location_Image", comment: nil)
}
public var kAddVehicleInvalidVINDetailsError: String {
    OALocalizedString("AddVehicle_Invalid_VIN_DetailsError", comment: nil)
}
public var kAddVehicleEnterNickName: String {
    OALocalizedString("AddVehicle_Enter_Nick_Name", comment: nil)
}
public var kAddVehicleClearNickName: String {
    OALocalizedString("AddVehicle_Clear_Nick_Name", comment: nil)
}

public var KCombinedDataMasterConsentsheaderTitle: String {
    OALocalizedString("Combined_Data_Master_Consents_header_Title", comment: nil)
}
public var kprivacymasterconsentdeclinedalertsubtitle: String {
    OALocalizedString("kprivacy_master_consent_declined_alert_subtitle", comment: nil)
}
public var kprivacyconsentdeclinedalertsubtitle: String {
    OALocalizedString("kprivacy_consent_declined_alert_subtitle", comment: nil)
}
public var kprivacy_consent_declined_alert_Return: String {
    OALocalizedString("kprivacy_consent_declined_alert_Return", comment: nil)
}
public var KManageSubscriptionPageAlertString: String {
    OALocalizedString("KManageSubscriptionPage_Alert_String", comment: nil)
}
public var kSelectAVehicle: String {
    OALocalizedString("edit_consnet_select_a_vehicle", comment: nil)
}
public var kPrivacyPortalWiFiSubscriptionAlertTitle: String {
    OALocalizedString("Privacy_Portal_Wi-Fi_Subscription_Alert_Title", comment: nil)
}
public var kPrivacyPortalWiFiSubscriptionAlertDescription: String {
    OALocalizedString("Privacy_Portal_Wi-Fi_Subscription_Alert_Description", comment: nil)
}

public var PrivacyPortalWiFiSubscriptionAlertButtonVerizonTitle: String {
    OALocalizedString("Privacy_Portal_Wi-Fi_Subscription_Alert_Button_Verizon_Title", comment: nil)
}
public var PrivacyPortalWiFiSubscriptionAlertButtonATTTitle: String {
    OALocalizedString("Privacy_Portal_Wi-Fi_Subscription_Alert_Button_AT&T_Title", comment: nil)
}
public var PrivacyPortalWiFiSubscriptionerrordescription: String {
    OALocalizedString("Privacy_Portal_Wi-Fi_Subscription_error_description", comment: nil)
}
public var KUpdateMDCTitle: String {
    OALocalizedString("MDC_Update_title", comment: nil)
}
public var KUpdateMDCSubTitle: String {
    OALocalizedString("MDC_Update_subtitle", comment: nil)
}
public var KDataConsentTitle: String {
    OALocalizedString("MDC_Data_Consent_title", comment: nil)
}
public var KFleetVehicleHeaderTitle: String {
    OALocalizedString("Fleet_Vehicle_HeaderTitle", comment: nil)
}
public var KFleetVehicleTitle: String {
    OALocalizedString("Fleet_Vehicle_Title", comment: nil)
}
public var KFleetVehicleSubTitle: String {
    OALocalizedString("Fleet_Vehicle_SubTitle", comment: nil)
}

public var KCommonConsentDeclined: String {
    OALocalizedString("Common_Consent_Declined", comment: nil)
}
public var KCommonConsentAccepted: String {
    OALocalizedString("Common_Consent_Accepted", comment: nil)
}
public var KAcceptedSafetySense: String {
    OALocalizedString("Accepted_Safety_Sense", comment: nil)
}
public var KDeclinedSafetySense: String {
    OALocalizedString("Declined_Safety_Sense", comment: nil)
}

public var KBackToDataPrivacyPortal: String {
    OALocalizedString("Back_To_Data_Privacy_Portal", comment: nil)
}
// MARK: SMS
public var KSMSoptOutConfirmScreen: String {
    OALocalizedString("SMS_opt_out_confirm_screen", comment: nil)
}

public var KSMSoptInConfirmScreen: String {
    OALocalizedString("SMS_opt_in_confirm_screen", comment: nil)
}

// MARK: Siri
public var kSiriAvailableShortcuts: String {
    OALocalizedString("Siri_Available_Shortcuts", comment: nil)
}
public var kSiriDisclaimer: String {
    OALocalizedString("Siri_Disclaimer", comment: nil)
}
public var kSiriShortcuts: String {
    OALocalizedString("Siri_Shortcuts", comment: nil)
}
public var kSiriRemoteError: String {
    OALocalizedString("Siri_Remote_Error", comment: nil)
}
public var kSiriRemoteStartError: String {
    OALocalizedString("Siri_Remote_Start_Error", comment: nil)
}
public var kSiriRemoteLockError: String {
    OALocalizedString("Siri_Remote_Lock_Error", comment: nil)
}
public var kSiriStartCar: String {
    OALocalizedString("Siri_Start_Car", comment: nil)
}
public var kSiriStopCar: String {
    OALocalizedString("Siri_Stop_Car", comment: nil)
}
public var kSiriLockCar: String {
    OALocalizedString("Siri_Lock_Car", comment: nil)
}
public var kSiriUnlockCar: String {
    OALocalizedString("Siri_Unlock_Car", comment: nil)
}
public var kSiriStartCarTitle: String {
    OALocalizedString("Siri_Start_Car_Title", comment: nil)
}
public var kSiriStopCarTitle: String {
    OALocalizedString("Siri_Stop_Car_Title", comment: nil)
}
public var kSiriLockCarTitle: String {
    OALocalizedString("Siri_Lock_Car_Title", comment: nil)
}
public var kSiriUnlockCarTitle: String {
    OALocalizedString("Siri_Unlock_Car_Title", comment: nil)
}
public var kSiriStartCarDesc: String {
    OALocalizedString("Siri_Start_Car_Desc", comment: nil)
}
public var kSiriStopCarDesc: String {
    OALocalizedString("Siri_Stop_Car_Desc", comment: nil)
}
public var kSiriLockCarDesc: String {
    OALocalizedString("Siri_Lock_Car_Desc", comment: nil)
}
public var kSiriUnlockCarDesc: String {
    OALocalizedString("Siri_Unlock_Car_Desc", comment: nil)
}
public var kSiriDefaultVehicleChange: String {
    OALocalizedString("Siri_Default_Vehicle_Change", comment: nil)
}
// MARK: - My Destinations
public var kMyDestinations: String {
    OALocalizedString("MyDestinations", comment: nil)
}
public var kMyDestinationsHome: String {
    OALocalizedString("MyDestinations_home", comment: nil)
}
public var kMyDestinationsSetHome: String {
    OALocalizedString("MyDestinations_set_home", comment: nil)
}
public var kMyDestinationsWork: String {
    OALocalizedString("MyDestinations_work", comment: nil)
}
public var kMyDestinationsSetWork: String {
    OALocalizedString("MyDestinations_set_work", comment: nil)
}
public var kMyDestinationsFavorites: String {
    OALocalizedString("MyDestinations_favorites", comment: nil)
}
public var kMyDestinationsFavoritesReachMaxTitle: String {
    OALocalizedString("MyDestinations_favorites_reach_max_title", comment: nil)
}
public var kMyDestinationsFavoritesReachMaxDetails: String {
    OALocalizedString("MyDestinations_favorites_reach_max_details", comment: nil)
}
public var kMyDestinationsViewDestinations: String {
    OALocalizedString("MyDestinations_view_destinations", comment: nil)
}
public var kMyDestinationsSearchTitleSetHome: String {
    OALocalizedString("MyDestinations_search_title_set_home", comment: nil)
}
public var kMyDestinationsSearchTitleSetWork: String {
    OALocalizedString("MyDestinations_search_title_set_work", comment: nil)
}
public var kMyDestinationsSearchTitle: String {
    OALocalizedString("MyDestinations_search_title", comment: nil)
}
public var kMyDestinationsSearchPlaceholderHome: String {
    OALocalizedString("MyDestinations_search_home_placeholder", comment: nil)
}
public var kMyDestinationsSearchPlaceholderWork: String {
    OALocalizedString("MyDestinations_search_work_placeholder", comment: nil)
}
public var kMyDestinationsSearchPlaceholderFavorites: String {
    OALocalizedString("MyDestinations_search_favorites_placeholder", comment: nil)
}
public var kMyDestinationsRecents: String {
    OALocalizedString("MyDestinations_recents", comment: nil)
}
public var kMyDestinationsSetUp: String {
    OALocalizedString("MyDestinations_set_up", comment: nil)
}
public var kMyDestinationsMenuUpdateHome: String {
    OALocalizedString("MyDestinations_menu_update_home", comment: nil)
}
public var kMyDestinationsMenuRemoveHome: String {
    OALocalizedString("MyDestinations_menu_remove_home", comment: nil)
}
public var kMyDestinationsMenuUpdateWork: String {
    OALocalizedString("MyDestinations_menu_update_work", comment: nil)
}
public var kMyDestinationsMenuRemoveWork: String {
    OALocalizedString("MyDestinations_menu_remove_work", comment: nil)
}
public var kMyDestinationsMenuViewDetails: String {
    OALocalizedString("MyDestinations_menu_view_details", comment: nil)
}
public var kMyDestinationsSaveDestination: String {
    OALocalizedString("MyDestinations_save_destination", comment: nil)
}
public var kMyDestinationsSaveAsHome: String {
    OALocalizedString("MyDestinations_save_as_home", comment: nil)
}
public var kMyDestinationsSaveAsWork: String {
    OALocalizedString("MyDestinations_save_as_work", comment: nil)
}
public var kMyDestinationsSaveAsFavorite: String {
    OALocalizedString("MyDestinations_save_as_favorite", comment: nil)
}
public var kMyDestinationsMyFavoriteEmptyTitle: String {
    OALocalizedString("MyDestinations_my_favorite_empty_title", comment: nil)
}
public var kMyDestinationsMyFavoriteEmptySubTitle: String {
    OALocalizedString("MyDestinations_my_favorite_empty_subtitle", comment: nil)
}
public var kMyDestinationsSentToCarEmptyTitle: String {
    OALocalizedString("MyDestinations_sent_to_car_empty_title", comment: nil)
}
public var kMyDestinationsSentToCarEmptySubTitle: String {
    OALocalizedString("MyDestinations_sent_to_car_empty_subtitle", comment: nil)
}
public var kMyDestinationsNoResultTitle: String {
    OALocalizedString("MyDestinations_no_result_title", comment: nil)
}
public var kMyDestinationsNoResultMessage: String {
    OALocalizedString("MyDestinations_no_result_message", comment: nil)
}
public var kMyDestinationsRemoveConfirmTitle: String {
    OALocalizedString("MyDestinations_remove_confirm_title", comment: nil)
}
public var kMyDestinationsRemoveConfirmBody: String {
    OALocalizedString("MyDestinations_remove_confirm_body", comment: nil)
}
public var kMyDestinationsSentToCarRemoveConfirmTitle: String {
    OALocalizedString("MyDestinations_sent_to_car_remove_confirm_title", comment: nil)
}
public var kMyDestinationsSentToCarRemoveConfirmBody: String {
    OALocalizedString("MyDestinations_sent_to_car_remove_confirm_body", comment: nil)
}
public var kPOISendToCarErrorTitle: String {
    OALocalizedString("POI_send_to_car_error_title", comment: nil)
}
public var kPOISendToCarErrorDescription: String {
    OALocalizedString("POI_send_to_car_error_description", comment: nil)
}
public var kPOISendToCarSuccessMessage: String {
    OALocalizedString("POI_send_to_car_success_message", comment: nil)
}
public var kPOIDetails: String {
    OALocalizedString("POI_details_title", comment: nil)
}
public var kPOISanitizerLocationNotFound: String {
    OALocalizedString("POI_sanitizer_location_not_found", comment: nil)
}
public var kMyDestinationsSendToCar: String {
    OALocalizedString("MyDestinations_send_to_car", comment: nil)
}
public var kMyDestinationsSentToCar: String {
    OALocalizedString("MyDestinations_sent_to_car", comment: nil)
}
public var kMyDestinationsDescription: String {
    OALocalizedString("Dashboard_MyDestinations_Description", comment: nil)
}
public var kMyDestinationsEmptyDashboardCardBody: String {
    OALocalizedString("MyDestinations_Empty_card_body", comment: nil)
}
public var kPOISanitizerErrorTitle: String {
    OALocalizedString("POI_sanitizer_error_title", comment: nil)
}
public var kPOISanitizerErrorDescription: String {
    OALocalizedString("POI_sanitizer_error_description", comment: nil)
}
public var kPOISharedLoginErrorTitle: String {
    OALocalizedString("POI_shared_login_error_title", comment: nil)
}
public var kPOISharedLoginErrorDescription: String {
    OALocalizedString("POI_shared_login_error_description", comment: nil)
}

// MARK: - Timestamp
public var kYearsAgo: String {
    OALocalizedString("Timestamp_years_ago", comment: nil)
}
public var kOneYearAgo: String {
    OALocalizedString("Timestamp_one_year_ago", comment: nil)
}
public var kMonthsAgo: String {
    OALocalizedString("Timestamp_months_ago", comment: nil)
}
public var kOneMonthAgo: String {
    OALocalizedString("Timestamp_one_month_ago", comment: nil)
}
public var kWeeksAgo: String {
    OALocalizedString("Timestamp_weeks_ago", comment: nil)
}
public var kOneWeekAgo: String {
    OALocalizedString("Timestamp_one_week_ago", comment: nil)
}
public var kDaysAgo: String {
    OALocalizedString("Timestamp_days_ago", comment: nil)
}
public var kOneDayAgo: String {
    OALocalizedString("Timestamp_one_day_ago", comment: nil)
}
public var kHoursAgo: String {
    OALocalizedString("Timestamp_hours_ago", comment: nil)
}
public var kOneHourAgo: String {
    OALocalizedString("Timestamp_one_hour_ago", comment: nil)
}
public var kMinutesAgo: String {
    OALocalizedString("Timestamp_minutes_ago", comment: nil)
}
public var kOneMinuteAgo: String {
    OALocalizedString("Timestamp_one_minute_ago", comment: nil)
}
public var kSecondsAgo: String {
    OALocalizedString("Timestamp_seconds_ago", comment: nil)
}
public var kOneSecondAgo: String {
    OALocalizedString("Timestamp_one_second_ago", comment: nil)
}
// MARK: - Dashboard Empty
public var kEmptyDashboardHeaderTitle: String {
    OALocalizedString("Empty_dashboard_header_title", comment: nil)
}
// MARK: - Dashboard PaymentDeferral
public var kPaymentDeferralTitle: String {
    OALocalizedString("Payment_deferral_title", comment: nil)
}
public var kPaymentDeferralDescription: String {
    OALocalizedString("Payment_deferral_description", comment: nil)
}
// MARK: - Dashboard FinancialServices
public var kFinancialServicesCardTitle: String {
    OALocalizedString("Financial_services_card_title", comment: nil)
}
public var kFinancialServicesCardBodyTitle: String {
    OALocalizedString("Financial_services_card_body_title", comment: nil)
}
public var kFinancialServicesCardBodyDescription: String {
    OALocalizedString("Financial_services_card_body_description", comment: nil)
}
public var kFinancialServicesCardLinkAccountNow: String {
    OALocalizedString("Financial_services_card_link_account_now", comment: nil)
}
// MARK: - Dashboard ExploreNewLineup
public var kExploreNewLineupTitle: String {
    OALocalizedString("Explore_new_lineup_title", comment: nil)
}
public var kExploreNewLineupDescription: String {
    OALocalizedString("Explore_new_lineup_description", comment: nil)
}
public var kExploreOurVehicles: String {
    OALocalizedString("Explore_our_vehicles", comment: nil)
}
// MARK: - QR Vehicle registration
public var kQRVehiclePendingRegistrationTitle: String {
    OALocalizedString("QR_vehicle_pending_registration_title", comment: nil)
}
public var kQRVehicleFailedRegistrationTitle: String {
    OALocalizedString("QR_vehicle_failed_registration_title", comment: nil)
}
public var kQRVehicleCodeFailedTitle: String {
    OALocalizedString("QR_vehicle_code_failed_title", comment: nil)
}
public var kQRVehicleFailedRegistrationMessage: String {
    OALocalizedString("QR_vehicle_failed_registration_message", comment: nil)
}
public var kQRVehicleFailedRegistrationMessageLexus: String {
    OALocalizedString("QR_vehicle_failed_registration_message_lexus", comment: nil)
}
public var kQRVehicleFailedRegistrationMessageSubaru: String {
    OALocalizedString("QR_vehicle_failed_registration_message_subaru", comment: nil)
}
public var kQRVehicleLinkFailedRegistrationMessage: String {
    OALocalizedString("QR_vehicle_link_failed_registration_message", comment: nil)
}
public var kQRVehicleLinkFailedRegistrationMessageLexus: String {
    OALocalizedString("QR_vehicle_link_failed_registration_message_lexus", comment: nil)
}
public var kQRVehicleLinkFailedRegistrationMessageSubaru: String {
    OALocalizedString("QR_vehicle_link_failed_registration_message_subaru", comment: nil)
}
public var kQRIncorrectUserTitle: String {
    OALocalizedString("QR_incorrect_account_failure_title", comment: nil)
}
public var kQRIncorrectUserMessage: String {
    OALocalizedString("QR_incorrect_account_failure_description", comment: nil)
}
public var kQRIncorrectUserMessageLexus: String {
    OALocalizedString("QR_incorrect_account_failure_description_lexus", comment: nil)
}
public var kQRIncorrectUserMessageSubaru: String {
    OALocalizedString("QR_incorrect_account_failure_description_subaru", comment: nil)
}
public var kQRScanInstructions: String {
    OALocalizedString("QR_vehicle_scan_qr_instructions", comment: nil)
}
public var kQRScanManualInstructions: String {
    OALocalizedString("QR_vehicle_scan_manual_instructions", comment: nil)
}
public var kQRPermissionsTitle: String {
    OALocalizedString("QR_vehicle_scan_qr_permissions_title", comment: nil)
}
public var kQRPermissionsSubtitle: String {
    OALocalizedString("QR_vehicle_scan_qr_permissions_subtitle", comment: nil)
}
public var kQRPermissionsCameraAccess: String {
    OALocalizedString("QR_vehicle_scan_qr_permissions_camera_access", comment: nil)
}
public var kQRPermissionsEnterManualCode: String {
    OALocalizedString("QR_vehicle_scan_qr_permissions_manual_code", comment: nil)
}
public var kQRScanQRButton: String {
    OALocalizedString("QR_vehicle_scan_qr_scan_code", comment: nil)
}
public var kQRScanQRtitleSubContent: String {
    OALocalizedString("QR_vehicle_scan_qr_scan_code_titleSubContent", comment: nil)
}
public var kQRScanQRtitleContent: String {
    OALocalizedString("QR_vehicle_scan_qr_scan_code_titleContent", comment: nil)
}
public var kQRScanQRBotomContent: String {
    OALocalizedString("QR_vehicle_scan_qr_scan_code_botomContent", comment: nil)
}
public var kQRScanManualButton: String {
    OALocalizedString("QR_vehicle_code_failed_manual_button", comment: nil)
}
public var kQRConnectByCodeButton: String {
    OALocalizedString("QR_vehicle_connect_by_code_code", comment: nil)
}
public var kQRScanQRInstructionsTitle: String {
    OALocalizedString("QR_vehicle_qr_instrutions_title", comment: nil)
}
public var kQRScanQRInstructionsBody: String {
    OALocalizedString("QR_vehicle_qr_instrutions_body", comment: nil)
}
public var kQRScanQRInstructionsSelectedWords: String {
    OALocalizedString("QR_vehicle_qr_instrutions_selected_words", comment: nil)
}
public var kQRScanQRInstructionsTitle2: String {
    OALocalizedString("QR_vehicle_qr_instrutions_title2", comment: nil)
}
public var kQRScanQRInstructionsBody2: String {
    OALocalizedString("QR_vehicle_qr_instrutions_body2", comment: nil)
}
public var kQRScanQRInstructionsSelectedWords2: String {
    OALocalizedString("QR_vehicle_qr_instrutions_selected_words2", comment: nil)
}
public var kQRScanInstructionsNoFind: String {
    OALocalizedString("QR_vehicle_qr_instructions_no_find", comment: nil)
}
public var kQRAddVehicleScanVinButton: String {
    OALocalizedString("QR_vehicle_scan_vin_button", comment: nil)
}
public var kQRAddVehicleManualVinEntryButton: String {
    OALocalizedString("QR_vehicle_manual_input_vin_button", comment: nil)
}
public var kQRAddVehicleVinInstructionsBody: String {
    OALocalizedString("QR_vehicle_vin_instructions_body", comment: nil)
}
public var kRemoteActivationFailedTitle: String {
    OALocalizedString("RemoteActivation_failed_registration_title", comment: nil)
}
public var kRecentPurchaseQRScanBody: String {
    OALocalizedString("QR_vehicle_recent_qr_body", comment: nil)
}
public var kQRVehicleCapable: String {
    OALocalizedString("QR_vehicle_capable", comment: nil)
}
public var kQRVehicleCapableTest: String {
    OALocalizedString("QR_vehicle_capable_test", comment: nil)
}
public var kTooltipMessageEnterVinManuallyToyota: String {
    OALocalizedString("Tooltip_Message_Enter_Vin_Manually_Toyota", comment: nil)
}
public var kTooltipMessageScanQRToyota: String {
    OALocalizedString("Tooltip_Message_Scan_QR_Toyota", comment: nil)
}
public var kTooltipMessageEnterVinManuallyLexus: String {
    OALocalizedString("Tooltip_Message_Enter_Vin_Manually_Lexus", comment: nil)
}
public var kTooltipMessageScanQRLexus: String {
    OALocalizedString("Tooltip_Message_Scan_QR_Lexus", comment: nil)
}
public var kTooltipMessageEnterVinManuallySubaru: String {
    OALocalizedString("Tooltip_Message_Enter_Vin_Manually_Subaru", comment: nil)
}
public var kTooltipMessageScanQRSubaru: String {
    OALocalizedString("Tooltip_Message_Scan_QR_Subaru", comment: nil)
}
// MARK: - QR RemoteVerification
var kRemoteVerificationInstructionsButtonTitle: String {
    OALocalizedString("RemoteVerification_instructions_button_title", comment: nil)
}
var kRemoteVerificationSkipButtonTitle: String {
    OALocalizedString("RemoteVerification_skip_button_title", comment: nil)
}
var kRemoteVerificationUserNavbarTitle: String {
    OALocalizedString("RemoteVerification_user_activation_navbar_title", comment: nil)
}
var kRemoteVerificationUserActivationTitle: String {
    OALocalizedString("RemoteVerification_user_activation_title", comment: nil)
}
var kRemoteVerificationUserActivationSubtitle: String {
    OALocalizedString("RemoteVerification_user_activation_subtitle", comment: nil)
}
var kRemoteVerificationGuestActivationNavbarTitle: String {
    OALocalizedString("RemoteVerification_guest_activation_navbar_title", comment: nil)
}
var kRemoteVerificationGuestActivationTitle: String {
    OALocalizedString("RemoteVerification_guest_activation_title", comment: nil)
}
var kRemoteVerificationGuestActivationSubtitle: String {
    OALocalizedString("RemoteVerification_guest_activation_subtitle", comment: nil)
}
var kRemoteVerificationInstructionsNavTitle: String {
    OALocalizedString("RemoteVerification_instructions_navbar_title", comment: nil)
}
var kGuestRemoteVerificationInstructionsNavTitle: String {
    OALocalizedString("RemoteVerification_guest_instructions_navbar_title", comment: nil)
}
var kGuestRemoteNeedsActivation: String {
    OALocalizedString("Guest_RemoteActivation_needs_activate_title", comment: nil)
}
var kGuestRemoteNeedsActivationDescription: String {
    OALocalizedString("Guest_RemoteActivation_needs_activate_note", comment: nil)
}
var kGuestRemoteActivationVerifyGuestRemoteAccess: String {
    OALocalizedString("Guest_RemoteActivation_verify_guest_remote_access", comment: nil)
}

var kRemoteVerificationInstructionsStepHeader1: String {
    OALocalizedString("RemoteVerification_instructions_step_header_1", comment: nil)
}
var kRemoteVerificationInstructionsStepSubheader1: String {
    OALocalizedString("RemoteVerification_instructions_step_subheader_1", comment: nil)
}
var kRemoteVerificationInstructionsStepBody1: String {
    OALocalizedString("RemoteVerification_instructions_step_body_1", comment: nil)
}
var kRemoteVerificationInstructionsStepIcon1: String {
    OALocalizedString("RemoteVerification_instructions_step_icon_1", comment: nil)
}
var kRemoteVerificationInstructionsStepHeader2: String {
    OALocalizedString("RemoteVerification_instructions_step_header_2", comment: nil)
}
var kRemoteVerificationInstructionsStepSubheader2: String {
    OALocalizedString("RemoteVerification_instructions_step_subheader_2", comment: nil)
}
var kRemoteVerificationInstructionsStepBody2: String {
    OALocalizedString("RemoteVerification_instructions_step_body_2", comment: nil)
}
var kRemoteVerificationInstructionsStepHeader3: String {
    OALocalizedString("RemoteVerification_instructions_step_header_3", comment: nil)
}
var kRemoteVerificationInstructionsStepSubheader3: String {
    OALocalizedString("RemoteVerification_instructions_step_subheader_3", comment: nil)
}
var kRemoteVerificationInstructionsStepBody3: String {
    OALocalizedString("RemoteVerification_instructions_step_body_3", comment: nil)
}
var kRemoteVerificationInstructionsStepHeader4: String {
    OALocalizedString("RemoteVerification_instructions_step_header_4", comment: nil)
}
var kRemoteVerificationInstructionsStepSubheader4: String {
    OALocalizedString("RemoteVerification_instructions_step_subheader_4", comment: nil)
}
var kRemoteVerificationInstructionsStepBody4: String {
    OALocalizedString("RemoteVerification_instructions_step_body_4", comment: nil)
}
var kRemoteVerificationManualCode: String {
    OALocalizedString("RemoteVerification_manual_code", comment: nil)
}


// MARK: Drivers Companion

public var kDriversCompanion: String {
    OALocalizedString("Drivers_companion", comment: nil)
}

// MARK: - Digital Key
public let kDKCalibrationValueSave = "DKSetCalibration"
public let kDKManualCalibrationEnabled = "DKManualCalibrationEnabled"
public let kDKCalibrationSetting = "DKCalibrationSetting"

public var kResend: String {
    OALocalizedString("Common_resend", comment: nil)
}
public var kNext: String {
    OALocalizedString("Common_next", comment: nil)
}
public var kAccept: String {
    OALocalizedString("Common_accept", comment: nil)
}
public var kDecline: String {
    OALocalizedString("Common_decline", comment: nil)
}
public var kSureWantCancel: String {
    OALocalizedString("Common_sure_want_cancel", comment: nil)
}
public var kGuestAccessOnly: String {
    OALocalizedString("Common_guest_access_only", comment: nil)
}
public var kDigitalKey: String {
    OALocalizedString("Digital_key", comment: nil)
}
public var kDKDownloadKey: String {
    OALocalizedString("Digital_key_download_key", comment: nil)
}
public var kDKCardTitle: String {
    OALocalizedString("Digital_key_car_title", comment: nil)
}
public var kDKShared: String {
    OALocalizedString("Digital_key_shared", comment: nil)
}
public var kDKSharedReachedMaxTitle: String {
    OALocalizedString("Digital_key_shared_reached_max_title", comment: nil)
}
public var kDKSharedReachedMaxMessage: String {
    OALocalizedString("Digital_key_shared_reached_max_message", comment: nil)
}
public var kDKSharedConfirmInvite: String {
    OALocalizedString("Digital_key_shared_confirm_invite", comment: nil)
}
public var kDKSharedAcceptInvite: String {
    OALocalizedString("Digital_key_shared_accept_invite", comment: nil)
}
public var kDKSharedInvitePending: String {
    OALocalizedString("Digital_key_shared_invite_pending", comment: nil)
}
public var kDKSharedStatusPending: String {
    OALocalizedString("Digital_key_shared_status_pending", comment: nil)
}
public var kDKCardBody: String {
    OALocalizedString("Digital_key_car_body", comment: nil)
}
public var kDKCardSharedBody: String {
    OALocalizedString("Digital_key_car_shared_body", comment: nil)
}
public var kDKCardBodyMaintenance: String {
    OALocalizedString("Digital_key_car_body_maintenance", comment: nil)
}
public var kDKSetUpMyKey: String {
    OALocalizedString("Digital_key_set_up_my_key", comment: nil)
}
public var kDKEnrollUserDevice: String {
    OALocalizedString("Digital_key_enroll_device", comment: nil)
}
public var kDKEnrollUserDeviceEnrolled: String {
    OALocalizedString("Digital_key_enroll_device_enrolled", comment: nil)
}
public var kDKActivateDigitalKey: String {
    OALocalizedString("Digital_key_activate_digital_key", comment: nil)
}
public var kDKManageKeyAndSharing: String {
    OALocalizedString("Digital_key_manage_key_and_sharing", comment: nil)
}
public var kDKConnectToVehicle: String {
    OALocalizedString("Digital_key_connect_to_vehicle", comment: nil)
}
public var kDKManageKey: String {
    OALocalizedString("Digital_key_manage_key", comment: nil)
}
public var kDKManageKeyContact: String {
    OALocalizedString("Digital_key_contact", comment: nil)
}
public var kDKRemoveMyKey: String {
    OALocalizedString("Digital_key_remove_my_key", comment: nil)
}
public var kDKTransferDigitalKey: String {
    OALocalizedString("Digital_key_transfer_digital_key", comment: nil)
}
public var kDKTransferDigitalKeyHeader: String {
    OALocalizedString("Digital_key_transfer_digital_key_header", comment: nil)
}
public var kDKTransferDigitalKeyBody: String {
    OALocalizedString("Digital_key_transfer_digital_key_body", comment: nil)
}
public var kDKTransferDigitalKeyTransferKey: String {
    OALocalizedString("Digital_key_transfer_digital_key_transfer_key", comment: nil)
}
public var kDKTransferDigitalKeyConfirmTitle: String {
    OALocalizedString("Digital_key_transfer_digital_key_confirm_title", comment: nil)
}
public var kDKTransferDigitalKeyConfirmBody: String {
    OALocalizedString("Digital_key_transfer_digital_key_confirm_body", comment: nil)
}
public var kDKTransferDigitalKeyConfirmAction: String {
    OALocalizedString("Digital_key_transfer_digital_key_confirm_action", comment: nil)
}
public var kDKTransferDigitalKeyAddFromContacts: String {
    OALocalizedString("Digital_key_transfer_digital_key_add_from_contacts", comment: nil)
}
public var kDKTransferDigitalKeySendSuccess: String {
    OALocalizedString("Digital_key_transfer_digital_key_success", comment: nil)
}
public var kDKShareDigitalKey: String {
    OALocalizedString("Digital_key_share_digital_key", comment: nil)
}
public var kDKShareDigitalKeyHeader: String {
    OALocalizedString("Digital_key_share_digital_key_header", comment: nil)
}
public var kDKShareDigitalKeyBody: String {
    OALocalizedString("Digital_key_share_digital_key_body", comment: nil)
}
public var kDKShareDigitalKeyTransferKey: String {
    OALocalizedString("Digital_key_share_digital_key_transfer_key", comment: nil)
}
public var kDKShareDigitalKeySendSuccess: String {
    OALocalizedString("Digital_key_share_digital_key_success", comment: nil)
}
public var kDKShareDigitalKeyNewUser: String {
    OALocalizedString("Digital_key_share_digital_key_new_user", comment: nil)
}
public var kDKSharedKeysMax: String {
    OALocalizedString("Digital_key_shared_keys_max", comment: nil)
}
public var kDKSharedEmptyHeader: String {
    OALocalizedString("Digital_key_shared_empty_header", comment: nil)
}
public var kDKSharedEmptyBody: String {
    OALocalizedString("Digital_key_shared_empty_body", comment: nil)
}
public var kDKRemoveKey: String {
    OALocalizedString("Digital_key_remove_key", comment: nil)
}
public var kDKSharedRevokeUserConfirmTitle: String {
    OALocalizedString("Digital_key_shared_revoke_user_confirm_title", comment: nil)
}
public var kDKSharedRevokeUserConfirmMessage: String {
    OALocalizedString("Digital_key_shared_revoke_user_confirm_message", comment: nil)
}
public var kDKSharedRevokeUserSuccess: String {
    OALocalizedString("Digital_key_shared_revoke_user_success", comment: nil)
}
public var kDKSetupTitle: String {
    OALocalizedString("Digital_key_setup", comment: nil)
}
public var kDKVerifyDeviceTitle: String {
    OALocalizedString("Digital_key_verify_device_title", comment: nil)
}
public var kDKVerifyDeviceBody: String {
    OALocalizedString("Digital_key_verify_device_body", comment: nil)
}
public var kDKVerifyDeviceNoCode: String {
    OALocalizedString("Digital_key_verify_device_no_code", comment: nil)
}
public var kDKVerifyDeviceEnterCode: String {
    OALocalizedString("Digital_key_verify_device_enter_code", comment: nil)
}
public var kDKVerifyDevice: String {
    OALocalizedString("Digital_key_verify_device", comment: nil)
}
public var kDKVerifyDeviceNewCode: String {
    OALocalizedString("Digital_key_verify_device_new_code", comment: nil)
}
public var kDKSyncingCancel: String {
    OALocalizedString("Digital_key_syncing_cancel", comment: nil)
}
public var kDKPairInfoHeader: String {
    OALocalizedString("Digital_key_pair_info_header", comment: nil)
}
public var kDKPairInfoConnect: String {
    OALocalizedString("Digital_key_pair_info_connect", comment: nil)
}
public var kDKPairInfoCheckmark1: String {
    OALocalizedString("Digital_key_pair_info_checkmark_1", comment: nil)
}
public var kDKPairInfoCheckmark2: String {
    OALocalizedString("Digital_key_pair_info_checkmark_2", comment: nil)
}
public var kDKPairInfoCheckmark3: String {
    OALocalizedString("Digital_key_pair_info_checkmark_3", comment: nil)
}
public var kDKBluetoothTurnOn: String {
    OALocalizedString("Digital_key_bluetooth_turn_on", comment: nil)
}
public var kDKBluetoothOffMessage: String {
    OALocalizedString("Digital_key_bluetooth_off_message", comment: nil)
}
public var kDKBLEVehicleConnectErrorTitle: String {
    OALocalizedString("Digital_key_ble_vehicle_connect_error_title", comment: nil)
}
public var kDKBLEVehicleConnectErrorMessage: String {
    OALocalizedString("Digital_key_ble_vehicle_connect_error_message", comment: nil)
}
public var kDKConnectingToYourVehicle: String {
    OALocalizedString("Digital_key_connecting_to_your_vehicle", comment: nil)
}
public var kDKConnectingSureNearVehicle: String {
    OALocalizedString("Digital_key_connecting_sure_near_vehicle", comment: nil)
}
public var kDKPlaceKeyFobOverStart: String {
    OALocalizedString("Digital_key_place_key_fob_over_start", comment: nil)
}
public var kDKPlaceKeyFobOrCardOverStart: String {
    OALocalizedString("Digital_key_place_key_fob_or_card_over_start", comment: nil)
}
public var kDKHoldHearTwoBeeps: String {
    OALocalizedString("Digital_hold_hear_two_beeps", comment: nil)
}
public var kDKDidnotHearBeeps: String {
    OALocalizedString("Digital_didnot_hear_beeps", comment: nil)
}
public var kDKSetupSuccessHeader: String {
    OALocalizedString("Digital_key_setup_success_header", comment: nil)
}
public var kDKSetupSuccessCheckmark1: String {
    OALocalizedString("Digital_key_setup_success_checkmark_1", comment: nil)
}
public var kDKSetupSuccessCheckmark2: String {
    OALocalizedString("Digital_key_setup_success_checkmark_2", comment: nil)
}
public var kDKSetupSuccessCheckmark3: String {
    OALocalizedString("Digital_key_setup_success_checkmark_3", comment: nil)
}
public var kDKSetupSuccessCheckmark4: String {
    OALocalizedString("Digital_key_setup_success_checkmark_4", comment: nil)
}
public var kDKSetupChecklistHeader: String {
    OALocalizedString("Digital_key_setup_checklist_header", comment: nil)
}
public var kDKSetupCheckList1: String {
    OALocalizedString("Digital_key_setup_checkList_1", comment: nil)
}
public var kDKSetupCheckList2: String {
    OALocalizedString("Digital_key_setup_checkList_2", comment: nil)
}
public var kDKSetupCheckList3: String {
    OALocalizedString("Digital_key_setup_checkList_3", comment: nil)
}
public var kDKSetupCheckList4: String {
    OALocalizedString("Digital_key_setup_checkList_4", comment: nil)
}
public var kDKSetupCheckList5: String {
    OALocalizedString("Digital_key_setup_checkList_5", comment: nil)
}
public var kDKEnterVehicleBeforeContinuing: String {
    OALocalizedString("Digital_key_enter_vehicle_before_continuing", comment: nil)
}
public var kDKEnterVehicleTitle: String {
    OALocalizedString("Digital_key_enter_vehicle_title", comment: nil)
}
public var kDKEnterVehicleBody: String {
    OALocalizedString("Digital_key_enter_vehicle_body", comment: nil)
}
public var kDKReturnToDashboard: String {
    OALocalizedString("Digital_key_return_to_dashboard", comment: nil)
}
public var kDKManageShareMyKey: String {
    OALocalizedString("Digital_key_manage_share_my_key", comment: nil)
}
public var kDKDownloadingYourMobile: String {
    OALocalizedString("Digital_key_downloading_your_mobile", comment: nil)
}
public var kDKRegisteringYourMobile: String {
    OALocalizedString("Digital_key_registering_your_mobile", comment: nil)
}
public var kDKContactingVehicle: String {
    OALocalizedString("Digital_key_contacting_vehicle", comment: nil)
}
public var kDKActivationConfirmed: String {
    OALocalizedString("Digital_key_vehicle_activation_confirmed", comment: nil)
}
public var kDKTakeFewMinutes: String {
    OALocalizedString("Digital_key_take_few_minutes", comment: nil)
}
public var kDKHowToUse: String {
    OALocalizedString("Digital_key_how_to_use", comment: nil)
}
public var kDKHowToUse1Header: String {
    OALocalizedString("Digital_key_how_to_use_first_header", comment: nil)
}
public var kDKHowToUse1Checkmark1: String {
    OALocalizedString("Digital_key_how_to_use_1_checkmark_1", comment: nil)
}
public var kDKHowToUse1Checkmark2: String {
    OALocalizedString("Digital_key_how_to_use_1_checkmark_2", comment: nil)
}
public var kDKHowToUse1Checkmark3: String {
    OALocalizedString("Digital_key_how_to_use_1_checkmark_3", comment: nil)
}
public var kDKHowToUse2Header: String {
    OALocalizedString("Digital_key_how_to_use_2_header", comment: nil)
}
public var kDKHowToUse2Message: String {
    OALocalizedString("Digital_key_how_to_use_2_message", comment: nil)
}
public var kDKHowToUse3Header: String {
    OALocalizedString("Digital_key_how_to_use_3_header", comment: nil)
}
public var kDKHowToUse3Message: String {
    OALocalizedString("Digital_key_how_to_use_3_message", comment: nil)
}
public var kDKHowToUse4Header: String {
    OALocalizedString("Digital_key_how_to_use_4_header", comment: nil)
}
public var kDKHowToUse4Message: String {
    OALocalizedString("Digital_key_how_to_use_4_message", comment: nil)
}
public var kDKHowToUse5Header: String {
    OALocalizedString("Digital_key_how_to_use_5_header", comment: nil)
}
public var kDKHowToUse5Message: String {
    OALocalizedString("Digital_key_how_to_use_5_message", comment: nil)
}
public var kDKConnected: String {
    OALocalizedString("Digital_key_connected", comment: nil)
}
public var kDKNotConnected: String {
    OALocalizedString("Digital_key_not_connected", comment: nil)
}
public var kDKInVehicleCarHeader: String {
    OALocalizedString("Digital_key_in_vehicle_header", comment: nil)
}
public var kDKInVehicleCarBody: String {
    OALocalizedString("Digital_key_in_vehicle_body", comment: nil)
}
public var kDKAssetAccessAdded: String {
    OALocalizedString("Digital_key_asset_access_add", comment: nil)
}
public var kDKAssetAccessRemoved: String {
    OALocalizedString("Digital_key_asset_access_removed", comment: nil)
}
public var kDKSuccessRemovedSharedKeyHeader: String {
    OALocalizedString("Digital_key_success_removed_shared_key_header", comment: nil)
}
public var kDKSuccessRemovedSharedKeyBody: String {
    OALocalizedString("Digital_key_success_removed_shared_key_body", comment: nil)
}
public var kDKSuccessRemovedOwnerKeyHeader: String {
    OALocalizedString("Digital_key_success_removed_owner_key_header", comment: nil)
}
public var kDKRemoveSharedKeyConfirmTitle: String {
    OALocalizedString("Digital_key_removed_shared_key_confirm_title", comment: nil)
}
public var kDKRemoveSharedKeyConfirmMessage: String {
    OALocalizedString("Digital_key_removed_shared_key_confirm_message", comment: nil)
}
public var kDKRemoveOwnerKeyConfirmTitle: String {
    OALocalizedString("Digital_key_removed_owner_key_confirm_title", comment: nil)
}
public var kDKRemoveOwnerKeyConfirmMessage: String {
    OALocalizedString("Digital_key_removed_owner_key_confirm_message", comment: nil)
}
public var kDKBiometricEnableTitle: String {
    OALocalizedString("Digital_key_biometric_enable_title", comment: nil)
}
public var kDKBiometricEnableMessage: String {
    OALocalizedString("Digital_key_biometric_enable_message", comment: nil)
}
public var kDKDownloadCompleteHeader: String {
    OALocalizedString("Digital_key_download_complete_header", comment: nil)
}
public var kDKDownloadCompleteBody: String {
    OALocalizedString("Digital_key_download_complete_body", comment: nil)
}
public var kDKYesConenctNow: String {
    OALocalizedString("Digital_key_yes_connect_now", comment: nil)
}
public var kDKNoConenctLater: String {
    OALocalizedString("Digital_key_not_connect_later", comment: nil)
}

// MARK: - OTA 21MM update
public var kOTADCMDriverAssist: String {
    OALocalizedString("OTA_DCM_Driver_Assist", comment: nil)
}
public var kOTANewSoftware: String {
    OALocalizedString("OTA_new_software_available", comment: nil)
}
public var kOTAVersionNumber: String {
    OALocalizedString("OTA_version_no", comment: nil)
}
public var kOTAWhatsNew: String {
    OALocalizedString("OTA_whats_new", comment: nil)
}
public var kOTAImportant: String {
    OALocalizedString("OTA_important", comment: nil)
}
public var kOTAAgreeInstall: String {
    OALocalizedString("OTA_agree_install", comment: nil)
}
public var kOTADisagree: String {
    OALocalizedString("OTA_disagree", comment: nil)
}
public var kOTAInstallDetails: String {
    OALocalizedString("OTA_vehicle_Software_install_details", comment: nil)
}
public var kOTAInstallFailed: String {
    OALocalizedString("OTA_install_failed", comment: nil)
}
public var kOTAInstallComplete: String {
    OALocalizedString("OTA_install_complete", comment: nil)
}
public var kOTAInstallCompleteDetails: String {
    OALocalizedString("OTA_install_complete_details", comment: nil)
}
public var kOTAGoToDashboard: String {
    OALocalizedString("OTA_go_to_dash", comment: nil)
}
public var kOTAAlertTitle: String {
    OALocalizedString("OTA_alert_title", comment: nil)
}
public var kOTAAlertMessage: String {
    OALocalizedString("OTA_alert_message", comment: nil)
}
public var kOTAAlertSure: String {
    OALocalizedString("OTA_alert_sure", comment: nil)
}
public var kOTAAlertGoBack: String {
    OALocalizedString("OTA_alert_goback", comment: nil)
}

public var kDKSharedHeader: String {
    OALocalizedString("Digital_key_shared_setup_header", comment: nil)
}
public var kDKSharedBody: String {
    OALocalizedString("Digital_key_shared_setup_body", comment: nil)
}
public var kDKSharedDevice: String {
    OALocalizedString("Digital_key_shared_setup_device", comment: nil)
}
public var kDKSharedDeviceSuccess: String {
    OALocalizedString("Digital_key_shared_setup_device_success", comment: nil)
}
public var kDKActivationFailedHeader: String {
    OALocalizedString("Digital_key_activation_error_header", comment: nil)
}
public var kDKActivationFailedBody: String {
    OALocalizedString("Digital_key_activation_error_body", comment: nil)
}
public var kDKTryAgain: String {
    OALocalizedString("Digital_key_try_again", comment: nil)
}
public var kDKRemove: String {
    OALocalizedString("Digital_key_remove", comment: nil)
}
public var kDKContactInvite: String {
    OALocalizedString("Digital_key_phone_invite", comment: nil)
}
public var kDKInvitePending: String {
    OALocalizedString("Digital_key_invitation_pending", comment: nil)
}
public var kDKErrorTipConnection: String {
    OALocalizedString("Digital_key_error_tip_connection", comment: nil)
}
public var kDKErrorUseFob: String {
    OALocalizedString("Digital_key_error_tip_use_fob", comment: nil)
}
public var kDKErrorDashboardTryAgain: String {
    OALocalizedString("Digital_key_error_dashboard_try_again", comment: nil)
}
public var kDKFailUnlock: String {
    OALocalizedString("Digital_key_fail_unlock", comment: nil)
}
public var kDKFailLock: String {
    OALocalizedString("Digital_key_fail_lock", comment: nil)
}
public var kDKGuestKeyRemove: String {
    OALocalizedString("Digital_key_guest_key_remove", comment: nil)
}
public var kDKKeyOwner: String {
    OALocalizedString("Digital_key_owner", comment: nil)
}
public var kDKManage: String {
    OALocalizedString("Digital_key_manage", comment: nil)
}
public var kDKErrorTryAgain: String {
    OALocalizedString("Digital_key_try_again_force_close", comment: nil)
}
public var kDKUnableRemoveAccess: String {
    OALocalizedString("Digital_key_unable_remove_access", comment: nil)
}
public var kDKErrorNotShared: String {
    OALocalizedString("Digital_key_error_not_share", comment: nil)
}
public var kDKErrorConfirmPhone: String {
    OALocalizedString("Digital_key_error_confirm_phone", comment: nil)
}
public var kDKOn: String {
    OALocalizedString("Digital_key_on", comment: nil)
}
public var kDKPhoneNumber: String {
    OALocalizedString("Common_phone_number", comment: nil)
}
public var kDKSendInvitation: String {
    OALocalizedString("Digital_key_send_invitation", comment: nil)
}
public var kDKAccessMessasge: String {
    OALocalizedString("Digital_key_access_message", comment: nil)
}
public var kDKShareConfirmTitle: String {
    OALocalizedString("Digital_key_share_confirm_title", comment: nil)
}
public var kDKShareConfirmMessage: String {
    OALocalizedString("Digital_key_share_confirm_message", comment: nil)
}
public var kDKInvitationSent: String {
    OALocalizedString("Digital_key_invitation_sent", comment: nil)
}
public var kDKRegistrationError: String {
    OALocalizedString("Digital_key_register_error", comment: nil)
}
public var kDKMustBeInVehicle: String {
    OALocalizedString("Digital_key_be_in_vehicle_error", comment: nil)
}
public var kDKHaveKeyFob: String {
    OALocalizedString("Digital_key_have_key_fob_error", comment: nil)
}
public var kDKTryAgainReturnDash: String {
    OALocalizedString("Digital_key_try_again_return_dash_error", comment: nil)
}
public var kDKFobHold: String {
    OALocalizedString("Digital_key_fob_and_hold", comment: nil)
}
public var kDKPairError: String {
    OALocalizedString("Digital_key_pair_error", comment: nil)
}
public var kDKAppOpenOrBackground: String {
    OALocalizedString("Digital_key_app_must_be_open", comment: nil)
}
public var kDKKeyFobBackup: String {
    OALocalizedString("Digital_key_fob_backup", comment: nil)
}
public var kDKPairingDevice: String {
    OALocalizedString("Digital_key_pairing_device", comment: nil)
}
public var kDKBTRequired: String {
    OALocalizedString("Digital_key_bluetooth_required", comment: nil)
}
public var kDKNoAccess: String {
    OALocalizedString("Digital_key_no_access", comment: nil)
}
public var kDKSharedRemoved: String {
    OALocalizedString("Digital_key_shared_removed", comment: nil)
}
public var kDKNotAvailable: String {
    OALocalizedString("Digital_key_feature_not_available", comment: nil)
}
public var kDKTechDifficulties: String {
    OALocalizedString("Digital_key_difficulties", comment: nil)
}
public var kDKRoutineMaintenance: String {
    OALocalizedString("Digital_key_routine_maintenance", comment: nil)
}
public var kDKOutage: String {
    OALocalizedString("Digital_key_outage", comment: nil)
}
public var kDKScheduledMaintenanceMessage: String {
    OALocalizedString("Digital_key_scheduled_maintenance", comment: nil)
}
public var kDKFeatureTechDifficulties: String {
    OALocalizedString("Digital_key_tech_difficulties", comment: nil)
}
public var kDKServiceInterruption: String {
    OALocalizedString("Digital_key_service_interruption", comment: nil)
}
public var kDKScheduledMaintenanceTitle: String {
    OALocalizedString("Digital_key_scheduled_maintenance_title", comment: nil)
}
public var kDKRemovalWarning: String {
    OALocalizedString("Digital_key_removal_warning", comment: nil)
}
public var kDKToRemoveMessage: String {
    OALocalizedString("Digital_key_to_remove", comment: nil)
}
public var kDKNotRemovedTitle: String {
    OALocalizedString("Digital_key_not_removed_title", comment: nil)
}
public var kDKNotRemovedDescription: String {
    OALocalizedString("Digital_key_not_removed_description", comment: nil)
}
public var kDKUnableCommunicateTitle: String {
    OALocalizedString("Digital_key_unable_communicate_title", comment: nil)
}
public var kDKUnableCommunicateDescription: String {
    OALocalizedString("Digital_key_unable_communicate_description", comment: nil)
}
public var kFinish: String {
    OALocalizedString("Common_finish", comment: nil)
}
public var kDKRemoveError: String {
    OALocalizedString("Digital_key_unable_remove", comment: nil)
}
public var kDKRemoveErrorMessage: String {
    OALocalizedString("Digital_key_remove_error_messsage", comment: nil)
}
public var kDKErrorConfirmOn: String {
    OALocalizedString("Digital_key_confirm_on", comment: nil)
}
public var kRetry: String {
    OALocalizedString("Common_retry", comment: nil)
}
public var kDKVehicleCommError: String {
    OALocalizedString("Digital_key_comm_vehicle_error", comment: nil)
}
public var kDKViewKeyHMI: String {
    OALocalizedString("Digital_key_view_keys_hmi", comment: nil)
}
public var kDKPairingFailedHeader: String {
    OALocalizedString("Digital_key_pairing_error_header", comment: nil)
}
public var kDKPairingnFailedMessage: String {
    OALocalizedString("Digital_key_pairing_error_message", comment: nil)
}
public var kDKPairingnFailedSuggestion1: String {
    OALocalizedString("Digital_key_pairing_error_suggestion_1", comment: nil)
}
public var kDKPairingnFailedSuggestion2: String {
    OALocalizedString("Digital_key_pairing_error_suggestion_2", comment: nil)
}
public var kDKPairingnFailedSuggestion3: String {
    OALocalizedString("Digital_key_pairing_error_suggestion_3", comment: nil)
}
public var kDKPairingnFailedSuggestion4: String {
    OALocalizedString("Digital_key_pairing_error_suggestion_4", comment: nil)
}
public var kDKPhoneEntryTitle: String {
    OALocalizedString("Digital_key_PhoneEntry_Title", comment: nil)
}
public var kDKPhoneEntryHeader: String {
    OALocalizedString("Digital_key_PhoneEntry_Header", comment: nil)
}
public var kDKSharingComingSoon: String {
    OALocalizedString("Digital_key_Shared_Coming_Soon", comment: nil)
}
public func kDKInviteAcceptedOwnerMessage(name: String) -> String {
    String(format: OALocalizedString("Digital_key_invite_accepted_owner_message", comment: nil), name)
}
public func kDKInviteDeclinedOwnerMessage(name: String) -> String {
    String(format: OALocalizedString("Digital_key_invite_declined_owner_message", comment: nil), name)
}
public var kDKCustomizeBLERange: String {
    OALocalizedString("Digital_key_customize_ble_range", comment: nil)
}
public var kDKBLERange: String {
    OALocalizedString("Digital_key_ble_range", comment: nil)
}
public var kDKBLEManualSensitivity: String {
    OALocalizedString("Digital_key_ble_manual_sensitivity", comment: nil)
}
public var kDKBLESensitivity: String {
    OALocalizedString("Digital_key_ble_sensitivity", comment: nil)
}
public var kDKBLERangeDescription: String {
    OALocalizedString("Digital_key_ble_customize_sensitivity", comment: nil)
}
public var kDKBLESensitivityDescription: String {
    OALocalizedString("Digital_key_ble_sensitivity_description", comment: nil)
}
public var kDKBLELow: String {
    OALocalizedString("Digital_key_ble_sensitivity_low", comment: nil)
}
public var kDKBLEMedium: String {
    OALocalizedString("Digital_key_ble_sensitivity_medium", comment: nil)
}
public var kDKBLEHigh: String {
    OALocalizedString("Digital_key_ble_sensitivity_High", comment: nil)
}
public var kDKBLENearVehicle: String {
    OALocalizedString("Digital_key_ble_be_near_vehicle", comment: nil)
}
public var kDKBLENearVehicleMessage: String {
    OALocalizedString("Digital_key_ble_be_near_vehicle_message", comment: nil)
}
public var kDKBLECalibrationUpdated: String {
    OALocalizedString("Digital_key_ble_calibration_updated", comment: nil)
}
public var kDKBLECalibrationUpdatedMessage: String {
    OALocalizedString("Digital_key_ble_calibration_updated_message", comment: nil)
}

// SMS Consent
public var kSMSConsentText: String {
    OALocalizedString("SMS_consent_pending_text", comment: nil)
}
public var kSMSConsentFailedTitle: String {
    OALocalizedString("SMS_consent_feiled_title", comment: nil)
}
public var kSMSConsentDeclinedUpdateMessage: String {
    OALocalizedString("SMS_consent_declined_update_message", comment: nil)
}
public var kSMSConsentDeclinedDKMessage: String {
    OALocalizedString("SMS_consent_declined_dk_message", comment: nil)
}
public var kSMSConsentTimeoutMessage: String {
    OALocalizedString("SMS_consent_timeout_message", comment: nil)
}
// RemoteParking
public var kRemoteParkControlCardLearnMore: String {
    OALocalizedString("Remote_Park_Control_Card_Learn_More", comment: nil)
}
public var kRemoteParkControlCardActivateTitle: String {
    OALocalizedString("Remote_Park_Control_Card_Activate_Title", comment: nil)
}
public var kRemoteParkControlCardActivateBody: String {
    OALocalizedString("Remote_Park_Control_Card_Activate_Body", comment: nil)
}
public var kRemoteParkControlTitle: String {
    OALocalizedString("Remote_Park_Control_Title", comment: nil)
}
public var kRemoteParkControlDescriptionModelTitle: String {
    OALocalizedString("kRemote_ParkControl_Description_Model_Title", comment: nil)
}
public var kRemoteParkControlDescriptionModelBody: String {
    OALocalizedString("kRemote_ParkControl_Description_Model_Body", comment: nil)
}
public var kRemoteParkControlDescriptionHowToUpdateTitle: String {
    OALocalizedString("kRemote_ParkControl_Description_How_To_Update_Title", comment: nil)
}
public var kRemoteParkControlDescriptionHowToUpdateBody: String {
    OALocalizedString("kRemote_ParkControl_Description_How_To_Update_Body", comment: nil)
}
public var kRemoteParkControlDescriptionDifferenceTitle: String {
    OALocalizedString("kRemote_ParkControl_Description_Difference_Title", comment: nil)
}
public var kRemoteParkControlDescriptionDifferenceBody: String {
    OALocalizedString("kRemote_ParkControl_Description_Difference_Body", comment: nil)
}
public var kRemoteParkControlDescriptionCostTitle: String {
    OALocalizedString("kRemote_ParkControl_Description_Cost_Title", comment: nil)
}
public var kRemoteParkControlDescriptionCostBody: String {
    OALocalizedString("kRemote_ParkControl_Description_Cost_Body", comment: nil)
}
public var kRemoteParkBluethoothOffAlertTitle: String {
    OALocalizedString("kRemote_ParkBluethooth_OffAlert_Title", comment: nil)
}
public var kRemoteParkBluethoothOffAlertBody: String {
    OALocalizedString("kRemote_ParkBluethooth_OffAlert_Body", comment: nil)
}


public var kSearchVehicleLocationError: String {
    OALocalizedString("Search_vehicle_location_error", comment: nil)
}
public var kSkip: String {
    OALocalizedString("Common_skip", comment: nil)
}
public var kCityTextNilError: String {
    OALocalizedString("City_text_nil_error", comment: nil)
}


//Delete Account

public var kDeletePersonalInformation: String {
    OALocalizedString("Delete_personal_information", comment: nil)
}

public var kDoNotSellPersonalInformation: String {
    OALocalizedString("Do_not_sell_personal_information", comment: nil)
}

public var kDeletePersonalIn: String {
    OALocalizedString("Delete_personal_info", comment: nil)
}

public var kConfirmRequest: String {
    OALocalizedString("Delete_confirm_message", comment: nil)
}

public var kIamSure: String {
    OALocalizedString("Yes_im_sure", comment: nil)
}

public var kRequestReceived: String {
    OALocalizedString("Request_received", comment: nil)
}

public var kDeleteConfirm: String {
    OALocalizedString("delete_confirm", comment: nil)
}

public var KCustomerAlreadyDeletedAcct: String {
    OALocalizedString("Customer_already_Submitted_delete_request", comment: nil)
}
public var KDeletionRequestError: String {
    OALocalizedString("deletion_request_error", comment: nil)
}
// Block Fleet Vehicle
public var KVehicleNotLinked: String {
    OALocalizedString("Vehicle_Not_Linked", comment: nil)
}
public var KFlaggedVinError: String {
    OALocalizedString("Flagged_Vin_Error", comment: nil)
}
public var kProfileSavedTitle: String {
    OALocalizedString("Profile_Saved_Fleet_Title", comment: nil)
}
public var kProfileSavedSubTitle: String {
    OALocalizedString("Profile_Saved_Fleet_Subtitle", comment: nil)
}
public var kProfileSavedHeaderTitle: String {
    OALocalizedString("Profile_Saved_Fleet_Header_Title", comment: nil)
}
public var kContinueToDashboard: String {
    OALocalizedString("Continue_To_Dashboard", comment: nil)
}
public var kCasPhoneRequired: String {
    OALocalizedString("Cas_phone_required_title", comment: nil)
}
public var kCasPhoneRequiredSub: String {
    OALocalizedString("Cas_phone_required_sub_title", comment: nil)
}
public var kCasAccountSettings: String {
    OALocalizedString("Cas_account_settings", comment: nil)
}
public var kCasPhoneVerify: String {
    OALocalizedString("Cas_phone_verify_title", comment: nil)
}
public var kCasPhoneVerifySub: String {
    OALocalizedString("Cas_phone_verify_sub_title", comment: nil)
}
public var kCasSignOut: String {
    OALocalizedString("Cas_sign_out", comment: nil)
}

 //MARK: Subaru EV
public var kEVChargeInfo: String {
    OALocalizedString("EV_charge_info", comment: nil)
}
public var kEVClimateOn: String {
    OALocalizedString("EV_climate_on", comment: nil)
}
public var kEVClimateOff: String {
    OALocalizedString("EV_climate_off", comment: nil)
}
public var kEVUntilFullyCharged: String {
    OALocalizedString("EV_until_fully_charged", comment: nil)
}
public var kEVHoursUntilFull: String {
    OALocalizedString("EV_hours_until_full", comment: nil)
}
public var kEVMinUntilFull: String {
    OALocalizedString("EV_min_until_full", comment: nil)
}
public var kEVDayUntilFull: String {
    OALocalizedString("EV_day_until_full", comment: nil)
}
public var kEVEstimation: String {
    OALocalizedString("EV_estimation", comment: nil)
}
public var kEVScheduleTitle: String {
    OALocalizedString("EV_schedule_title", comment: nil)
}
public var kEVScheduleSubtitle: String {
    OALocalizedString("EV_schedule_subtitle", comment: nil)
}
public var kChargeInfo: String {
    OALocalizedString("EV_charge_now", comment: nil)
}
public var kEVNoScheduleTitle: String {
    OALocalizedString("EV_no_schedule_title", comment: nil)
}
public var kEVNoScheduleSubtitle: String {
    OALocalizedString("EV_no_schedule_subtitle", comment: nil)
}
public var kEVStartCharging: String {
    OALocalizedString("EV_start_charging", comment: nil)
}
public var kEVCreateSchedule: String {
    OALocalizedString("EV_create_schedule", comment: nil)
}
public var kEVChargeSchedule: String {
    OALocalizedString("EV_charge_schedule", comment: nil)
}
public var kEVStartSchedule: String {
    OALocalizedString("EV_start_schedule", comment: nil)
}
public var kEVEndSchedule: String {
    OALocalizedString("EV_end_schedule", comment: nil)
}
public var kEVDaysOfWeekSchedule: String {
    OALocalizedString("EV_days_schedule", comment: nil)
}
public var kEVEndSubtitle: String {
    OALocalizedString("EV_end_subtitle", comment: nil)
}
public var kEVToastDays: String {
    OALocalizedString("EV_toast_days", comment: nil)
}
public var kEVToastCreated: String {
    OALocalizedString("EV_toast_created", comment: nil)
}
public var kEVToastUpdated: String {
    OALocalizedString("EV_toast_updated", comment: nil)
}
public var kEVToastDeleted: String {
    OALocalizedString("EV_toast_deleted", comment: nil)
}
public var kEVPluggedIn: String {
    OALocalizedString("EV_plugged_in", comment: nil)
}
public var kEVDeleteScheduleAlertTitle: String {
    OALocalizedString("EV_delete_schedule_alert_title", comment: nil)
}
public var kEVDeleteScheduleAlertSubtitle: String {
    OALocalizedString("EV_delete_schedule_alert_subtitle", comment: nil)
}
public var kEVDeleteScheduleAlertCancel: String {
    OALocalizedString("EV_delete_schedule_alert_cancel", comment: nil)
}
public var kEVDeleteScheduleAlertDelete: String {
    OALocalizedString("EV_delete_schedule_alert_delete", comment: nil)
}
public var kEVMinutes: String {
    OALocalizedString("EV_minutes", comment: nil)
}
public var kEVHours: String {
    OALocalizedString("EV_hours", comment: nil)
}
public var kEVSetMinutes: String {
    OALocalizedString("EV_set_minutes", comment: nil)
}
public var kEVSetHours: String {
    OALocalizedString("EV_set_hours", comment: nil)
}
