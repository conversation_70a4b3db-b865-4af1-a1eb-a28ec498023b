// Copyright © 2019 Toyota. All rights reserved.

import Foundation

// MARK: - XCAPP
let loadV2Dashboard = true
let xcappEnabled = true
let ownersManualEnabled = true
let driverScoreOptOutEnabled = true
let roadsideAssistanceEnabled = true
let hapticTouchEnabled = true
let serviceHistoryEnabled = true
/// enables manual service history entry screens
let canManuallyAddServiceHistory = true
let siriusXMEnabled = true
let nonCVEnabled = true
let criticalMessageEnabled = true
let profileUpdateEnabled = true
let lastTripClearRecentHistory = true
let overrideEnabled = true
let driverScoreOptInEnabled = true
let scheduleMaintenanceCardEnabled = true
let importantMessageEnabled = true
let tripEventsEnabled = true
let electricVehicleEnabled = true
let isVehicleSoftwareEnabled = true
let isDashboardLightsEnabled = true
let deleteSinglePaymentMethod = false
let branchDeeplinkingEnabled = true
let helpAndFeedbackEnabled = true
let frNativeEnabled = true
let connectedVehicleProfilesEnabled = true
let bottomTabBarEnabled = false
let isFlexSupported: Bool = {
    #if LEXUS
    return false
    #elseif SUBARU
    return false
    #else
    return true
    #endif
}()
let isNewSubscriptionsEnabled = true
let isCombinedDataConsentEnabled = true
let appSurveyEnabled = true

let isNewSubscriptionsOnboardingFlowEnabled = true

let isMultiScreenEnabled = false
let carPlayXp = CarPlayXp(
    isEnabled: false,
    enabledFeatures: [.findNearbyDealers, .odometerEntry, .maintenanceTimeline])

let dealerServicesRedesignEnabled = false

let EditLegacyPaymentEnabled = true
let externalProductsEnabled = true

let isInAppMarketingConsentEnabled = true
#if !SUBARU
let isSiriShortcutsEnabled = true
#else
let isSiriShortcutsEnabled = false
#endif
let isHealthReportHistoryEnabled = false
let isMexicoRegionEnabled = true
let isInAppAmberConsentEnabled = false
let isTooltipEnable = true


// MARK: - 21MM
var isQRVehicleRegisterEnabled: Bool {
    isFeatureOverride(id: "isQRVehicleRegisterEnabled", default: true)
}
var myDestinationsEnabled: Bool {
    isFeatureOverride(id: "myDestinationsEnabled", default: true)
}
var isEmptyDashbaordEnabled: Bool {
    isFeatureOverride(id: "isEmptyDashbaordEnabled", default: true)
}
var lastMileSendToPhone: Bool {
    isFeatureOverride(id: "lastMileEnabled", default: true)
}
var linkedAccountsEnabled: Bool {
    isFeatureOverride(id: "linkedAccountsEnabled", default: true)
}
var vaSettingsEnabled: Bool {
    isFeatureOverride(id: "vaSettingsEnabled", default: true)
}
var is21MMActivateRemoteServicesEnabled: Bool {
    isFeatureOverride(id: "is21MMActivateRemoteServicesEnabled", default: true)
}
var isBLEUserProfileEnabled: Bool {
    isFeatureOverride(id: "isBLEUserProfileEnabled", default: true)
}
var debugingBLEUserProfileEnabled: Bool {
    debugFeature(id: "debugingBLEUserProfileEnabled")
}
var accountPINEnabled: Bool {
    isFeatureOverride(id: "accountPINEnabled", default: true)
}
var isRemoteServices21mmEnabled: Bool {
    isFeatureOverride(id: "remoteServices21MMEnabled", default: true)
}
var isDigitalKeyEnabled: Bool {
    isFeatureOverride(id: "isDigitalKeyEnabled", default: true)
}
var isDigitalKeySharingEnabled: Bool {
    isFeatureOverride(id: "isDigitalKeySharingEnabled", default: true)
}
var isDigitalKeyDALogsEnabled: Bool {
    debugFeature(id: "isDigitalKeyDALogsEnabled", default: false)
}
var isDigitalKeySkeepPushEnabled: Bool {
    debugFeature(id: "isDigitalKeySkeepPushEnabled", default: false)
}
var isRemoteParkEnabled: Bool {
    isFeatureOverride(id: "isRemoteParkEnabled", default: true)
}
var debugingDigitalKeyEnabled: Bool {
    debugFeature(id: "debugingDigitalKeyEnabled")
}
var debugingLogEnabled: Bool {
    debugFeature(id: "debugingLogEnabled")
}
var SMSConsentEnabled: Bool {
    isFeatureOverride(id: "SMSConsentEnabled", default: true)
}

var isOfflineModeEnabled: Bool {
    isFeatureOverride(id: "isOfflineModeEnabled", default: false)
}

var isUserBLECalibrationEnabled: Bool {
    isFeatureOverride(id: "isUserBLECalibrationEnabled", default: true)
}

var skipHSMUnlock: Bool {
    isFeatureOverride(id: "skipHSMUnlock", default: false)
}

var isDataDogEnabled: Bool {
    isFeatureOverride(id: "dataDogEnabled", default: false)
}

var isDataDogDKEnabled: Bool {
    isFeatureOverride(id: "dataDogDKEnabled", default: true)
}

var isCertPinningEnabled: Bool {
    return false
//    #if APP_STORE
//    return true
//    #else
//    return debugFeature(id: "isCertPinningEnabled", default: true)
//    #endif
}

// MARK: Flutter depreciation flags
var nativeVehicleGloveboxEnabled: Bool {
    isFeatureOverride(id: "nativeVehicleGloveboxEnabled", default: true)
}

var vehicleHealthDetailsPageV2: Bool {
    isFeatureOverride(id: "vehicleHealthDetailsPageV2", default: true)
}

internal var nativeVehicleSoftwareEnabled: Bool {
    isFeatureOverride(id: "nativeVehicleSoftwareEnabled", default: true)
}

internal var climateScreenV2: Bool {
    isFeatureOverride(id: "climateScreenV2", default: true)
}

internal var nativeAnnouncementCenterEnabled: Bool {
    isFeatureOverride(id: "nativeAnnouncementCenterEnabled", default: true)
}

internal var nativeGuestDriver: Bool {
#if SUBARU
    isFeatureOverride(id: "nativeGuestDriver", default: true)
#else
    isFeatureOverride(id: "nativeGuestDriver", default: false)
#endif
}

internal var drivePulseAndTrips: Bool {
    isFeatureOverride(id: "drivePulseAndTrips", default: false)
}

internal var nativeEVWalletEnabled: Bool {
    isFeatureOverride(id: "nativeEVWalletEnabled", default: true)
}

internal var isEVIonnaEnabled: Bool {
    isFeatureOverride(id: "isEVIonnaEnabled", default: false)
}

internal var isEVTeslaEnabled: Bool {
    isFeatureOverride(id: "isEVTeslaEnabled", default: false)
}

private func isFeatureOverride(id: String, default: Bool) -> Bool {
    #if ALLOW_DEBUG_TOOLS
    return DebugAppFeature.isOn(id) ?? `default`
    #else
    return `default`
    #endif
}

private func debugFeature(id: String, default: Bool = false) -> Bool {
    #if APP_STORE
    return false
    #else
    return DebugAppFeature.isOn(id) ?? `default`
    #endif
}

// MARK: EV Flutter depreciation flags

public var isEVFlutterDepreciation: Bool {
    isFeatureOverride(id: "isEVFlutterDepreciation", default: true)
}

// MARK: TFS Flutter depreciation flags

public var nativePayEnabled: Bool {
    isFeatureOverride(id: "nativePayEnabled", default: false)
}

public var isNativeDSASupported: Bool {
    // Flag - dealerAppointment
#if SUBARU
    isFeatureOverride(id:"nativeDsa", default: true)
#else
    isFeatureOverride(id:"nativeDsa", default: false)
#endif
}

// MARK: Charge Assist
internal var isChargeAssistEnabled: Bool {
    isFeatureOverride(id: "chargeAssist", default: false)
}

var DebugAppFeatures: [DebugAppFeature] = [
    DebugAppFeature(id: "isCertPinningEnabled", default: true),
    DebugAppFeature(id: "chargeAssist", default: isChargeAssistEnabled),
    DebugAppFeature(id: "myDestinationsEnabled", default: myDestinationsEnabled),
    DebugAppFeature(id: "vaSettingsEnabled", default: vaSettingsEnabled),
    DebugAppFeature(id: "isEmptyDashbaordEnabled", default: isEmptyDashbaordEnabled),
    DebugAppFeature(id: "linkedAccountsEnabled", default: linkedAccountsEnabled),
    DebugAppFeature(id: "lastMileEnabled", default: lastMileSendToPhone),
    DebugAppFeature(id: "isQRVehicleRegisterEnabled", default: isQRVehicleRegisterEnabled),
    DebugAppFeature(id: "is21MMActivateRemoteServicesEnabled", default: is21MMActivateRemoteServicesEnabled),
    DebugAppFeature(id: "isBLEUserProfileEnabled", default: isBLEUserProfileEnabled),
    DebugAppFeature(id: "debugingBLEUserProfileEnabled", default: debugingBLEUserProfileEnabled),
    DebugAppFeature(id: "accountPINEnabled", default: accountPINEnabled),
    DebugAppFeature(id: "remoteServices21MMEnabled", default: isRemoteServices21mmEnabled),
    DebugAppFeature(id: "isDigitalKeyEnabled", default: isDigitalKeyEnabled),
    DebugAppFeature(id: "isDigitalKeySharingEnabled", default: isDigitalKeySharingEnabled),
    DebugAppFeature(id: "isDigitalKeyDALogsEnabled", default: isDigitalKeyDALogsEnabled),
    DebugAppFeature(id: "isDigitalKeySkeepPushEnabled", default: isDigitalKeySkeepPushEnabled),
    DebugAppFeature(id: "debugingDigitalKeyEnabled", default: debugingDigitalKeyEnabled),
    DebugAppFeature(id: "debugingLogEnabled", default: debugingLogEnabled),
    DebugAppFeature(id: "SMSConsentEnabled", default: SMSConsentEnabled),
    DebugAppFeature(id: "DeeplinkTest", default: BranchKey.isTestKey),
    DebugAppFeature(id: "isOfflineModeEnabled", default: isOfflineModeEnabled),
    DebugAppFeature(id: "isUserBLECalibrationEnabled", default: isUserBLECalibrationEnabled),
    DebugAppFeature(id: "skipHSMUnlock", default: skipHSMUnlock),
    DebugAppFeature(id: "dataDogEnabled", default: true),
    DebugAppFeature(id: "nativeVehicleGloveboxEnabled", default: true),
    DebugAppFeature(id: "vehicleHealthDetailsPageV2", default: true),
    DebugAppFeature(id: "isEVFlutterDepreciation", default: true),
    DebugAppFeature(id: "nativeVehicleSoftwareEnabled", default: true),
    DebugAppFeature(id: "dataDogDKEnabled", default: true),
    DebugAppFeature(id: "climateScreenV2", default: climateScreenV2),
    DebugAppFeature(id: "nativePayEnabled", default: nativePayEnabled),
    DebugAppFeature(id: "nativeAnnouncementCenterEnabled", default: true),
    DebugAppFeature(id: "nativeGuestDriver", default: nativeGuestDriver),
    DebugAppFeature(id: "nativeEVWalletEnabled", default: true),
    DebugAppFeature(id: "nativeDsa", default: false),
    DebugAppFeature(id: "drivePulseAndTrips", default: false),
    DebugAppFeature(id: "isEVIonnaEnabled", default: false)
]
