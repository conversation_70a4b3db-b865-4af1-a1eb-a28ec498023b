// Copyright © 2025 Toyota. All rights reserved.

import Foundation

public enum AddVehicleErrorType {
    case appUpdate
    case pre17cy
    case seventeenCy
    case seventeenCyPlus
    case twentyOneMM
    case nullError
    case sb1394Toyota
    case sb1394Lexus
    case sb1394Subaru
    case unknown

    init(from raw: String?) {
        guard let raw = raw else {
            self = .unknown
            return
        }

        switch raw {
        case "app-update-error":
            self = .appUpdate
        case "vs-error-pre17cy":
            self = .pre17cy
        case "vs-error-17cy":
            self = .seventeenCy
        case "vs-error-17cyplus":
            self = .seventeenCyPlus
        case "vs-error-21mm":
            self = .twentyOneMM
        case "vs-error-null":
            self = .nullError
        case let value where value.hasSuffix("toy"):
            self = .sb1394Toyota
        case let value where value.hasSuffix("lex"):
            self = .sb1394Lexus
        case let value where value.hasSuffix("sub"):
            self = .sb1394Subaru
        default:
            self = .unknown
        }
    }
}

extension AddVehicleErrorType {
    var brandName: String? {
        switch self {
        case .sb1394Toyota:
            return kBrandNameToyota
        case .sb1394Lexus:
            return kBrandNameLexus
        case .sb1394Subaru:
            return kBrandNameSubaru
        default:
            return nil
        }
    }
}
