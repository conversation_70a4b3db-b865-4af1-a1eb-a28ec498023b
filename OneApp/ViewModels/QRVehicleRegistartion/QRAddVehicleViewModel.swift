//  Copyright © 2020 Toyota. All rights reserved.

import UIKit

class QRAddVehicleViewModel: BaseViewModel {

    func addVehicle(
        vinNumber: String,
        isFromQRScan: Bool = false,
        view: UIView? = nil,
        completion: @escaping (_ error: String?, _ detailDesc: String?) -> Void
    ) {
        var errorMessage = ""

        if checkIsDemoMode(showsDialog: false) == false {
            for vehicle in AppState.vehicleList ?? [] where vehicle.vin == vinNumber {
                errorMessage = kVehicleAlreadyAssociated
                completion(errorMessage, "")
                return
            }
        }

        mainStore.dispatch(SetVinNumber(vinNumber: vinNumber))

        VehicleRepository.getVehicleDetails(vinNumber: vinNumber) { vehicleDetails, status in
            let response = status?.status?.messages?.first?.responseCode?.lowercased()
            let rawDesc = status?.status?.messages?.first?.detailedDescription ?? kProfileSavedSubTitle
            let detailedDesc = rawDesc.replacingOccurrences(of: "<break>", with: "\n")

            let errorType = AddVehicleErrorType(from: response)

            if vehicleDetails == nil && status != nil {
                switch errorType {
                case .appUpdate:
                    if !isFromQRScan {
                        completion("app-update-error", detailedDesc)
                    }
                    return

                case .pre17cy, .seventeenCy, .seventeenCyPlus, .twentyOneMM, .nullError,
                    .sb1394Toyota, .sb1394Lexus, .sb1394Subaru:
                    completion(response, detailedDesc)
                    return

                case .unknown:
                    completion(status?.status?.messages?.first?.description ?? kVINisNotValid, detailedDesc)
                    return
                }
            } else if vehicleDetails == nil && status == nil {
                completion(status?.status?.messages?.first?.description ?? kVINisNotValid, detailedDesc)
                return
            } else if let vehicle = vehicleDetails?.payload?.vehicle {
                if vehicle.generationType == .nonCV, !nonCVEnabled {
                    completion(kVINisNotSupported, "")
                    return
                }

                let is21mmQREligible = vehicle.generationType == .twentyOneMM
                    && isQRVehicleRegisterEnabled
                    && !isFromQRScan

                let isNonMexicoEligible = is21mmQREligible && vehicle.region != OneAppRegion.Mexico.rawValue

                let isMexicoEligible = is21mmQREligible
                    && vehicle.region == OneAppRegion.Mexico.rawValue
                    && (vehicle.qrcodeEligible ?? false)

                if isNonMexicoEligible || isMexicoEligible {
                    if let view = view {
                        mainStore.dispatch(PushView(viewState: .qrContinue))
                    } else {
                        DeeplinkManager.shared.executeDeepLink(
                            given: DeeplinkPath.qr_vehicle_register.rawValue,
                            payload: nil
                        )
                    }
                    completion(nil, nil)
                    return
                }

                mainStore.dispatch(SetVehicle(vehicleState: vehicleDetails))
                mainStore.dispatch(SetDealerPreferedState(preferedDealerState: nil))

                if isFromQRScan {
                    mainStore.dispatch(PopAndPushView(viewState: .addVin(vehicle: vehicle)))
                } else {
                    mainStore.dispatch(PushView(viewState: .addVin(vehicle: vehicle)))
                }

                completion(nil, nil)
            }
        }
    }

    func showScanner(fromViewController viewController: UIViewController, completion: ((Bool) -> Void)? = nil) {
        DispatchQueue.main.async {
            let scanController = VINScanViewController()
            scanController.modalPresentationStyle = .fullScreen

            scanController.scanCompletion = { vin in
                guard vin.isValid() else { return }

                if vin.count == 17 {
                    mainStore.dispatch(PushView(viewState: .homeWithScan(vin: vin)))
                } else {
                    let queryDictionary = URLComponents(string: vin)?.queryDictionary
                    guard let userCode = queryDictionary?["user_code"] ?? queryDictionary?["$user_code"] else { return }
                    mainStore.dispatch(
                        PushView(
                            viewState: .manualQrScan(
                                remoteActivation: false,
                                scanMode: .qrScanner,
                                userCodeFromNC: userCode
                            )
                        )
                    )
                }
            }
            viewController.present(scanController, animated: true, completion: nil)
        }
    }

    func getCountry() -> String? {
        LocalizableHelper.getUserRegion()?.rawValue
    }
}
