// Copyright © 2020 Toyota. All rights reserved.

import BiometricFeature
import Foundation
import RxSwift
import OACommon

final class SecuritySettingsViewModel {
    
    let updatedSectionSubject = BehaviorSubject<Bool>(value: false)
    var setupPIN = false

    // In the future there will be additional sections/cells added
    
    private lazy var sections: [SecuritySettingSection] = {
        let biometryType = getBiometryType()
        let brand: String
        #if LEXUS
        brand = kLexus.capitalized
        #elseif SUBARU
        brand = kSubaruApp
        #else
        brand = kToyota.capitalized
        #endif
        let titleText = String(format: kBiometricSettings, biometryType)
        let subtitleText = String(format: kBiometricSettingsSubtitle, brand, biometryType)
        let faceIdSection = SecuritySettingSection(titleText: titleText, subtitleText: subtitleText)        
        let keepMeLoggedInSection = SecuritySettingSection(titleText: "", subtitleText: "")
        let appName: String
        #if LEXUS
        appName = kLexus.capitalized
        #elseif SUBARU
        appName = kSubaruApp
        #else
        appName = kToyota.capitalized
        #endif
        
        let descriptionText = String(format: kRequireAuthenticationDescription, appName)
        let descriptionSection = SecuritySettingSection(titleText: nil, subtitleText: descriptionText)
        var sections = [SecuritySettingSection]()
        sections.append(contentsOf: [faceIdSection, keepMeLoggedInSection, descriptionSection])
        
        if accountPINEnabled {
            let resetPIN = SecuritySettingSection(titleText: kPINSecuritySettingsTitle, subtitleText: kPINSecuritySettingsDescription)
            sections.append(resetPIN)
        }
        if connectedVehicleProfilesEnabled {
            let vehicleSection = SecuritySettingSection(titleText: kAccountSettingsVehicleProfileTitle, subtitleText: kAccountSettingsVehicleProfileSubTitle)
            sections.append(vehicleSection)
        }
        if AppState.currentVehicle?.region == OneAppRegion.Mexico.rawValue {
            return sections
        }
        let PersonalDataSection = SecuritySettingSection(titleText: kAccountSettingsPersonalInfoTitle, subtitleText: kAccountSettingsProfileSubTitle)
        sections.append(PersonalDataSection)
        return sections
    }()
    
    private lazy var cells: [[SecuritySetting]] = {
        let biometryType = getBiometryType()

        let enableBiometricsTitle = String(format: kEnableBiometricsTitle, biometryType)
        let enableBiometricsCell = SecuritySetting(mainText: enableBiometricsTitle, detailText: nil, hasToggle: true, hasArrowImage: nil, cellType: "enableFaceID")
        let requireAuthenticationCell = SecuritySetting(mainText: kRequireAuthenticationTitle, detailText: nil, hasToggle: nil, hasArrowImage: true, cellType: "requireAuthentication")
        let keepMeLoggedInAuthenticationCell = SecuritySetting(mainText: kTitleKeepMeLoggedIn, detailText: nil, hasToggle: true, hasArrowImage: nil, cellType: SecuritySettingCellType.keepMeLoggedIn.rawValue)
        
        var cells = [[enableBiometricsCell, requireAuthenticationCell], [keepMeLoggedInAuthenticationCell], []]

        if accountPINEnabled {
            let pinCell = SecuritySetting(mainText: kPINResetFlowTitle, detailText: nil, hasToggle: nil, hasArrowImage: true, cellType: "pin")
            cells.append([pinCell])
        }
        if connectedVehicleProfilesEnabled {
            let vehicleProfileCell = SecuritySetting(mainText: kAccountSettingsManageProfiles, detailText: nil, hasToggle: nil, hasArrowImage: true, cellType: "vehicleProfile")
            cells.append([vehicleProfileCell])
        }
        let personalDataCell = SecuritySetting(mainText: LocalizableHelper.getUserRegion() == .USA ? kAccountSettings_manage_data : kDeletePersonalIn, detailText: nil, hasToggle: nil, hasArrowImage: true, cellType: "personalData")
            cells.append([personalDataCell])
        return cells
    }()
    
    private func getBiometryType() -> String {
        let biometryType: String
        switch AuthenticationManager.shared.biometricsAuth.supportedBiometry {
        case .available(.faceID), .notAvailable(.faceID), .lockedOut(.faceID):
            biometryType = kFaceID
        case .available(.touchID), .notAvailable(.touchID), .lockedOut(.touchID):
            biometryType = kTouchID
        case .none:
            biometryType = ""
        }
        
        return biometryType
    }
    
    func getSectionCount() -> Int {
        return sections.count
    }
    
    func getNumberOfCellsInSection(_ sectionIndex: Int) -> Int {
        guard sectionIndex < cells.count else { return 0 }
        
        return cells[sectionIndex].count
    }
    
    func getSectionHeaderContent(for sectionIndex: Int) -> SecuritySettingSection? {
        guard sectionIndex < sections.count else { return nil }
        return sections[sectionIndex]
    }
    
    func getCellContent(for indexPath: IndexPath) -> SecuritySetting? {
        let rowIndex = indexPath.row
        let sectionIndex = indexPath.section
        guard sectionIndex < cells.count,
            rowIndex < cells[sectionIndex].count
            else { return nil }
        
        let cellContent = cells[indexPath.section][indexPath.row]
        return cellContent
    }
    
    func updateCells() {
        for (i, section) in cells.enumerated() {
            for (j, cell) in section.enumerated() {
                guard let cellTypeString = cell.cellType,
                let cellType = SecuritySettingCellType(rawValue: cellTypeString)
                else { continue }
                
                switch cellType {
                case .requireAuthentication:
                    updateRequireAuthenticationCell(section: i, row: j)
                case .resetPIN:
                    updateResetPIN(section: i, row: j)
                default:
                    break
                }
            }
        }
    }
    
    private func updateRequireAuthenticationCell(section: Int, row: Int) {
        let selectedTimeString = UserDefaults.standard.value(forKey: kUserDefaultsRequireAuthenticationTime) as? String ?? "fifteenMinutes"
        let selectedTime = RequireAuthenticationTime(rawValue: selectedTimeString) ?? .fifteenMinutes
        
        let newTimeString: String
        switch selectedTime {
        case .immediately:
            newTimeString = kImmediately
        case .oneMinute:
            newTimeString = kOneMinute
        case .fiveMinutes:
            newTimeString = kFiveMinutes
        case .fifteenMinutes:
            newTimeString = kFifteenMinutes
        case .oneHour:
            newTimeString = kOneHour
        case .sixHours:
            newTimeString = kSixHours
        case .twelveHours:
            newTimeString = kTwelveHours
        }
        
        let currentCell = cells[section][row]
        let faceIdUnlockEnabled = UserDefaults.standard.value(forKey: kUserDefaultsFaceIdUnlockEnabled) as? Bool
        let requireAuthenticationCell = SecuritySetting(mainText: currentCell.mainText, detailText: newTimeString, hasToggle: currentCell.hasToggle, hasArrowImage: faceIdUnlockEnabled, cellType: currentCell.cellType)
        
        cells[section][row] = requireAuthenticationCell
    }
    
    private func updateResetPIN(section: Int, row: Int) {
        let currentCell = cells[section][row]
        let pinSectionCell = SecuritySetting(mainText: self.setupPIN ? kPINSetupFlowTitle : kPINResetFlowTitle, detailText: currentCell.detailText, hasToggle: currentCell.hasToggle, hasArrowImage: currentCell.hasArrowImage, cellType: currentCell.cellType)
        cells[section][row] = pinSectionCell
    }
   
    func getCellDestination(for indexPath: IndexPath) -> UIViewController? {
        let cellContent = cells[indexPath.section][indexPath.row]
        guard let cellTypeString = cellContent.cellType,
            let cellType = SecuritySettingCellType(rawValue: cellTypeString)
            else { return nil }
        
        let faceIdUnlockEnabled = UserDefaults.standard.value(forKey: kUserDefaultsFaceIdUnlockEnabled) as? Bool
        if cellType == .requireAuthentication, faceIdUnlockEnabled == true {
            return RequireAuthenticationViewController()
        }
        
        return nil
    }
    
    func getCellViewState(for indexPath: IndexPath) -> ViewState? {
        let rowIndex = indexPath.row
        let sectionIndex = indexPath.section
        guard sectionIndex < cells.count,
            rowIndex < cells[sectionIndex].count
            else { return nil }
        let cellContent = cells[sectionIndex][rowIndex]
        guard let cellTypeString = cellContent.cellType,
            let cellType = SecuritySettingCellType(rawValue: cellTypeString)
            else { return nil }
        
        if connectedVehicleProfilesEnabled {
            if cellType == .vehicleProfile {
                mainStore.dispatch(PushView(viewState: .vehicleProfile))
            }
        }
        if cellType == .personalData {
            let userRegion = LocalizableHelper.getUserRegion()
            switch userRegion {
            case .Mexico, .Canada, .USA_PR:
                Analytics.logEvent(.MANAGE_DATA_DELETE_PERSONAL_INFORMATION_CLICKED)
                mainStore.dispatch(PushView(viewState: .deleteData))
            default:
                mainStore.dispatch(PushView(viewState: .personalDataList))
            }
        }
        if accountPINEnabled {
            if cellType == .resetPIN {
                return .accountPINInfo()
            }
        }
        
        return nil
    }

    func getIndexPathForCellType(type: SecuritySettingCellType) -> IndexPath? {
        for (sectionIndex, section) in cells.enumerated() {
            for (rowIndex, cell) in section.enumerated() {
                guard let cellTypeString = cell.cellType,
                    let cellType = SecuritySettingCellType(rawValue: cellTypeString),
                    cellType == type
                    else { continue }
                
                return IndexPath(row: rowIndex, section: sectionIndex)
            }
        }
        
        return nil
    }
}
