// Copyright © 2021 Toyota. All rights reserved.

import UIKit
import RxSwift

class SMSConsentPendingViewController: IdentifiableViewController {
    
    private var upTopNavigation: ThemedNavigationBar = {
        let view = ThemedNavigationBar(frame: DUMMY_RECT)
        return view
    }()
    
    private let logoImageView: UIImageView = {
        let imageView = UIImageView()
        #if LEXUS
        imageView.image = #imageLiteral(resourceName: "LexusBarButton").withRenderingMode(.alwaysTemplate)
        imageView.tintColor = UIColor.black
        #elseif SUBARU
        imageView.image = #imageLiteral(resourceName: "SubaruBarButton").withRenderingMode(.automatic)
        #else
        imageView.image = #imageLiteral(resourceName: "↳ 🎨 Fill").withRenderingMode(.alwaysOriginal)
        #endif
        return imageView
    }()
    
    private let titleLabel: OATitleLabel = {
        let tLabel = OATitleLabel()
        tLabel.font = theme.fontStyle.title3
        #if SUBARU
        tLabel.text = String(format: kSMSConsentTimeoutMessage, kSubaruApp)
        #else
        tLabel.text = String(format: kSMSConsentTimeoutMessage, BrandCode.fromTarget().brandName)
        #endif
        tLabel.numberOfLines = 0
        return tLabel
    }()
    
    private let activityIndicator: OACircleIndicatorUIView = {
        let view = OACircleIndicatorUIView()
        return view
    }()
    
    private var disposeBag = DisposeBag()
    private weak var coordinator: SMSConsentCoordinator?
    
    init(coordinator: SMSConsentCoordinator) {
        self.coordinator = coordinator
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setup()
        self.bind()
    }
    
    private func setup() {
        view.backgroundColor = theme.BACKGROUND_COLOR
        
        upTopNavigation.pinTop(view)
        
        view.addSubview(logoImageView)
        logoImageView.snp.makeConstraints { make in
            make.top.equalTo(upTopNavigation.snp.bottom).offset(24)
            make.left.equalToSuperview().inset(24)
            make.width.height.equalTo(40)
        }
        
        view.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(logoImageView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(26)
        }
        
        view.addSubview(activityIndicator)
        activityIndicator.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(titleLabel.snp.bottom).offset(10)
            make.height.width.equalTo(88)
        }
    }
    
    private func bind() {
        upTopNavigation.addBackButton { [weak self] in
            self?.coordinator?.close()
        }
    }
}

