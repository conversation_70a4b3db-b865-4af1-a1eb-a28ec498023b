#!/bin/bash

# Define color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[1;34m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
BOLD='\033[1m'
RESET='\033[0m'

print_section() {
  local directory=$1
  local title=$2

  if [ -d "$directory" ]; then
    echo "${YELLOW}${BOLD}✔ Found directory:${RESET} ${BLUE}$directory${RESET}\n"

    for file in "$directory"/*; do
      if [ -f "$file" ]; then
        echo "${CYAN}════════════════════════════════════════════════════${RESET}"
        echo "${GREEN}${BOLD}$(basename "$file")${RESET}\n"
        cat "$file"
        echo "\n${CYAN}════════════════════════════════════════════════════${RESET}\n"
      else
        echo "${RED}⚠ Skipping non-regular file:${RESET} $file"
      fi
    done

  else
    echo "${RED}${BOLD}✖ Directory does not exist:${RESET} $directory"
    # exit 1
  fi
}

# Print header
echo "\n${BOLD}${CYAN}=== 🔍 Build Links Summary ===${RESET}\n"

# Process App Center and TestFairy folders
print_section "fastlane/buildlinks/appcenter" "AppCenter"
# print_section "fastlane/buildlinks/testfairy" "TestFairy"

# Print footer
echo  "\n${BOLD}${GREEN}✅ All Build link displayed.${RESET}\n"
