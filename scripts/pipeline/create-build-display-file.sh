#!/bin/bash

# Usage: ./create_text_file.sh /path/to/directory filename "Your content here"

# Input arguments
pathToDirectory="$1"
filename="$2"
content="$3"

# Check for required arguments
if [[ -z "$pathToDirectory" || -z "$filename" || -z "$content" ]]; then
  echo "Usage: $0 <pathToDirectory> <filename> <content>"
  exit 1
fi
# Clean the directory before adding new file
rm -rf "$pathToDirectory"
# Create directory if it doesn't exist
mkdir -p "$pathToDirectory"

# Form the full file path
fullPath="${pathToDirectory%/}/$filename"

# Write content to file
echo "$content" > "$fullPath"

# Confirm creation
echo "File created at: $fullPath"
