fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

### loadAppStoreConnectApiKeys

```sh
[bundle exec] fastlane loadAppStoreConnectApiKeys
```



### load_build_schema

```sh
[bundle exec] fastlane load_build_schema
```



### load_configuration

```sh
[bundle exec] fastlane load_configuration
```



### loadDistributionGroup

```sh
[bundle exec] fastlane loadDistributionGroup
```



### loadAppname

```sh
[bundle exec] fastlane loadAppname
```



### loadNARegionAppname

```sh
[bundle exec] fastlane loadNARegionAppname
```



### loadAURegionAppname

```sh
[bundle exec] fastlane loadAURegionAppname
```



### loadToyotaNAAppname

```sh
[bundle exec] fastlane loadToyotaNAAppname
```



### loadLexusNAAppname

```sh
[bundle exec] fastlane loadLexusNAAppname
```



### loadSubaruNAAppname

```sh
[bundle exec] fastlane loadSubaruNAAppname
```



### loadToyotaAUAppname

```sh
[bundle exec] fastlane loadToyotaAUAppname
```



### loadLexusAUAppname

```sh
[bundle exec] fastlane loadLexusAUAppname
```



### loadExportMethod

```sh
[bundle exec] fastlane loadExportMethod
```



### load_ipa_name

```sh
[bundle exec] fastlane load_ipa_name
```



### setup_build_info

```sh
[bundle exec] fastlane setup_build_info
```



### up_build_number

```sh
[bundle exec] fastlane up_build_number
```



### generate_build_number

```sh
[bundle exec] fastlane generate_build_number
```



### set_build_number

```sh
[bundle exec] fastlane set_build_number
```



### setup_custom_keychain

```sh
[bundle exec] fastlane setup_custom_keychain
```

Setting up keychain

### set_keychain_build_tool_path

```sh
[bundle exec] fastlane set_keychain_build_tool_path
```



### install_certs_profile

```sh
[bundle exec] fastlane install_certs_profile
```



### getBundleIdentifierWithProfilesFormMatch

```sh
[bundle exec] fastlane getBundleIdentifierWithProfilesFormMatch
```



### run_linter

```sh
[bundle exec] fastlane run_linter
```



### run_linter_local

```sh
[bundle exec] fastlane run_linter_local
```



### run_string_file_lint

```sh
[bundle exec] fastlane run_string_file_lint
```



### run_eol_fix_lint

```sh
[bundle exec] fastlane run_eol_fix_lint
```



### run_linter_fix_local

```sh
[bundle exec] fastlane run_linter_fix_local
```



### loadMatchType

```sh
[bundle exec] fastlane loadMatchType
```



### loadAppIdentifiers

```sh
[bundle exec] fastlane loadAppIdentifiers
```



### loadMatchGitBranch

```sh
[bundle exec] fastlane loadMatchGitBranch
```



### loadMatchRequiredInfo

```sh
[bundle exec] fastlane loadMatchRequiredInfo
```



### getCoveragePaths

```sh
[bundle exec] fastlane getCoveragePaths
```



### sonarqube

```sh
[bundle exec] fastlane sonarqube
```



### run_sonar_local

```sh
[bundle exec] fastlane run_sonar_local
```



### generate_build_number_multi_aps_appcenter

```sh
[bundle exec] fastlane generate_build_number_multi_aps_appcenter
```



### upload_appcenter

```sh
[bundle exec] fastlane upload_appcenter
```



### getMarketingVersion

```sh
[bundle exec] fastlane getMarketingVersion
```



### createAppcenterArtifactsFile

```sh
[bundle exec] fastlane createAppcenterArtifactsFile
```



### testfairy_folder_path

```sh
[bundle exec] fastlane testfairy_folder_path
```



### load_testers_group

```sh
[bundle exec] fastlane load_testers_group
```



### upload_test_fairy

```sh
[bundle exec] fastlane upload_test_fairy
```



### createTestFairyArtifactsFile

```sh
[bundle exec] fastlane createTestFairyArtifactsFile
```



### createArtifactsFile

```sh
[bundle exec] fastlane createArtifactsFile
```



### run_analyzer

```sh
[bundle exec] fastlane run_analyzer
```



### unit_tests

```sh
[bundle exec] fastlane unit_tests
```

Runs unit tests. Expects `scheme` or `testplan`, `coverage` is optional.

### clean_test_output

```sh
[bundle exec] fastlane clean_test_output
```



### bump_version

```sh
[bundle exec] fastlane bump_version
```



### build

```sh
[bundle exec] fastlane build
```



### upload_symbols

```sh
[bundle exec] fastlane upload_symbols
```



### generate_release_notes

```sh
[bundle exec] fastlane generate_release_notes
```



### notifyteams

```sh
[bundle exec] fastlane notifyteams
```



### publish

```sh
[bundle exec] fastlane publish
```



### build_number

```sh
[bundle exec] fastlane build_number
```



### build_oneapp

```sh
[bundle exec] fastlane build_oneapp
```



### upload_app

```sh
[bundle exec] fastlane upload_app
```



### upload_appstore

```sh
[bundle exec] fastlane upload_appstore
```



### generated_release_notes

```sh
[bundle exec] fastlane generated_release_notes
```



### generate_release_notes_from_git

```sh
[bundle exec] fastlane generate_release_notes_from_git
```

Generate a release notes

----


## iOS

### ios custom_lane

```sh
[bundle exec] fastlane ios custom_lane
```

Description of what the lane does

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
