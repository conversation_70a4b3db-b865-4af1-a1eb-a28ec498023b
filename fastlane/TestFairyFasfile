require 'json'

default_platform(:ios)

# Build numbers
lane :generate_build_number_multi_aps_appcenter do |options|
  UI.message "options: #{options}"
  names = []
  reuse =  options[:reuse].to_s || ""
  region = options[:region].to_s || ""
  options.each_key do |key|
    brands = options[key].to_s || ""
    UI.message "---"
    UI.message "brands: #{brands} for app: #{key.to_s}"
    if !brands.empty? then
      brands.split(',').each do |brand|
        info = setup_build_info(app: key.to_s, brand: brand, region: region)
        if info[:appname] then
          names.append(info[:appname])
        end
      end
    end
  end

  build_number = 1
  names.each do |name|
    begin
      version = appcenter_fetch_version_number(owner_name: APPCENTER_OWNER_NAME, app_name: name)
      if reuse.empty? then
        app_number = version['build_number'].to_i + 1
      else
        app_number = version['build_number'].to_i
      end
    rescue => ex
      UI.error(ex)
      app_number = 1
    end
    UI.message("name: #{name} app_number: #{app_number}")
    if build_number < app_number then
      build_number = app_number
    end
  end

  build_number = build_number.to_s
  UI.message("NEW_BUILD_NUMBER: #{build_number}")
  ENV["NEW_BUILD_NUMBER"] = build_number
  sh("rm -f build.env")
  sh("echo \"BUILD_NUMBER=#{build_number}\" >> build.env")
  build_number
end

lane :upload_appcenter do |options|
  ipa = options[:ipaname]
  dsym = ipa + ".app.dSYM.zip"
  ipaPath = "./build/#{ipa}.ipa"
  dsymPath = "./build/#{dsym}"
  UI.message("Generated IPA Path:\n#{ipaPath}(#{dsymPath})")
  # By default appcenter plugin will use the environment variable APPCENTER_API_TOKEN. so pls add that in Gitlab CI variables
  appcenter_upload(
    owner_name: APPCENTER_OWNER_NAME,
    app_name: options[:appname],
    file: ipaPath,
    dsym: dsymPath,
    destinations: options[:groupname],
    destination_type: "group",
    release_notes: options[:release_notes] || generate_release_notes,
    notify_testers: true
  )
  createAppcenterArtifactsFile(options)
  upload_symbols(options)
end

lane :getMarketingVersion do |options|
  brand = options[:brand]
  xcconfig_name = "Common"

  if brand == BRAND_SUBARU
    xcconfig_name = "Subaru"
  end

  xcconfig_path = "./Configuration/Targets/Brand/#{xcconfig_name}.xcconfig"

  marketing_version = get_xcconfig_value(
    path: xcconfig_path,
    name: "MARKETING_VERSION"
  )

  UI.message("📦 Marketing version: #{marketing_version}")
  marketing_version
end

lane :createAppcenterArtifactsFile do |options|
  build_info = options
  brand = to_pascal_case(build_info[:brand])
  region = options[:region].upcase
  configuration = to_pascal_case(options[:configuration])
  build_type = to_pascal_case(options[:build_type])
  path = "../fastlane/buildlinks/appcenter"
  UI.message("Before parsing")
  info = lane_context[SharedValues::APPCENTER_BUILD_INFORMATION]
  UI.message("Before parsing #{info}")
  buildNumber = ENV["BUILD_NUMBER"]

  marketingVersion = getMarketingVersion()
  
  UI.message("buildNumber #{buildNumber}")
  UI.message("marketingVersion #{marketingVersion}")
  appname = info["app_name"]
  UI.message("appname #{appname}")
  releaseId = info["id"]
  UI.message("releaseId #{releaseId}")
  fileName = "#{build_type} #{brand}#{region} #{configuration} v#{marketingVersion}(#{buildNumber})"
  distribute_path = "https://appcenter.ms/orgs/#{APPCENTER_OWNER_NAME}/apps/#{appname}/distribute/releases/#{releaseId}"
  install_path = "https://install.appcenter.ms/orgs/#{APPCENTER_OWNER_NAME}/apps/#{appname}/releases/#{releaseId}"
  
  releaseDetails = "Install path: #{install_path} \nDistribute path: #{distribute_path} "
  
  releaseOptions = { }
  releaseOptions[:fileName] = fileName
  releaseOptions[:path] = path
  releaseOptions[:details] = releaseDetails
  createArtifactsFile(releaseOptions)
end

lane :testfairy_folder_path do |options|
  build_info = options
  # Root folder path with App region
  region_folder_name = ""
  case build_info[:region]
  when REGION_NA
    region_folder_name = "TMNA"
  when REGION_AU
    region_folder_name = "TMCA"
  end
  # Next with App brand
  brand_captalize = to_pascal_case(build_info[:brand])

  build_type = build_info[:isrelease] == true ? "ReleaseCut" : "Regular"

  case build_info[:build_type]
  when BUILD_TYPE_CUSTOM
    case build_info[:branch]
    when "develop"
      build_type_folder_name = "InMarket/Custom"
    when "24mm/develop"
      build_type_folder_name = "MM24/Custom"
    end
  when BUILD_TYPE_DEVELOP
    build_type_folder_name = "InMarket/#{build_type}"
  when BUILD_TYPE_24MM
    build_type_folder_name = "MM24/#{build_type}"
  end

  configuration_name = ""
  case options[:configuration]
  when CONFIG_RELEASE
    configuration_name = "Prod"
  when CONFIG_STAGING
    configuration_name = "Stage"
  when CONFIG_QA
    configuration_name = "QA"
  when CONFIG_SANDBOX
    configuration_name = "Sandbox"
  when CONFIG_DEV
    configuration_name = "Dev"
  end
  # Exit lane if region or build type is empty
  if region_folder_name.empty? || build_type_folder_name.empty?
    UI.user_error!("Error: Region #{region_folder_name} or Build Type #{build_type_folder_name} folder name is empty. Exiting lane.with buildinfo #{build_info} ")
  end

  if configuration_name.empty? 
    UI.user_error!("Error: Configuration #{configuration_name} folder name is empty. Exiting lane.with buildinfo #{build_info} ")
  end

  # Construct folder path
  folder_path = "/#{region_folder_name}/#{build_type_folder_name}/#{brand_captalize}/MobileApp/iOS/#{configuration_name}"

  build_info[:folder_path] = folder_path
  UI.message("App will upload to this path #{build_info[:folder_path]}")
  build_info
end

lane :load_testers_group do |options|
  build_info = options
  testers_groups = ""
  case build_info[:build_type]
  when BUILD_TYPE_CUSTOM
    testers_groups = ENV["OA_CUSTOM_DIST_GRP"]
  when BUILD_TYPE_DEVELOP
    testers_groups = ENV["OA_REGULAR_DIST_GRP"]
  when BUILD_TYPE_24MM
    testers_groups = ENV["OA_REGULAR_DIST_GRP"]
  end
  build_info[:testers_groups] = testers_groups
  build_info
end


lane :upload_test_fairy do |options|
  
  options = testfairy_folder_path(options)
  options = load_testers_group(options)
  UI.message("App will upload to testfairy with #{options}")
  ipa = options[:ipaname]
  dsym = ipa + ".app.dSYM.zip"
  ipaPath = "./build/#{ipa}.ipa"
  dsymPath = "./build/#{dsym}"
  UI.message("Generated IPA Path:\n#{ipaPath}(#{dsymPath})")
  sauce_lab_api_key = options[:saucelabsApiKey] || ENV['SAUCELABS_API_KEY']
  sauce_lab_url = options[:saucelabsUrl] || ENV['SAUCELABS_API_URL']
  testers_groups = options[:testers_groups] || ENV['SAUCELABS_TESTERS_GROUP']

  testfairy(
    api_key: sauce_lab_api_key,
    ipa: ipaPath,
    upload_url: sauce_lab_url,
    folder_name: options[:folder_path],
    testers_groups: testers_groups,
    notify: "on",
    landing_page_mode: "closed",
    comment: options[:release_notes] || generate_release_notes,
  )  
  createTestFairyArtifactsFile(options)
end

lane :createTestFairyArtifactsFile do |options|
  build_info = options
  brand = to_pascal_case(build_info[:brand])
  region = options[:region].upcase
  configuration = to_pascal_case(options[:configuration])
  build_type = to_pascal_case(options[:build_type])

  buildNumber = ENV["BUILD_NUMBER"]

  marketingVersion = getMarketingVersion()

  UI.message("buildNumber #{buildNumber}")
  UI.message("marketingVersion #{marketingVersion}")
  
  fileName = "#{build_type} #{brand}#{region} #{configuration} v#{marketingVersion}(#{buildNumber})"
  folder_path = options[:folder_path]
  build_url = lane_context[SharedValues::TESTFAIRY_BUILD_URL]
  download_url = lane_context[SharedValues::TESTFAIRY_DOWNLOAD_URL]
  landingpage_url = lane_context[SharedValues::TESTFAIRY_LANDING_PAGE]

  #Artifacts path
  path = "../fastlane/buildlinks/testfairy"
  releaseDetails = "Tesfairy Folder Path:#{folder_path}\nBuild URL: #{build_url}\nDownload URL: #{download_url}\nLanding Page: #{landingpage_url}"
  
  releaseOptions = { }
  releaseOptions[:fileName] = fileName
  releaseOptions[:path] = path
  releaseOptions[:details] = releaseDetails
  createArtifactsFile(releaseOptions)
end

lane :createArtifactsFile do |options|
  file_name = options[:fileName]
  path = options[:path]
  release_details = options[:details]

  script_path = "../scripts/pipeline/create-build-display-file.sh"
  sh("bash #{script_path} \"#{path}\" \"#{file_name}\" \"#{release_details}\"")
end