stages:
  - stream
  - lint
  - sonar_lint
  - unit_tests
  - setup
  - build_number
  - commit_bump
  - sonar
  - build
  - deploy
  - custom_build
  - release_build
  - display
  # - notify

variables:
  LC_ALL: "en_US.UTF-8"
  LANG: "en_US.UTF-8"
  FASTLANE_HIDE_CHANGELOG: "YES"
  FASTLANE_XCODE_PATH: "/Applications/Xcode16.1.app"
  DEFAULT_FASTLANE_XCODE_PATH: "/Applications/Xcode16.1.app"
  DEFAULT_BUILD_BRANCH_NAME: "develop" 
  TAG_NAME: "Develop-Build-Runner" #This will used only for upstream commit from flutter
  GIT_DEPTH: 0 # Fetch the full history
  XCODE_VERSION: ""
  
default:
  before_script:
    - bundle exec fastlane run clear_derived_data


workflow:
  auto_cancel:
    on_new_commit: interruptible #This will make pipeline can be interruptible
  rules:
    - if: $CI_COMMIT_TAG =~ /^App_Store_\w*/
      variables:
        TAG_NAME: "AppStore-Build-Runner"
        IS_APPSTORE_BUILD: "true"
        RUN_FLUTTER_SETUP: "true"
    - if: $MAKE_CUSTOM_STAGE_BUILD || $MAKE_CUSTOM_PROD_BUILD
      variables:
        IS_CUSTOM_BUILD: "true"
        TAG_NAME: "Develop-Build-Runner"

    - if: $MAKE_SANDBOX_BUILDS # && $CI_COMMIT_REF_PROTECTED == "true" #To make this appstore build upload happens only on protect branch
      variables:
        IS_SANDBOX_BUILD: "true"
        TAG_NAME: "Sandbox_Build_Runner"

    - if: $MAKE_DEVELOP_BUILDS && $CI_COMMIT_BRANCH == $DEFAULT_BUILD_BRANCH_NAME
      variables:
        IS_SCHEDULE_DEVELOP_BUILD: "true"
        TAG_NAME: "Develop-Build-Runner"

    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /release\/\w*/
      variables:
        TAG_NAME: "Release-Unit-Test-Runner"
        RUN_FLUTTER_SETUP: "true"

    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      variables:
      #This tag is different only for unit test(depends on flutter setup)
      #but for other job lint and sonar can be run on any machine
        TAG_NAME: "Develop-Unit-Test-Runner" 

    - if: $CI_COMMIT_MESSAGE =~ /^\[CI\]/
      when: never

    - if: $CI_COMMIT_BRANCH =~ /release\/\w*/
      variables:
        TAG_NAME: "Release-Build-Runner" 
        IS_RELEASE_CUT_BUILD: "true"
        RUN_FLUTTER_SETUP: "true"
    - when: always

include:
  - local: ".cicd/extends.gitlab-ci.yml"
  - local: ".cicd/develop.build.gitlab-ci.yml"
  - local: ".cicd/appstore.build.gitlab-ci.yml"
  - local: ".cicd/custom.build.gitlab-ci.yml"
  - local: ".cicd/pr.merge.gitlab-ci.yml"
  - local: ".cicd/24mm.build.gitlab-ci.yml"
  - local: ".cicd/sanbox.build.gitlab-ci.yml"
