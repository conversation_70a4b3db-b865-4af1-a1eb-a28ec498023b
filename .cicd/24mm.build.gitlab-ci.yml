
#MAKE_24MM_BUILDS - values "stage", "prod"
## Scheduled 24mm Builds ## 

toyota_na_stage_24mm_build:
  variables:
    BUILD_TYPE: '24mm'
    BRAND: 'toyota'
    BUILD_REGION: 'na'
    CONFIG: 'stage'
  extends: 
    - .build_app
  rules:
    - if: '$IS_SCHEDULE_24mm_BUILD == "true" && ($MAKE_24MM_BUILDS == "stage" || $MAKE_DEVELOP_BUILDS == "all")'
      when: always
    
lexus_na_stage_24mm_build:
  variables:
    BUILD_TYPE: '24mm'
    BRAND: 'lexus'
    BUILD_REGION: 'na'
    CONFIG: 'stage'
  extends: 
      - .build_app
  rules:
    - if: '$IS_SCHEDULE_24mm_BUILD == "true" && ($MAKE_24MM_BUILDS == "stage" || $MAKE_DEVELOP_BUILDS == "all")'
      when: always

# Commenting for now as Appcenter app not exist
# subaru_na_stage_24mm_build:
#   variables:
#     BUILD_TYPE: '24mm'
#     BRAND: 'subaru'
#     BUILD_REGION: 'na'
#     CONFIG: 'stage'
#   extends: 
#       - .build_app
#   rules:
#     - if: '$IS_SCHEDULE_24mm_BUILD == "true" && ($MAKE_24MM_BUILDS == "stage" || $MAKE_DEVELOP_BUILDS == "all")'
#       when: always

toyota_au_stage_24mm_build:
  variables:
    BUILD_TYPE: '24mm'
    BRAND: 'toyota'
    BUILD_REGION: 'au'
    CONFIG: 'stage'
  extends: 
      - .build_app
  rules:
    - if: '$IS_SCHEDULE_24mm_BUILD == "true" && ($MAKE_24MM_BUILDS == "stage" || $MAKE_DEVELOP_BUILDS == "all")'
      when: always

lexus_au_stage_24mm_build:
  variables:
    BUILD_TYPE: '24mm'
    BRAND: 'lexus'
    BUILD_REGION: 'au'
    CONFIG: 'stage'
  extends: 
      - .build_app
  rules:
    - if: '$IS_SCHEDULE_24mm_BUILD == "true" && ($MAKE_24MM_BUILDS == "stage" || $MAKE_DEVELOP_BUILDS == "all")'
      when: always

# Commenting for now as Appcenter app not exist
## Custom Prod Builds ## 
# toyota_na_prod_24mm_build:
#   variables:
#     BUILD_TYPE: '24mm'
#     BRAND: 'toyota'
#     BUILD_REGION: 'na'
#     CONFIG: 'prod'
#   extends: 
#       - .build_app
#   rules:
#     - if: '$IS_SCHEDULE_24mm_BUILD == "true" && ($MAKE_24MM_BUILDS == "prod" || $MAKE_DEVELOP_BUILDS == "all")'
#       when: always

# lexus_na_prod_24mm_build:
#   variables:
#     BUILD_TYPE: '24mm'
#     BRAND: 'lexus'
#     BUILD_REGION: 'na'
#     CONFIG: 'prod'
#   extends: 
#       - .build_app
#   rules:
#     - if: '$IS_SCHEDULE_24mm_BUILD == "true" && ($MAKE_24MM_BUILDS == "prod" || $MAKE_DEVELOP_BUILDS == "all")'
#       when: always

# subaru_na_prod_24mm_build:
#   variables:
#     BUILD_TYPE: '24mm'
#     BRAND: 'subaru'
#     BUILD_REGION: 'na'
#     CONFIG: 'prod'
#   extends: 
#       - .build_app
#   rules:
#     - if: '$IS_SCHEDULE_24mm_BUILD == "true" && ($MAKE_24MM_BUILDS == "prod" || $MAKE_DEVELOP_BUILDS == "all")'
#       when: always

# toyota_au_prod_24mm_build:
#   variables:
#     BUILD_TYPE: '24mm'
#     BRAND: 'toyota'
#     BUILD_REGION: 'au'
#     CONFIG: 'prod'
#   extends: 
#       - .build_app
#   rules:
#     - if: '$IS_SCHEDULE_24mm_BUILD == "true" && ($MAKE_24MM_BUILDS == "prod" || $MAKE_DEVELOP_BUILDS == "all")'
#       when: always

# lexus_au_prod_24mm_build:
#   variables:
#     BUILD_TYPE: '24mm'
#     BRAND: 'lexus'
#     BUILD_REGION: 'au'
#     CONFIG: 'prod'
#   extends: 
#       - .build_app
#   rules:
#     - if: '$IS_SCHEDULE_24mm_BUILD == "true" && ($MAKE_24MM_BUILDS == "prod" || $MAKE_DEVELOP_BUILDS == "all")'
#       when: always

# display_all_24mm_builds:
#   stage: display
#   extends: .display_build_links
#   needs:
#     - job: toyota_na_stage_24mm_build
#       artifacts: true
#       optional: true
#     - job: lexus_na_stage_24mm_build
#       artifacts: true            
#       optional: true
#     - job: subaru_na_stage_24mm_build
#       artifacts: true
#       optional: true
#     - job: toyota_au_stage_24mm_build
#       artifacts: true
#       optional: true
#     - job: lexus_au_stage_24mm_build
#       artifacts: true
#       optional: true
#     - job: toyota_na_prod_24mm_build
#       artifacts: true
#       optional: true
#     - job: lexus_na_prod_24mm_build
#       artifacts: true
#       optional: true
#     - job: subaru_na_prod_24mm_build
#       artifacts: true
#       optional: true
#     - job: toyota_au_prod_24mm_build
#       artifacts: true
#       optional: true
#     - job: lexus_au_prod_24mm_build
#       artifacts: true
#       optional: true
#   when: always
#   rules:
#      - if: $MAKE_24MM_BUILDS 
