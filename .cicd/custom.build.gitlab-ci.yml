
#MAKE_CUSTOM_STAGE_BUILD/MAKE_CUSTOM_PROD_BUILD - values can be - "all", "toyota-na", "lexus-na", "subaru-na" ,"toyota-au", 

## Custom Stage Builds ## 
toyota_na_stage_custom_build:
  variables:
    BUILD_TYPE: 'custom'
    BRAND: 'toyota'
    BUILD_REGION: 'na'
    CONFIG: 'stage'
  extends: .custom_build_publish
  rules:
    - if: '$MAKE_CUSTOM_STAGE_BUILD == "all"'
      when: always
    - if: '$MAKE_CUSTOM_STAGE_BUILD =~ /toyota-na/'
      when: always

lexus_na_stage_custom_build:
  variables:
    BUILD_TYPE: 'custom'
    BRAND: 'lexus'
    BUILD_REGION: 'na'
    CONFIG: 'stage'
  extends: .custom_build_publish
  rules:
    - if: '$MAKE_CUSTOM_STAGE_BUILD == "all"'
      when: always
    - if: '$MAKE_CUSTOM_STAGE_BUILD =~ /lexus-na/'
      when: always

subaru_na_stage_custom_build:
  variables:
    BUILD_TYPE: 'custom'
    BRAND: 'subaru'
    BUILD_REGION: 'na'
    CONFIG: 'stage'
  extends: .custom_build_publish
  rules:
    - if: '$MAKE_CUSTOM_STAGE_BUILD == "all"'
      when: always
    - if: '$MAKE_CUSTOM_STAGE_BUILD =~ /subaru-na/'
      when: always

# Commenting for now as Appcenter app not exist
# toyota_au_stage_custom_build:
#   variables:
#     BUILD_TYPE: 'custom'
#     BRAND: 'toyota'
#     BUILD_REGION: 'au'
#     CONFIG: 'stage'
#   extends: .custom_build_publish
#   rules:
#     - if: '$MAKE_CUSTOM_STAGE_BUILD == "all"'
#       when: always
#     - if: '$MAKE_CUSTOM_STAGE_BUILD =~ /toyota-au/'
#       when: always

# lexus_au_stage_custom_build:
#   variables:
#     BUILD_TYPE: 'custom'
#     BRAND: 'lexus'
#     BUILD_REGION: 'au'
#     CONFIG: 'stage'
#   extends: .custom_build_publish
#   rules:
#     - if: '$MAKE_CUSTOM_STAGE_BUILD == "all"'
#       when: always
#     - if: '$MAKE_CUSTOM_STAGE_BUILD =~ /lexus-au/'
#       when: always

## Custom Prod Builds ## 
toyota_na_prod_custom_build:
  variables:
    BUILD_TYPE: 'custom'
    BRAND: 'toyota'
    BUILD_REGION: 'na'
    CONFIG: 'prod'
  extends: .custom_build_publish
  rules:
    - if: '$MAKE_CUSTOM_PROD_BUILD == "all"'
      when: always
    - if: '$MAKE_CUSTOM_PROD_BUILD =~ /toyota-na/'
      when: always

lexus_na_prod_custom_build:
  variables:
    BUILD_TYPE: 'custom'
    BRAND: 'lexus'
    BUILD_REGION: 'na'
    CONFIG: 'prod'
  extends: .custom_build_publish
  rules:
    - if: '$MAKE_CUSTOM_PROD_BUILD == "all"'
      when: always
    - if: '$MAKE_CUSTOM_PROD_BUILD =~ /lexus-na/'
      when: always

subaru_na_prod_custom_build:
  variables:
    BUILD_TYPE: 'custom'
    BRAND: 'subaru'
    BUILD_REGION: 'na'
    CONFIG: 'prod'
  extends: .custom_build_publish
  rules:
    - if: '$MAKE_CUSTOM_PROD_BUILD == "all"'
      when: always
    - if: '$MAKE_CUSTOM_PROD_BUILD =~ /subaru-na/'
      when: always

# Commenting for now as Appcenter app not exist
# toyota_au_prod_custom_build:
#   variables:
#     BUILD_TYPE: 'custom'
#     BRAND: 'toyota'
#     BUILD_REGION: 'au'
#     CONFIG: 'prod'
#   extends: .custom_build_publish
#   rules:
#     - if: '$MAKE_CUSTOM_PROD_BUILD == "all"'
#       when: always
#     - if: '$MAKE_CUSTOM_PROD_BUILD =~ /toyota-au/'
#       when: always

# lexus_au_prod_custom_build:
#   variables:
#     BUILD_TYPE: 'custom'
#     BRAND: 'lexus'
#     BUILD_REGION: 'au'
#     CONFIG: 'prod'
#   extends: .custom_build_publish
#   rules:
#     - if: '$MAKE_CUSTOM_PROD_BUILD == "all"'
#       when: always
#     - if: '$MAKE_CUSTOM_PROD_BUILD =~ /lexus-au/'
#       when: always

display_all_custom_builds:
  stage: display
  extends: .display_build_links
  needs:
    - job: toyota_na_stage_custom_build
      artifacts: true
      optional: true
    - job: lexus_na_stage_custom_build
      artifacts: true            
      optional: true
    - job: subaru_na_stage_custom_build
      artifacts: true
      optional: true
    # - job: toyota_au_stage_custom_build
    #   artifacts: true
    #   optional: true
    # - job: lexus_au_stage_custom_build
    #   artifacts: true
    #   optional: true
    - job: toyota_na_prod_custom_build
      artifacts: true
      optional: true
    - job: lexus_na_prod_custom_build
      artifacts: true
      optional: true
    - job: subaru_na_prod_custom_build
      artifacts: true
      optional: true
    # - job: toyota_au_prod_custom_build
    #   artifacts: true
    #   optional: true
    # - job: lexus_au_prod_custom_build
    #   artifacts: true
    #   optional: true
  when: always
  rules:
     - if: $MAKE_CUSTOM_STAGE_BUILD || $MAKE_CUSTOM_PROD_BUILD
