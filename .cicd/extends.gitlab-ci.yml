 
.build_app:
  stage: build
  timeout: 2 hours
  artifacts:
    expire_in: 1 week
    paths:
      - fastlane/buildlinks/
  needs:
    - job: appcenter_build_number
      artifacts: true
      optional: true
  before_script:
    - source .gitlab-ci-gem-install-script.sh
    - check_if_gem_install_is_already_running
    - bundle config set --local path '~/vendor/cache'
    - bundle install
    - rm -rf fastlane/buildlinks
    - bundle exec fastlane run clear_derived_data
  script:
    - echo "$BUILD_NUMBER"
    - echo "$PWD"
    - export
    - ./scripts/flutter-setup.sh $UPSTREAM_COMMIT_SHA
    - bundle exec fastlane build_oneapp build_type:$BUILD_TYPE brand:$BRAND group:$RELEASE_GROUP region:$BUILD_REGION config:$CONFIG appname:$APPCENTER_APP_NAME isrelease:$IS_RELEASE_CUT_BUILD branch:$DEFAULT_BUILD_BRANCH_NAME
    - bundle exec fastlane upload_app build_type:$BUILD_TYPE brand:$BRAND group:$RELEASE_GROUP region:$BUILD_REGION config:$CONFIG appname:$APPCENTER_APP_NAME isrelease:$IS_RELEASE_CUT_BUILD branch:$DEFAULT_BUILD_BRANCH_NAME
  allow_failure: false
  tags:
    - "${TAG_NAME}${XCODE_VERSION}"

.build_appstore_app:
  stage: build
  timeout: 2 hours
  artifacts:
    expire_in: 1 week
    paths:
      - ./build
  before_script:
    - source .gitlab-ci-gem-install-script.sh
    - check_if_gem_install_is_already_running
    - bundle config set --local path '~/vendor/cache'
    - bundle install
    - bundle exec fastlane run clear_derived_data
  script:
    - echo "$BUILD_NUMBER"
    - echo "$PWD"
    - export IS_APPSTORE=true
    - echo "Install the Unityframework for appstore builds with ENV variable IS_APPSTORE=$IS_APPSTORE"
    - ./scripts/flutter-setup.sh $UPSTREAM_COMMIT_SHA
    - export IS_APPSTORE=false
    - bundle exec fastlane up_build_number
    - bundle exec fastlane build_oneapp build_type:$BUILD_TYPE brand:$BRAND region:$BUILD_REGION config:$CONFIG
  allow_failure: false
  tags:
    - "${TAG_NAME}${XCODE_VERSION}"

.upload_appstore:
  stage: deploy
  before_script:
    - source .gitlab-ci-gem-install-script.sh
    - check_if_gem_install_is_already_running
    - bundle config set --local path '~/vendor/cache'
    - bundle install
    - bundle exec fastlane run clear_derived_data
  script:
    - bundle exec fastlane up_build_number
    - bundle exec fastlane upload_appstore build_type:$BUILD_TYPE brand:$BRAND region:$BUILD_REGION config:$CONFIG
  allow_failure: false
  tags:
    - "${TAG_NAME}${XCODE_VERSION}"

#Display all build links in a pipeline
.display_build_links:
  stage: display
  before_script:
    - source .gitlab-ci-gem-install-script.sh
    - check_if_gem_install_is_already_running
    - bundle config set --local path '~/vendor/cache'
    - bundle install
  script:
    - sh scripts/pipeline/display-all-build-links.sh

  allow_failure: true
  tags:
    - "${TAG_NAME}${XCODE_VERSION}"

# Notifications ## 
.notify_teams:
    stage: notify
    before_script:
      - bundle config set --local path '~/vendor/cache'
      - bundle install
      - bundle exec fastlane run clear_derived_data
    script:
      - bundle exec fastlane up_build_number
      - bundle exec fastlane notifyteams build_type:$BUILD_TYPE brand:$BRAND group:$RELEASE_GROUP region:$BUILD_REGION config:$CONFIG appname:$APPCENTER_APP_NAME isrelease:$IS_RELEASE_CUT_BUILD branch:$DEFAULT_BUILD_BRANCH_NAME
    allow_failure: false
    tags:
    - "${TAG_NAME}${XCODE_VERSION}"

.commit_bump:
  stage: commit_bump
  before_script:
    - df -h
    - ruby -v
    - bundle config set --local path '~/vendor/cache'
    - bundle install
    - bundle exec fastlane run clear_derived_data
  script:
    - bundle exec fastlane bump_version deployBranch:$CI_COMMIT_REF_NAME    
  tags: 
    - "${TAG_NAME}${XCODE_VERSION}"

.build_publish:
  stage: build
  timeout: 2 hours
  artifacts:
    expire_in: 1 week
    paths:
      - fastlane/buildlinks/
  needs:
    - job: appcenter_build_number
      artifacts: true
      optional: true
  before_script:
    - source .gitlab-ci-gem-install-script.sh
    - check_if_gem_install_is_already_running
    - bundle config set --local path '~/vendor/cache'
    - bundle install
    - rm -rf fastlane/buildlinks
    - bundle exec fastlane run clear_derived_data
  script:
    - echo "$BUILD_NUMBER"
    - echo "$PWD"
    - export
    - ./scripts/flutter-setup.sh $UPSTREAM_COMMIT_SHA
    - bundle exec fastlane publish build_type:$BUILD_TYPE brand:$BRAND group:$RELEASE_GROUP region:$BUILD_REGION config:$CONFIG appname:$APPCENTER_APP_NAME isrelease:$IS_RELEASE_CUT_BUILD branch:$DEFAULT_BUILD_BRANCH_NAME
  allow_failure: false
  tags:
    - "${TAG_NAME}${XCODE_VERSION}"


.unit_tests:
  stage: unit_tests
  artifacts:
    when: always
    expire_in: 1 week
    paths:
      - ./fastlane/test_output
    reports:
      junit: ./fastlane/test_output/**/report.junit
  before_script:
    - source .gitlab-ci-gem-install-script.sh
    - check_if_gem_install_is_already_running
    - bundle config set --local path '~/vendor/cache'
    - bundle install
    - bundle exec fastlane run clear_derived_data
  script:
    - ./scripts/flutter-setup.sh $UPSTREAM_COMMIT_SHA
    - bundle exec fastlane unit_tests scheme:$SCHEME
  interruptible: true #https://docs.gitlab.com/ee/ci/yaml/index.html#interruptible
  tags:
    - "${TAG_NAME}${XCODE_VERSION}"

.package_unit_tests:
  stage: unit_tests
  artifacts:
    expire_in: 1 week
    when: always
    paths:
      - ./fastlane/test_output
    reports:
      junit: ./fastlane/test_output/**/report.junit
  before_script:
    - source .gitlab-ci-gem-install-script.sh
    - check_if_gem_install_is_already_running
    - bundle config set --local path '~/vendor/cache'
    - bundle install
    - bundle exec fastlane run clear_derived_data
  script:
    - echo "Running test target validation..."
    - ./scripts/testplan/validate_test_target.swift
    - bundle exec fastlane unit_tests testplan:OneAppTestPlan coverage:true
  interruptible: true #https://docs.gitlab.com/ee/ci/yaml/index.html#interruptible
  tags:
    - "${TAG_NAME}${XCODE_VERSION}"

.swift_lint:
  stage: lint
  artifacts:
    when: always
    expire_in: 1 week
    paths:
      - ./fastlane/swiftlint.report.json
  before_script:
    - source .gitlab-ci-gem-install-script.sh
    - check_if_gem_install_is_already_running
    - bundle config set --local path '~/vendor/cache'
    - bundle install
    - bundle exec fastlane run clear_derived_data
  script:
    - export
    - check_prject_file
    - bundle exec fastlane run_linter
  interruptible: true
  tags:
    - "${TAG_NAME}${XCODE_VERSION}"

.get_appcenter_build_number:
  stage: build_number
  before_script:
    - source .gitlab-ci-gem-install-script.sh
    - check_if_gem_install_is_already_running
    - bundle config set --local path '~/vendor/cache'
    - bundle install
    - bundle exec fastlane run clear_derived_data
  script:
    - export CUSTOM=$(convert_variable_value_to_app_name $MAKE_CUSTOM_BUILD)
    #Have to revisit this
    - bundle exec fastlane generate_build_number_multi_aps_appcenter build_type:$BUILD_TYPE brand:$BRAND group:$RELEASE_GROUP region:$BUILD_REGION config:$CONFIG appname:$APPCENTER_APP_NAME isrelease:$IS_RELEASE_CUT_BUILD branch:$DEFAULT_BUILD_BRANCH_NAME
  tags:
    - "${TAG_NAME}${XCODE_VERSION}"

.custom_build_publish:
  artifacts:
    expire_in: 1 week
    paths:
      - fastlane/buildlinks/
  stage: custom_build
  timeout: 3 hours 30 minutes
  before_script:
    - source .gitlab-ci-gem-install-script.sh
    - check_if_gem_install_is_already_running
    - bundle config set --local path '~/vendor/cache'
    - bundle install
    - rm -rf fastlane/buildlinks
    - bundle exec fastlane run clear_derived_data
  script:
    - echo "$BUILD_NUMBER"
    - echo "$PWD"
    - export
    - ./scripts/flutter-setup.sh $UPSTREAM_COMMIT_SHA
    - bundle exec fastlane publish build_type:$BUILD_TYPE brand:$BRAND group:$RELEASE_GROUP region:$BUILD_REGION config:$CONFIG appname:$APPCENTER_APP_NAME isrelease:$IS_RELEASE_CUT_BUILD branch:$DEFAULT_BUILD_BRANCH_NAME
  allow_failure: false
  tags:
    - "${TAG_NAME}${XCODE_VERSION}"

.sonar_integration:
    before_script:
      - source .gitlab-ci-gem-install-script.sh
      - check_if_gem_install_is_already_running
      - bundle config set --local path '~/vendor/cache'
      - bundle install
      - sh ./scripts/sonar-setup.sh
    script:
      - bundle exec fastlane sonarqube coverage:$SONAR_INCLUDE_COVERAGE
    interruptible: true
    tags:
      - "${TAG_NAME}${XCODE_VERSION}"
