
#MAKE_DEVELOP_BUILDS - values "stage", "prod"

## Scheduled Develop Builds ## 

toyota_na_stage_develop_build:
  variables:
    BUILD_TYPE: 'develop'
    BRAND: 'toyota'
    BUILD_REGION: 'na'
    CONFIG: 'stage'
  extends: 
    - .build_app
  rules:
    - if: '$IS_SCHEDULE_DEVELOP_BUILD == "true" && ($MAKE_DEVELOP_BUILDS == "stage" || $MAKE_DEVELOP_BUILDS == "all")'
      when: always
    - if: '$IS_RELEASE_CUT_BUILD == "true"'
      when: always
    
lexus_na_stage_develop_build:
  variables:
    BUILD_TYPE: 'develop'
    BRAND: 'lexus'
    BUILD_REGION: 'na'
    CONFIG: 'stage'
  extends: 
      - .build_app
  rules:
    - if: '$IS_SCHEDULE_DEVELOP_BUILD == "true" && ($MAKE_DEVELOP_BUILDS == "stage" || $MAKE_DEVELOP_BUILDS == "all")'
      when: always
    - if: '$IS_RELEASE_CUT_BUILD == "true"'
      when: always

subaru_na_stage_develop_build:
  variables:
    BUILD_TYPE: 'develop'
    BRAND: 'subaru'
    BUILD_REGION: 'na'
    CONFIG: 'stage'
  extends: 
      - .build_app
  rules:
    - if: '$IS_SCHEDULE_DEVELOP_BUILD == "true" && ($MAKE_DEVELOP_BUILDS == "stage" || $MAKE_DEVELOP_BUILDS == "all")'
      when: always
    - if: '$IS_RELEASE_CUT_BUILD == "true"'
      when: always

# Commenting for now as Appcenter app not exist
# toyota_au_stage_develop_build:
#   variables:
#     BUILD_TYPE: 'develop'
#     BRAND: 'toyota'
#     BUILD_REGION: 'au'
#     CONFIG: 'stage'
#   extends: 
#       - .build_app
#   rules:
#     - if: '$IS_SCHEDULE_DEVELOP_BUILD == "true" && ($MAKE_DEVELOP_BUILDS == "stage" || $MAKE_DEVELOP_BUILDS == "all")'
#       when: always
#     - if: '$IS_RELEASE_CUT_BUILD == "true"'
#       when: always

# lexus_au_stage_develop_build:
#   variables:
#     BUILD_TYPE: 'develop'
#     BRAND: 'lexus'
#     BUILD_REGION: 'au'
#     CONFIG: 'stage'
#   extends: 
#       - .build_app
#   rules:
#     - if: '$IS_SCHEDULE_DEVELOP_BUILD == "true" && ($MAKE_DEVELOP_BUILDS == "stage" || $MAKE_DEVELOP_BUILDS == "all")'
#       when: always
#     - if: '$IS_RELEASE_CUT_BUILD == "true"'
#       when: always

## Custom Prod Builds ## 
toyota_na_prod_develop_build:
  variables:
    BUILD_TYPE: 'develop'
    BRAND: 'toyota'
    BUILD_REGION: 'na'
    CONFIG: 'prod'
  extends: 
      - .build_app
  rules:
    - if: '$IS_SCHEDULE_DEVELOP_BUILD == "true" && ($MAKE_DEVELOP_BUILDS == "prod" || $MAKE_DEVELOP_BUILDS == "all")'
      when: always
    - if: '$IS_RELEASE_CUT_BUILD == "true"'
      when: always

lexus_na_prod_develop_build:
  variables:
    BUILD_TYPE: 'develop'
    BRAND: 'lexus'
    BUILD_REGION: 'na'
    CONFIG: 'prod'
  extends: 
      - .build_app
  rules:
    - if: '$IS_SCHEDULE_DEVELOP_BUILD == "true" && ($MAKE_DEVELOP_BUILDS == "prod" || $MAKE_DEVELOP_BUILDS == "all")'
      when: always
    - if: '$IS_RELEASE_CUT_BUILD == "true"'
      when: always


subaru_na_prod_develop_build:
  variables:
    BUILD_TYPE: 'develop'
    BRAND: 'subaru'
    BUILD_REGION: 'na'
    CONFIG: 'prod'
  extends: 
      - .build_app
  rules:
    - if: '$IS_SCHEDULE_DEVELOP_BUILD == "true" && ($MAKE_DEVELOP_BUILDS == "prod" || $MAKE_DEVELOP_BUILDS == "all")'
      when: always
    - if: '$IS_RELEASE_CUT_BUILD == "true"'
      when: always

# Commenting for now as Appcenter app not exist
# toyota_au_prod_develop_build:
#   variables:
#     BUILD_TYPE: 'develop'
#     BRAND: 'toyota'
#     BUILD_REGION: 'au'
#     CONFIG: 'prod'
#   extends: 
#       - .build_app
#   rules:
#     - if: '$IS_SCHEDULE_DEVELOP_BUILD == "true" && ($MAKE_DEVELOP_BUILDS == "prod" || $MAKE_DEVELOP_BUILDS == "all")'
#       when: always
#     - if: '$IS_RELEASE_CUT_BUILD == "true"'
#       when: always

# lexus_au_prod_develop_build:
#   variables:
#     BUILD_TYPE: 'develop'
#     BRAND: 'lexus'
#     BUILD_REGION: 'au'
#     CONFIG: 'prod'
#   extends: 
#       - .build_app
#   rules:
#     - if: '$IS_SCHEDULE_DEVELOP_BUILD == "true" && ($MAKE_DEVELOP_BUILDS == "prod" || $MAKE_DEVELOP_BUILDS == "all")'
#       when: always
#     - if: '$IS_RELEASE_CUT_BUILD == "true"'
#       when: always



display_all_develop_builds:
  stage: display
  extends: .display_build_links
  needs:
    - job: toyota_na_stage_develop_build
      artifacts: true
      optional: true
    - job: lexus_na_stage_develop_build
      artifacts: true            
      optional: true
    - job: subaru_na_stage_develop_build
      artifacts: true
      optional: true
    # - job: toyota_au_stage_develop_build
    #   artifacts: true
    #   optional: true
    # - job: lexus_au_stage_develop_build
    #   artifacts: true
    #   optional: true
    - job: toyota_na_prod_develop_build
      artifacts: true
      optional: true
    - job: lexus_na_prod_develop_build
      artifacts: true
      optional: true
    - job: subaru_na_prod_develop_build
      artifacts: true
      optional: true
    # - job: toyota_au_prod_develop_build
    #   artifacts: true
    #   optional: true
    # - job: lexus_au_prod_develop_build
    #   artifacts: true
    #   optional: true
  when: always
  rules:
     - if: $MAKE_DEVELOP_BUILDS 
