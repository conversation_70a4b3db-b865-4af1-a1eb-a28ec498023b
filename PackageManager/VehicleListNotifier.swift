// Copyright © 2023 Toyota. All rights reserved.

import Foundation
import VehicleFeature
import RxSwift
import RxDataSources
import NetworkClients
import Combine

class VehicleListNotifier {
    static let shared = VehicleListNotifier()
    private let disposeBag = DisposeBag()
    private var cancellable = Set<AnyCancellable>()
    func observeVinList() {
        mainStore.observe { state in state.registrationState?.vehicleList ?? [] }
            .subscribe(on: MainScheduler.asyncInstance)
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] vehicleList in
                guard let self = self else { return }
                vehicleContainer.setVehicleList(vehicleList.map { self.mapVehicle(vehicle: $0) })
                WatchConnectivity.shared.syncData()
            }).disposed(by: self.disposeBag)
        
        DigitalKeyManager.shared?.keysManager.dkVehicleChanges
            .subscribe(on: MainScheduler.asyncInstance)
            .subscribe(onNext: { [weak self] changes in
                OALog("vehicleListChanges \(changes.vins) keys: \(changes.keys)")
                self?.fetchSharedDKVehicle(keys: changes.keys)
                WatchConnectivity.shared.syncData()
            }).disposed(by: self.disposeBag)

        mainStore.observe { state in state.registrationState?.currentVehicle?.vin ?? "" }
            .distinctUntilChanged()
            .subscribe(onNext: { [weak self] currentVin in
                DeeplinkManager.shared.notifyForVehicleSelection(AppState.currentVehicle)
                guard let self = self else { return }
                if let vehicle = mainStore.state.registrationState?.currentVehicle {
                    vehicleContainer.setSelectedVehicle(self.mapVehicle(vehicle: vehicle))
                } else {
                    vehicleContainer.setSelectedVehicle(nil)
                }
                WatchConnectivity.shared.syncData()
            }).disposed(by: self.disposeBag)
        observeVehicleFeature()
    }
    
    func observeVehicleFeature() {
        vehicleContainer.rawVehicleListString.publisher.receive(on: DispatchQueue.main).sink { rawVehicleString in
            let data = Data(rawVehicleString.utf8)
            do {
                
                let vehicleListValue = try JSONDecoder().decode([VehicleListValue].self, from: data)
                let vehicleList = vehicleListValue.map { VehicleList($0) }
                mainStore.dispatch(SetVehicleListAction(vehicleList: vehicleList))
                WatchConnectivity.shared.syncData()
            } catch {
                return
            }
        }.store(in: &cancellable)
        
        vehicleContainer.selectedVehicleUseCases().state.receive(on: DispatchQueue.main).sink { vehicle in
            guard let vehicle else { return }
            let vin = vehicle.vin
            let vinList = mainStore.state.registrationState?.vehicleList
            let selectedVehicle = vinList?.first(where: { vehicle in
                vehicle.vin == vin
            })
            guard let selectedVehicle else { return }
            guard let dispatchVehicle = AppState.currentVehicle else {
                return
            }
            if selectedVehicle.vin != dispatchVehicle.vin {
                mainStore.dispatch(SetCurrentVehicleAction(currentVehicle: selectedVehicle))
            }
            WatchConnectivity.shared.syncData()
        }.store(in: &cancellable)
    }
    
    func mapVehicle(vehicle: VehicleList) -> VehicleFeature.Vehicle {
        // Filtering out only the keys with value as 1 and taking the keys
        let enabledFeatures = vehicle.features?.filter { $0.value == 1 }
        // Converting the string flag to Feature enum
        let features = enabledFeatures?.keys.sorted().compactMap { Feature(rawValue: $0) } ?? []
        
        let maintenanceFeatures = vehicle.features?.filter{ $0.value == 2 }
        let mFeatures = maintenanceFeatures?.keys.sorted().compactMap { Feature(rawValue: $0) } ?? []
        
        let imageURL = URL(string: vehicle.image ?? "")
        let tffLink = mapVehicleLink(vehicle.tffLinks)
        let make = mapVehicleMake(vehicle.brand)
        let generation = mapGeneration(vehicle.generation)
        let nonCV = vehicle.nonCvtVehicle == true
        let region = mapVehicleRegion(vehicle.region)
        let remoteDisplay = mapRemoteDisplay(vehicle.remoteDisplay,
                                             generation: generation,
                                             region: region,
                                             make: make,
                                             vinCapabilities: vehicle.vehicleCapabilities ?? [])
        let shopGenuinePartsURL = getShopGenuinePartsURL(vehicle: vehicle)
        let ctsLink = mapctsLink(vehicle.ctsLinks)
        return VehicleFeature.Vehicle(vin: vehicle.vin,
                                      modelYear: vehicle.modelYear ?? "",
                                      modelName: vehicle.modelName ?? "",
                                      modelDescription: vehicle.modelDescription ?? "",
                                      make: make,
                                      region: region,
                                      generation: generation,
                                      nickName: vehicle.nickName,
                                      features: features,
                                      tffLink: tffLink,
                                      imageURL: imageURL,
                                      faqUrl: URL(string: vehicle.faqUrl ?? ""),
                                      proXSeatsVideoLink: URL(string: vehicle.proXSeatsVideoLink ?? ""),
                                      ctsLink: ctsLink,
                                      nonCV: nonCV,
                                      shopGenuinePartsURL: shopGenuinePartsURL,
                                      isDefault: vehicle.preferred,
                                      capabilities: getCapabilities(vehicle: vehicle),
                                      fuelType: mapFuelType(fuelType: vehicle.fuelType),
                                      remoteGuid: vehicle.remoteUserGuid ?? "",
                                      asiCode: vehicle.asiCode ?? "",
                                      hwType: vehicle.hwType ?? "",
                                      evVehicle: vehicle.evVehicle ?? false,
                                      remoteDisplay: remoteDisplay,
                                      primarySubscriber: vehicle.primarySubscriber,
                                      remoteUser: vehicle.remoteUser,
                                      autoRenewal: vehicle.renewableString,
                                      remoteSubscriptionExists: vehicle.remoteSubscriptionExists ?? false,
                                      subscriberGuid: vehicle.subscriberGuid,
                                      isRemoteShared: isRemoteShared(vehicle: vehicle),
                                      isDigitalKey: vehicle.value?.isDigitalkey ?? false,
                                      maintenanceFeatures: mFeatures,
                                      vehicleCapabilities: vehicle.vehicleCapabilities ?? [])
    }

    private func mapFuelType(fuelType: String?) -> VehicleFeature.FuelType {
        switch fuelType {
        case "E":
            return .pureElectric
        case "I":
            return .pluginHybrid
        case "R":
            return .hydrogenFuelCell
        default:
            return .gas
        }
    }

    private func mapRemoteDisplay(_ remoteDisplay: String?,
                                  generation: VehicleFeature.Generation,
                                  region: Region,
                                  make: VehicleMake,
                                  vinCapabilities: [String]) -> RemoteDisplay {
        let remoteDisplay = VehicleResponse.RemoteDisplay(rawValue: remoteDisplay ?? "")
        switch remoteDisplay {
        case .hide:
            return .hide
            // Toyota 17, 17PLUS -> Activate button -> Headunit image screen
            // Lexus 17 -> Enter Auth Code -> Take to code page
            // Lexus, 17PLUS -> Activate button -> Show sos button popup (IVR)
            
            // Toyota, 21MM, Not Mexico -> Activate Button -> QR Code Scan
            // Toyota, 21MM, Mexico -> Activate Button -> QR Code Scan (Not Applicable)
            // Lexus, 21MM, Not Mexico -> Activate button -> QR Code Scan
            // Lexus, 21MM and Mexico -> Activate button -> Show sos button popup (IVR) QR Scan If Drive Connect Capable
        case .authRequired:
            if make == .toyota {
                return .authRequired
            } else {
                if generation == .seventeenCY {
                    return .authRequiredLexus
                } else if (generation == .seventeenCYPlus) ||
                            (generation == .twentyOneMM && region == .mexico &&
                            !(vinCapabilities.map { $0.lowercased() }.contains("drive connect"))) {
                    return .authRequiredLexusSos
                } else {
                    return .authRequired
                }
            }
        case .subscriptionCancelledRemoteUser:
            return .subscriptionCancelledRemoteUser
        case .subscriptionCancelledPrimaryUser:
            return .subscriptionCancelledPrimaryUser
        case .failed:
            return .failed
        case .pending:
            return .pending
        case .error:
            return .error
        case .activated:
            return .activated
        case .subscriptionExpiredRemoteUser:
            return .subscriptionExpiredRemoteUser
        case .subscriptionExpiredPrimaryUser:
            return .subscriptionExpiredPrimaryUser
        case .vehicleStolen:
            return .vehicleStolen
        case .none:
            return .hide
        }
    }

    private func mapVehicleRegion(_ region: String?) -> VehicleFeature.Region {
        let region = VehicleResponse.Region(rawValue: region ?? "")
        switch region {
        case .usa:
            return .usa
        case .canada:
            return .canada
        case .mexico:
            return .mexico
        case .puertoRico:
            return .puertoRico
        case .hawaai:
            return .hawaai
        case .none:
            return .usa // USA region as default
        }
    }

    private func mapVehicleMake(_ brand: String?) -> VehicleFeature.VehicleMake {
        let brand = VehicleResponse.Brand(rawValue: brand ?? "")
        switch brand {
        case .lexus:
            return .lexus
        case .toyota:
            return .toyota
        case .none:
            return .toyota // Toyota as default
        case .subaru: //temp
            return .subaru
        }
    }

    private func mapGeneration(_ generation: String?) -> VehicleFeature.Generation {
        let gen = VehicleResponse.Generation(rawValue: generation ?? "")
        switch gen {
        case .twentyOneMM:
            return .twentyOneMM
        case .ngEightySix:
            return .ngEightySix
        case .seventeenCYPlus:
            return .seventeenCYPlus
        case .seventeenCY:
            return .seventeenCY
        case .seventeenPreCY:
            return .seventeenPreCY
        case .nonCV, .none:
            return .nonCV
        }
    }

    private func mapVehicleLink(_ link: TffLinks?) -> VehicleFeature.Vehicle.Link? {
        guard let linkString = link?.link,
              let url = URL(string: linkString) else {
            return nil
        }
        return VehicleFeature.Vehicle.Link(name: link?.name,
                                           url: url,
                                           imageURL: URL(string: link?.imageUrl ?? ""),
                                           body: link?.body,
                                           buttonText: link?.buttonText)
    }

    private func mapctsLink(_ link: [String: String?]?) -> VehicleFeature.Vehicle.Link? {
        guard let linkString = link?["link"],let urlVal = linkString,
              let url = URL(string: urlVal) else {
            return nil
        }
        return VehicleFeature.Vehicle.Link(name: link?["name"] ?? "",
                                           url: url,
                                           imageURL: URL(string: (link?["imageUrl"] ?? "") ?? ""),
                                           body: link?["body"] ?? "",
                                           buttonText: link?["buttonText"] ?? "")
    }

    private func mappedCapabilities(_ capability: Capability)
    -> (remote: VehicleResponse.RemoteServiceCapability?,
        extended: VehicleResponse.ExtendedCapability?) {
        switch capability {
        case .startStop:
            return (.estartStopCapable, .remoteEngineStartStop)
        case .lockUnlock:
            return (.dlockUnlockCapable, .doorLockUnlockCapable)
        case .powerTailgate:
            return (.powerTailgateCapable, .powerTailgateCapable)
        case .hazard:
            return (.hazardCapable, .hazardCapable)
        case .climateStart:
            return (nil, .remoteEConnectCapable)
        case .climate:
            return (nil, .climateCapable)
        case .lights:
            return (nil, .lightsCapable)
        case .buzzer:
            return (nil, .buzzerCapable)
        case .trunkLockUnlock:
            return (nil, .trunkLockUnlockCapable)
        case .horn:
            return (nil, .hornCapable)
        case .tailGateLockUnlock:
            return (nil, .powerTailgateCapable)
        case .guestDriver:
            return (.guestDriverCapable, .guestDriver)
        case .secondaryUser:
            return (nil, nil)
        case .remoteShare:
            return (nil, nil)
        case .steeringHeater:
            return (nil, .steeringHeater)
        case .frontDriverSeatHeater:
            return (nil, .frontDriverSeatHeater)
        case .frontPassengerSeatHeater:
            return (nil, .frontPassengerSeatHeater)
        case .rearDriverSeatHeater:
            return (nil, .rearDriverSeatHeater)
        case .rearPassengerSeatHeater:
            return (nil, .rearPassengerSeatHeater)
        case .frontDefogger:
            return (nil, .frontDefogger)
        case .rearDefogger:
            return (nil, .rearDefogger)
        case .frontDriverSeatVentilation:
            return (nil, .frontDriverSeatVentilation)
        case .frontPassengerSeatVentilation:
            return (nil, .frontPassengerSeatVentilation)
        case .rearDriverSeatVentilation:
            return (nil, .rearDriverSeatVentilation)
        case .rearPassengerSeatVentilation:
            return (nil, .rearPassengerSeatVentilation)
        }
    }

//if vehicle has either any one of the remoteCapability or
//extendedCapability is true, then its capable of the remote.
func getCapabilities(vehicle: VehicleList) -> [Capability] {
    let generation = mapGeneration(vehicle.generation)

    let remoteCapabilitiesEnabled = vehicle.remoteServiceCapabilities?.filter { $0.value == true }
    let extendedCapabilitiesEnabled = vehicle.extendedCapabilities?.filter { $0.value == true }

    let remoteCapabilities = remoteCapabilitiesEnabled?.keys.sorted().compactMap { VehicleResponse.RemoteServiceCapability(rawValue: $0) } ?? []

    let extendedCapabilities = extendedCapabilitiesEnabled?.keys.sorted().compactMap { VehicleResponse.ExtendedCapability(rawValue: $0) } ?? []

    let evRemoteCapable = vehicle.capabilities?.contains(where: { ( $0.name != nil && $0.name?.lowercased() == "evremoteservice" ) })

    var capabilities: [Capability] = []

    func isCapable(_ capability: Capability) -> Bool {
        let mapped = mappedCapabilities(capability)
        var enabled = false
        if let extended = mapped.extended {
            enabled = extendedCapabilities.contains(extended)
        }
        if capability == .remoteShare {
            enabled = isRemoteShareEnabled(vehicle: vehicle)
            return enabled
        }
        if capability == .secondaryUser {
            enabled = isSecondaryUser(vehicle: vehicle)
            return enabled
        }
        switch generation {
        case .seventeenCYPlus, .seventeenCY, .seventeenPreCY:
            if let remote = mapped.remote {
                if remoteCapabilities.contains(remote) {
                    enabled = true
                }
            }
            switch capability {
            case .climateStart:
                if evRemoteCapable == true { enabled = true } else { enabled = false }
            case .climate:
                enabled = false
            case .guestDriver:
                enabled = remoteCapabilities.contains(.guestDriverCapable)
            default:
                break
            }
        default:
            break
        }
        return enabled
    }
    capabilities = Capability.allCases.compactMap { capability in
        return isCapable(capability) == true ? capability : nil
    }
    return capabilities;
}



    private func isSecondaryUser(vehicle: VehicleList) -> Bool {
        if vehicle.primarySubscriber == false &&
            vehicle.remoteUser == true {
            return true
        } else {
            return false
        }
    }

    private func isRemoteShareEnabled(vehicle: VehicleList) -> Bool {
        if vehicle.remoteSubscriptionExists ?? false &&
            vehicle.primarySubscriber {
            return true
        } else {
            return false
        }
    }

    func getShopGenuinePartsURL(vehicle: VehicleList) -> URL? {
        let url = vehicle.shopGenuinePartsUrl?.replacingOccurrences(of: "http:", with: "https:").trimmingCharacters(in: .whitespaces)
        var shopGenuinePartsURL: URL?
        if let url = url {
            if url.contains("https://") == false {
                shopGenuinePartsURL = URL(string: "https://\(url)")
            } else {
                shopGenuinePartsURL = URL(string: url)
            }
        }
        return shopGenuinePartsURL
    }

    private func fetchSharedDKVehicle(keys: [DigitalKey]) {
        let vehicleList = mainStore.state.registrationState?.vehicleList ?? []

        var keys = keys
        var vehicleValueList = [VehicleListValue]()

        for vehicle in vehicleList {
            let keyIndex = keys.firstIndex(where: { $0.vin == vehicle.vin && $0.userType == .friend })
            if let vehicleValue = vehicle.value {
                if vehicle is DKVehicleList || vehicleValue.isDigitalkey == true {
                    vehicleValue.isDigitalkey = true
                    if keyIndex == nil {
                        continue // skip this vehicle as key not exsit
                    }
                } else {
                    vehicleValue.isDigitalkey = false
                }
                if vehicleValue.image == nil {
                    var dkVehicleList = vehicleValue
                    if let index = keyIndex, keys[index].vehicleList?.image != nil {
                        dkVehicleList = keys[index].vehicleList?.value ?? vehicleValue
                    }
                    dkVehicleList.isDigitalkey = true
                    vehicleValueList.append(dkVehicleList)
                } else {
                    vehicleValueList.append(vehicleValue)
                }
            }
            if let index = keyIndex {
                keys.remove(at: index)
            }
        }

        for key in keys {
            let isSecondaryVehicle = key.userType != .owner
            if let vehicleValue = key.vehicle.value, isSecondaryVehicle {
                vehicleValue.isDigitalkey = true
                vehicleValueList.append(vehicleValue)
            }
        }
        let returnVehicleList = vehicleValueList.map { VehicleList($0) }
        mainStore.dispatch(SetVehicleListAction(vehicleList: returnVehicleList))
        vehicleContainer.setVehicleList(returnVehicleList.map { self.mapVehicle(vehicle: $0) })
        if returnVehicleList.isEmpty {
            mainStore.dispatch(SetCurrentVehicleAction(currentVehicle: nil))
        } else if AppState.currentVehicle?.vin == "" {
            mainStore.dispatch(SetCurrentVehicleAction(currentVehicle: returnVehicleList.first))
        }
    }

    private func isRemoteShared(vehicle: VehicleList) -> Bool {
        let guid = getGUID()
        guard let remoteGuid = vehicle.remoteUserGuid else { return false }
        guard let subscriberGuid = vehicle.subscriberGuid else { return false }
        if !remoteGuid.isEmpty && remoteGuid != subscriberGuid &&
            guid == subscriberGuid {
            return true
        } else {
            return false
        }
    }

    private func getGUID() -> String {
        return mainStore.state.registrationState?.webToken?.guid ?? ""
    }
    
    private func fetchDKAcceptedVehicle(_ vins: [String]) {
        let vehicleList = mainStore.state.registrationState?.vehicleList ?? []

        var vehicleValueList = [VehicleListValue]()

        var acceptedVehicleList = [VehicleList]()

        for vin in vins {
            acceptedVehicleList = vehicleList.filter({ $0.vin == vin })
        }
        
        for vehicle in acceptedVehicleList {
            if let vehicleValue = vehicle.value {
                if vehicle is DKVehicleList || vehicleValue.isDigitalkey == true {
                    vehicleValue.isDigitalkey = true
                } else {
                    vehicleValue.isDigitalkey = false
                }
                if vehicleValue.image == nil {
                    var dkVehicleList = vehicleValue
                    dkVehicleList.isDigitalkey = true
                    vehicleValueList.append(dkVehicleList)
                } else {
                    vehicleValueList.append(vehicleValue)
                }
            }
        }
        let returnVehicleList = vehicleValueList.map { VehicleList($0) }
        mainStore.dispatch(SetVehicleListAction(vehicleList: returnVehicleList))
        vehicleContainer.setVehicleList(returnVehicleList.map { self.mapVehicle(vehicle: $0) })
        if returnVehicleList.isEmpty {
            mainStore.dispatch(SetCurrentVehicleAction(currentVehicle: nil))
        } else if AppState.currentVehicle == nil {
            mainStore.dispatch(SetCurrentVehicleAction(currentVehicle: returnVehicleList.first))
        }
    }
}
