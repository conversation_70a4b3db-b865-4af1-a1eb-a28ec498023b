// Copyright © 2023 Toyota. All rights reserved.

import Foundation
import BluetoothFeature
import RemoteParkControlFramework
import Navigation
import NativeLinker
import AccountSettingsFeature
import VehicleFeature
import PayFeature
import KeychainAccess
import XcappUI
import CoreLocation

extension NativeLinkerConfigurator {
    enum TFSKeychainKey {
        static var tfsAccessToken: String { "tfs.token.access" }
        static var tfsIdToken: String { "tfs.token.id" }
        static var tfsRefreshToken: String { "tfs.token.refresh" }
        static var tfsTokenExpirationTime: String { "tfs.token.expirationTime" }
        static var tfsTokenString: String { "tfsTokenString" }
    }
    enum TFSUserDefaultsKey {
        static var tfsNewRegisteredUser: String { "TfsNewRegisteredUser" }
    }
}

class NativeLinkerConfigurator {
    static let shared  = NativeLinkerConfigurator()
    let profileService = UserProfileService()
    lazy var keychain = Keychain(service: "flutter_secure_storage_service")
    private let tfsSessionCookie: String = "SSOSESSION"
    
    func configureNativeLinker() {
        nativeLinkerContainer.configure { [weak self] type in
            return self?.handleNativeLinkerType(type)
        }
    }
    
    func clearTfsTokens() {
        self.keychain[TFSKeychainKey.tfsIdToken] = nil
        self.keychain[TFSKeychainKey.tfsAccessToken] = nil
        self.keychain[TFSKeychainKey.tfsRefreshToken] = nil
        self.keychain[TFSKeychainKey.tfsTokenExpirationTime] = nil
        self.keychain[TFSKeychainKey.tfsTokenString] = nil
    }
    
    func clearCookies(unlink: Bool = false) {
        if unlink {
            if let cookies = HTTPCookieStorage.shared.cookies {
                for cookie in cookies where cookie.name == tfsSessionCookie {
                    HTTPCookieStorage.shared.deleteCookie(cookie)
                    break
                }
            }
        } else {
            HTTPCookieStorage.shared.cookies?.forEach(HTTPCookieStorage.shared.deleteCookie)
        }
    }
    
    func handleNewTFSTokens(_ token: TfsTokens) {
        self.keychain[TFSKeychainKey.tfsIdToken] = token.idToken
        self.keychain[TFSKeychainKey.tfsAccessToken] = token.accessToken
        self.keychain[TFSKeychainKey.tfsRefreshToken] = token.refreshToken
        self.keychain[TFSKeychainKey.tfsTokenExpirationTime] = String(token.expiresIn)
        self.keychain[TFSKeychainKey.tfsTokenString] = handleTheTFSToken(token)
    }
    
    private func handleTheTFSToken( _ tfsToken: TfsTokens) -> String? {
        let jsonDictionary: [String: Any] = [
            "access_token": tfsToken.accessToken,
            "refresh_token": tfsToken.refreshToken,
            "id_token": tfsToken.idToken,
            "token_type": tfsToken.tokenType,
            "expires_in": tfsToken.expiresIn
        ]
        
        if let data = try? JSONSerialization.data(withJSONObject: jsonDictionary, options: .withoutEscapingSlashes),
           let jsonStr = String(data: data, encoding: .utf8) {
            return jsonStr
        } else {
            return nil
        }
    }
    
    private func handleNativeLinkerType(_ type: NativeLinkerType) -> Any? {
        var result:Any?
        switch type {
        case .removeVehicle(let removeInput, let removeCompletion):
            handleRemoveVehicle(removeInput, completion: removeCompletion)
            result = nil
        case .fetchUserName(let userNameCompletion):
            self.fetchUserName(completion: userNameCompletion)
            result = nil
        case .logOutUser(let logoutCompletion):
            self.logOutUser(completion: logoutCompletion)
            result = nil
        case .fetchATTToken(let attTokenCompletion):
            self.fetchATTToken(completion: attTokenCompletion)
            result = nil
        case .fetchLocation(let locationCompletion):
            self.fetchLocation(completion: locationCompletion)
        case .fetchLocationWithLatLon(let locationCompletion):
            self.fetchLocationWithLatLon(completion: locationCompletion)
            result = nil
        case .beginDKDownload:
            handleBeginDKDownload()
            result = nil
        case .beginDKSetup:
            handleBeginDKSetup()
            result = nil
        case .openDKManage:
            handleManageDK()
            result = nil
        case .selectKeyRotation(let rotate):
            handleSelectionRotationKey(rotate: rotate)
            result = nil
        case .cancelKeyRotation:
            handleCancelRotationKey()
            result = nil
        case .acceptDKShare:
            handleAcceptDKShare()
            result = nil
        case .declineDKShare:
            handleDeclineDKShare()
            result = nil
        case .cancelDKShare:
            handleCancelDKShare()
            return nil
        case .disappearDKShare:
            handleDisappearDKShare()
            return nil
        case .closeDKDownloadNotification:
            handleCloseDKDownloadNotification()
            result = nil
        case .startBLEManager:
            startBLEManager()
            result = nil
        case .fetchGUID:
            result = fetchGUID()
        case .startRemotePark:
            result =  handleStartRemotePark()
        case .beginDKLock:
            result =  handleDkLock()
        case .beginDKUnlock:
            result =  handleDKUnlock()
        case .newTFSTokens(let newToken):
            handleNewTFSTokens(newToken)
            result = nil
        case .fetchTfsIdToken:
            result = fetchTfsIdToken()
        case .fetchTfsNewRegisteredUser:
            result = fetchTfsNewRegisteredUser()
        case .setTfsNewRegisteredUser(let value):
            setTfsNewRegisteredUser(value: value)
            result = nil
        case .clearTfsTokens:
            clearTfsTokens()
            result = nil
        case .getEnvironment:
            result = getEnviroment()
        case .ftueBaseUrl:
            result = getFtueBaseUrl()
        case .removeVehicleFromVehicleList(let vin):
            result = removeVehiceFromVehicleList(vin)
        case let .isDKOwnerVehicle(vin, isDKOwnerCompletion):
            self.isDKOwnerVehicle(vin: vin, completion: isDKOwnerCompletion)
            result = nil
        case .removeCurrentVehicle:
            result = removeCurrentVehicle()
        case .fetchUserAddress(let showLoading, let userAddressCompletion):
            result = fetchUserAddress(showLoading: showLoading ?? false, completion: userAddressCompletion)
        case .fetchUserPhoneNumber(let showLoading, let userPhoneCompletion):
            result = fetchUserPhoneNumber(showLoading: showLoading ?? false, completion: userPhoneCompletion)
        case .updateVinList(let vinList):
            result = updateVinList(vinList: vinList)
        case .setStoreChargeSessionId(value: let value):
            result = setStoreChargeSessionId(value: value)
        case .setStoreChargePartner(value: let value):
            result = setStoreChargePartner(value: value)
        case .getStoreChargeSessionId:
            result = getStoreChargeSessionId()
        case .getStoreChargePartner:
            result = getStoreChargePartner()
        case .clearChargeSession:
            result = clearChargeSession()
        case .getEmail:
            result = getEmail()
        case .getLocale:
            result = getLocale()
        case .getEVgoResetPasswordURL:
            result = getEVgoForgotPasswordURL()
        case .getEVgoURL:
            result = getEVgoURL()
        case .getWattTimeURL:
            result = getWattTimeURL()
        case .getCPWebAuthBaseURL:
            result = getCPAuthBaseURL()
        case .getCPWebAuthCallbackURL:
            result = getCPWebAuthCallbackURL()
        case .incrementAnnouncementShownCounter:
            result = incrementAnnouncementShownCounter()
        case .timesChargeAssistAnnouncementShown:
            result = numberOfTimesChargeAssistAnnouncementShown()
        case .incrementChargeAssistShownAfterRejection:
            result = incrementNumberOfTimesChargeAssistShownAfterRejection()
        case .timesChargeAssistShownAfterRejection:
            result = numberOfTimesChargeAssistShownAfterRejection()
        case .incrementTimesInvalidZipShown:
            result = incrementNumberOfTimesInvalidZipShown()
        case .timesInvalidZipCodeShown:
            result = numberOfTimesInvalidZipCodeShown()
        case .setInvalidZipCode(let valid):
            result = setInvalidZipCode(valid)
        case .hasInvalidZipCode:
            result = hasInvalidZipCode()
        case .getFavorites:
            result = getFavorites()
        case .loadFavorites:
            result = loadFavorites()
        case .addFavorite(let poi):
            result = addFavorite(poi: poi)
        case .isFavorite(let placeIds):
            result = isFavorites(randomPlaceIds: placeIds)
        case .removeFavorite(let poi):
            result = removeFavorites(poi: poi)
        case .sendToCar(let placeId):
            result = sendToCar(placeId)
        case .isChargeAssistAvailable:
            result = isChargeAssistFeatureEnabled()
        case .isEVIonnaEnabled:
            result = isEVIonnaFeatureEnabled()
        case .isEVTeslaEnabled:
            result = isEVTeslaFeatureEnabled()
        }
        return result
    }
    
    func sendToCar(_ placeId: String) {
        lookupPOI(from: placeId) { poi, service in
            service.sendToCar(poi: poi) { error in
                let result = (error == nil)
                NotificationCenter.default.post(name: .sendToCarResult, object: nil, userInfo: ["result": result])
            }
        }
    }
    
    func getFavorites() -> String {
        var returnResult = ""
        let favorites = mainStore.state.dashboardState?.myDestinations?.favorites ?? []
        if let data = try? JSONEncoder().encode(favorites),
           let jsonString = String(data: data, encoding: .utf8) {
            returnResult = "{\"favorites\":\(jsonString)}"
        } else {
            let error = FlutterStatusErrorDetails(type: "encoding", title: kError, body: kNotificationErrorDefault, details: "Could not encode favorites to JSON.")
            if let data = try? JSONEncoder().encode(error),
               let jsonString = String(data: data, encoding: .utf8) {
                returnResult = jsonString
            } else {
                returnResult = ""
            }
        }
        return returnResult
    }
    
    func lookupPOI(from text: String?, completion: @escaping (POI, POIService) -> Void) -> Bool {
        var returnedResult = false
        let service = POIService()
        guard let text = text else { return false }
        service.getPOI(text: text) { poi, error in
            if let poi = poi {
                completion(poi, service)
            } else if let error = error {
                returnedResult = false
            } else {
                returnedResult = true
            }
        }
        return returnedResult
    }
    
    private func removeFavorites(poi: String) -> Bool {
        var returnResult = false
        lookupPOI(from: poi) { poi, service in
            service.remove(poi: poi) { _, error in
                if let error = error {
                    returnResult = false
                } else {
                    returnResult = true
                }
            }
        }
        return returnResult
    }
    
    private func isFavorites(randomPlaceIds: [String]) -> [String: Bool] {
        let placeIds = Set(randomPlaceIds)
        let favorites = mainStore.state.dashboardState?.myDestinations?.favorites ?? []
        var results = [String: Bool]()
        placeIds.forEach { results[$0] = false }
        let favoriteIds = Set(favorites.compactMap { $0.placeId })
        let matches = placeIds.intersection(favoriteIds)
        matches.forEach { results[$0] = true }
        return results
    }
    
    private func addFavorite(poi: String?) -> Bool {
        var returnResult = false
        lookupPOI(from: poi) { poi, service in
            service.save(poi: poi) { _, error in
                if let error = error {
                    returnResult = false
                } else {
                    let favorites = mainStore.state.dashboardState?.myDestinations?.favorites ?? []
                    if favorites.isEmpty {
                        service.myDestinations { _ in
                            returnResult = true
                        }
                    }
                }
            }
        }
        return returnResult
    }
    
    private func loadFavorites() -> Bool {
        var returnResult = false
        let service = POIService()
        service.myDestinations { destinations in
            if destinations != nil {
                returnResult =  true
            } else {
                returnResult =  false
            }
        }
        return returnResult
    }
    
    private func updateVinList(vinList: String) {
        let data = Data(vinList.utf8)
        do {
            let vehicleListValue = try JSONDecoder().decode([VehicleListValue].self, from: data)
            var vehicleList = vehicleListValue.map { VehicleList($0) }
            DispatchQueue.main.async {
                //Remove to test
                if let dkSharedLists =
                    DigitalKeyManager.shared?.keysManager.vehicleService.fetchSharedDKVehicleLists(),
                   dkSharedLists.isNotEmpty {
                    for dkList in dkSharedLists {
                        if vehicleList.firstIndex(where: { $0.vin == dkList.vin }) != nil {
                            continue
                        }
                        vehicleList.append(dkList) // Must add Existing shared DK vinLists
                    }
                }
                mainStore.dispatch(SetVehicleListAction(vehicleList: vehicleList))
                WatchConnectivity.shared.syncData()
                self.updateDKVehicleList()
            }
        } catch {
            return
        }
    }
    
    private func updateDKVehicleList() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            var changes = [String]()
            if let keys = DigitalKeyManager.shared?.keysManager.keys {
                changes = keys.compactMap{ $0.vin }
                DigitalKeyManager.shared?.keysManager.dkVehicleChanges.onNext((changes, keys))
            }
        }
    }
    
    private func handleRemoveVehicle(_ input: Remove21mmVehicleInput,
                                     completion: @escaping (Remove21mmVehicleResponse) -> Void) {
        
        let guid = mainStore.state.registrationState?.webToken?.guid ?? ""
        RemoveVehicleViewModel().removeDigitalKey(vin: input.vin, completion: {})
        profileService.deregisterVehicle(vin: input.vin, userID: guid) { _, error in
            let vehicleResponse = Remove21mmVehicleResponse(success: error == nil, error: error)
            completion(vehicleResponse)
        }
    }
    
    private func fetchUserName(completion: @escaping (String) -> Void) {
        DispatchQueue.global(qos: .background).async {
            self.profileService.getUserProfile { response in
                let profileName = response?.userProfile.userInfo.profileName.value ?? ""
                completion(profileName)
            }
        }
    }
    
    func fetchUserAddress(showLoading: Bool = true, completion: @escaping (String) -> Void)  {
        let accountService = AccountService()
        DispatchQueue.main.async {
            if showLoading == true {
                ActivityProgressView.show()
            }
            accountService.fetchAccount(guid: AppState.guid ?? "") { account in
                let address = account?.payload?.customer.addresses?.first
                if showLoading {
                    ActivityProgressView.hide()
                }
                completion("\(address?.address ?? "") \(address?.city ?? "") \(address?.state ?? "") \(address?.zipCode ?? "")")
            }
        }
    }
    
    func fetchUserPhoneNumber(showLoading: Bool = true, completion: @escaping (String) -> Void) {
        let accountService = AccountService()
        DispatchQueue.main.async {
            if showLoading {
                ActivityProgressView.show()
            }
            accountService.fetchAccount(guid: AppState.guid ?? "") { account in
                defer {
                    if showLoading {
                        ActivityProgressView.hide()
                    }
                }
                guard let phoneModel = account?.payload?.customer.phoneNumbers?.first?.phoneNumber else {
                    completion("")
                    return
                }
                let phone = phoneModel.getStringValue()
                completion(phone)
            }
        }
    }
    
    private func fetchGUID() -> String {
        let guid = mainStore.state.registrationState?.webToken?.guid ?? ""
        return guid
    }
    
    private func logOutUser(completion: @escaping () -> Void) {
        AuthenticationManager.shared.logout(completion: { _ in
            self.clearTfsTokens()
            self.clearCookies()
            AppUserDefaults.save(key: "tsfSessionReceived", value: false)
            completion()
        })
    }
    
    private func handleBeginDKDownload() {
        guard let dkManager = DigitalKeyManager.shared else { return }
        DispatchQueue.main.async {
            dkManager.beginDownload()
        }
    }
    
    private func handleBeginDKSetup() {
        guard let dkManager = DigitalKeyManager.shared else { return }
        DispatchQueue.main.async {
            dkManager.beginSetup()
        }
    }
    
    private func handleDkLock() {
        guard let dkManager = DigitalKeyManager.shared else { return }
        DispatchQueue.main.async {
            dkManager.loadDigitalKeyRemote(action: .lock)
        }
    }
    
    private func handleDKUnlock() {
        guard let dkManager = DigitalKeyManager.shared else { return }
        DispatchQueue.main.async {
            dkManager.loadDigitalKeyRemote(action: .unlock)
        }
    }
    
    private func handleManageDK() {
        guard let dkManage = DigitalKeyManager.shared else { return }
        DispatchQueue.main.async {
            dkManage.loadDigitalKeyRoute(action: .manageKeySharing)
        }
    }
    
    private func handleSelectionRotationKey(rotate: Bool) {
        guard let dkManage = DigitalKeyManager.shared else { return }
        dkManage.resumeActivateDigitalKey(withOwnerSelection: rotate)
    }
    
    private func handleCancelRotationKey() {
        guard let dkManage = DigitalKeyManager.shared else { return }
        dkManage.cancelActivateDigitalKey()
    }
    
    private func handleAcceptDKShare() {
        guard let taskManager = DigitalKeyManager.shared?.personaTasks else { return }
        taskManager.actionViewCompleted(actionType: .accept)
    }
    
    private func handleDeclineDKShare() {
        guard let taskManager = DigitalKeyManager.shared?.personaTasks else { return }
        taskManager.actionViewCompleted(actionType: .decline)
    }
    
    private func handleCancelDKShare() {
        guard let taskManager = DigitalKeyManager.shared?.personaTasks else { return }
        taskManager.actionViewCompleted(actionType: .cancel)
    }
    
    private func handleDisappearDKShare() {
        guard let taskManager = DigitalKeyManager.shared?.personaTasks else { return }
        taskManager.actionViewCompleted(actionType: .disappear)
    }
    
    private func handleCloseDKDownloadNotification() {
        guard let mop = DigitalKeyManager.shared?.mop else { return }
        let selectedVin = AppState.currentVehicle?.vin ?? EmptyVehicleList().vin
        mop.updateFirstTimeDownload(vin: selectedVin)
    }
    
    private func startBLEManager() {
        if AppState.accountHasDKorRemoteParkVehicles() {
            _ = BluetoothManager.shared
        }
    }
    
    private func handleStartRemotePark() -> Void {
        
        // First time RP start
        BluetoothManager.shared.handleBleStatus = { [weak self] value in
            guard let self = self else { return }
            
            switch value {
            case .poweredOff, .unauthorized, .unsupported:
                self.isBluethoothOffShowAlert()
            case .poweredOn:
                DispatchQueue.main.async {
                    if let rootVC = mainWindow.rootViewController {
                        RemoteParkControlManager.sharedInstance.launchFramework(rootVC)
                    }
                }
            default:
                break
            }
            BluetoothManager.shared.handleBleStatus = nil
        }
        // Second Time RP start check last status:
        let value = BluetoothManager.shared.getBluetoothStatus()
        switch value {
        case .poweredOff, .unauthorized, .unsupported:
            self.isBluethoothOffShowAlert()
        case .poweredOn:
            BluetoothManager.shared.handleBleStatus = nil
            DispatchQueue.main.async {
                if let rootVC = mainWindow.rootViewController {
                    RemoteParkControlManager.sharedInstance.launchFramework(rootVC)
                }
            }
        default:
            break
        }
        BluetoothManager.shared.scan()
    }
    
    private func fetchTfsIdToken() -> String? {
        let tfsIdToken = self.keychain[TFSKeychainKey.tfsIdToken] ?? nil
        return tfsIdToken
    }
    
    private func fetchTfsNewRegisteredUser() -> Bool {
        let tfsNewRegisteredUser: Bool = AppUserDefaults.load(key: TFSUserDefaultsKey.tfsNewRegisteredUser) ?? false
        return tfsNewRegisteredUser
    }
    
    private func setTfsNewRegisteredUser(value: Bool) {
        AppUserDefaults.save(key: TFSUserDefaultsKey.tfsNewRegisteredUser, value: value)
    }
    
    private func getEnviroment() -> String {
        return ServiceConfiguration.currentConfiguration()
    }
    
    private func getFtueBaseUrl() -> String {
        return ServiceConfiguration.getBaseUrl(endpointKey: .FtueBaseUrl)!
    }
    
    private func fetchATTToken(completion: @escaping (String?) -> Void) {
        ATTService().limitToken { token in
            completion(token)
        }
    }
    
    private func fetchLocation(completion: @escaping (String) -> Void) {
        LocationManager.shared.showLocationPermissionDeniedAlert()
        if let location = LocationManager.shared.requestLocation() {
            CLGeocoder().reverseGeocodeLocation(CLLocation(latitude: location.coordinate.latitude,
                                                           longitude: location.coordinate.longitude)) {  placemarks, _  in
                completion(placemarks?.first?.administrativeArea ?? "")
            }
        }
    }
    
    private func fetchLocationWithLatLon(completion: @escaping (OAUserLocation) -> Void) {
        LocationManager.shared.showLocationPermissionDeniedAlert()
        if let location = LocationManager.shared.requestLocation() {
            completion(OAUserLocation(latitude: location.coordinate.latitude, longitude: location.coordinate.longitude))
        }
    }
    
    private func removeVehiceFromVehicleList(_ vin: String) {
        DispatchQueue.main.async {
            let updatedVehicleList = AppState.vehicleList?.filter { $0.vin != vin }
            mainStore.dispatch(SetVehicleListAction(vehicleList: updatedVehicleList))
        }
    }
    
    private func removeCurrentVehicle() {
        DispatchQueue.main.async {
            mainStore.dispatch(SetCurrentVehicleAction(currentVehicle: nil))
        }
    }
    
    private func isDKOwnerVehicle(vin: String, completion: @escaping (Bool) -> Void) {
        if let digKey = mainStore.state.digitalKey.keys?[vin] {
            completion(digKey.userType == .owner)
            return
        }
        completion(false)
    }
    
    //MARK: EV Charge management helpers
    func setStoreChargeSessionId(value: String?) {
        AppUserDefaults.save(key: "setStoreChargeSessionId", value: value)
    }
    func setStoreChargePartner(value: String?) {
        AppUserDefaults.save(key: "setStoreChargePartner", value: value)
    }
    func getStoreChargeSessionId() -> String? {
        AppUserDefaults.load(key: "setStoreChargeSessionId") as String?
    }
    func getStoreChargePartner() -> String? {
        AppUserDefaults.load(key: "setStoreChargePartner") as String?
    }
    func clearChargeSession() {
        setStoreChargeSessionId(value: "")
        setStoreChargePartner(value: "")
    }
    func getEmail() -> String? {
        return mainStore.state.registrationState?.webToken?.email
    }
    func getEVgoForgotPasswordURL() -> String {
        ServiceConfiguration.getBaseUrl(endpointKey: .EVGO_FORGET_PASSWORD_URL) ?? ""
    }
    func getEVgoURL() -> String {
        ServiceConfiguration.getBaseUrl(endpointKey: .EVGOURL) ?? ""
    }
    func getWattTimeURL() -> String {
        ServiceConfiguration.getBaseUrl(endpointKey: .WATT_TIME_URL) ?? ""
    }
    func getLocale() -> String {
        return LocalizationHelper.getLocale()
    }
    func getCPAuthBaseURL() -> String {
        return ServiceConfiguration.getBaseUrl(endpointKey: .CP_WEB_AUTH_BASE_URL) ?? ""
    }
    func getCPWebAuthCallbackURL() -> String {
        return ServiceConfiguration.getBaseUrl(endpointKey: .CP_WEB_AUTH_CALLBACK_URL) ?? ""
    }
    
    // MARK: Charge Assist Helpers
    func incrementAnnouncementShownCounter() {
        var numberOfTimesShown = numberOfTimesChargeAssistAnnouncementShown()
        numberOfTimesShown += 1
        AppUserDefaults.save(key: "chargeAssistAnnouncementShownTimes", value: numberOfTimesShown)
    }
    
    func numberOfTimesChargeAssistAnnouncementShown() -> Int {
        return AppUserDefaults.load(key: "chargeAssistAnnouncementShownTimes") as Int? ?? 0
    }
    
    func incrementNumberOfTimesChargeAssistShownAfterRejection() {
        var numberOfTimesShown = numberOfTimesChargeAssistShownAfterRejection()
        numberOfTimesShown += 1
        AppUserDefaults.save(key: "chargeAssistAnnouncementShownAfterRejectionTimes", value: numberOfTimesShown)
    }
    
    func numberOfTimesChargeAssistShownAfterRejection() -> Int {
        return AppUserDefaults.load(key: "chargeAssistAnnouncementShownAfterRejectionTimes") as Int? ?? 0
    }
    
    func incrementNumberOfTimesInvalidZipShown() {
        var numberOfTimesShown = numberOfTimesInvalidZipCodeShown()
        numberOfTimesShown += 1
        AppUserDefaults.save(key: "chargeAssistInvalidZipCodeShown", value: numberOfTimesShown)
    }
    
    func numberOfTimesInvalidZipCodeShown() -> Int {
        return AppUserDefaults.load(key: "chargeAssistInvalidZipCodeShown") as Int? ?? 0
    }
    
    func setInvalidZipCode(_ valid: Bool) {
        AppUserDefaults.save(key: "invalidZipCode", value: valid)
    }
    
    func hasInvalidZipCode() -> Bool {
        return AppUserDefaults.load(key: "invalidZipCode") as Bool? ?? false
    }

    func isChargeAssistFeatureEnabled() -> Bool {
        isChargeAssistEnabled
    }
    
    func isEVIonnaFeatureEnabled() -> Bool {
        isEVIonnaEnabled
    }

    func isEVTeslaFeatureEnabled() -> Bool {
        isEVTeslaEnabled
    }
}

extension NativeLinkerConfigurator {
    private func isBluethoothOffShowAlert() {
        BluetoothManager.shared.handleBleStatus = nil
        AlertUtility.showAlertWithHandler(title: kRemoteParkBluethoothOffAlertTitle,
                                          message: kRemoteParkBluethoothOffAlertBody,
                                          okButtonTitle: kCancel, cancelButtionTitle: kSettings) { action in
            if action != .Ok {
                if let settingsUrl = URL(string: "App-prefs:General") {
                    UIApplication.shared.open(settingsUrl, options: [:], completionHandler: nil)
                }
            }
        }
    }
}

extension Notification.Name {
    static let sendToCarResult = Notification.Name("sendToCarResult")
}





