// Copyright © 2023 Toyota. All rights reserved.

import Foundation
import Navigation
import Utility
import DashboardFeature
import GloveboxFeature
import EVFeature
import VehicleInfoFeature
import ClimateFeature
import VehicleSoftwareFeature
import AnnouncementCenterFeature
import OACommon
import PayFeature
import Components
import VehicleInfoFeature
import ServiceFeature
import Analytics
import SwiftUI

class NavigationHelper {
    static let shared = NavigationHelper()
    private let appSuiteViewModel = AppSuiteViewModel()
    func configureNavigation() {
        navigationContainer.setNativeNavigationHandler { [weak self] navigation in
            self?.handleNativeNavigation(navigation)
        }
    }

    private func handleNativeNavigation(_ navigation: NativeNavigation) {
        switch navigation {
        case .addVehicle, .findStations, .createPHEVSchedule, .removeVehicle:
            handleVehicleNavigation(navigation)
        case .chargeInfo, .chargingScreen, .chargeStatistics, .chargeHistory,
                .chargeSchedule, .manualSchedule, .createModifyChargeSchedule,
                .evgoComplimentaryPopup, .showLCFSDataConsent,
                .chargeAssistConsent, .chargeAssistLearnMore, .chargeAssistAnnouncement,
                .zipCodeNotAvailable, .zipCodeNotEligible,
                .chargeAssistApplicationSubmitted, .chargeAssistSchedule,
                .chargeAssistScheduleLearnMore, .ecoChargeLearnMore, .chargeAssistDisableAnnouncement,
                .chargeAssistAccountDetails, .chargeAssistUnenrollAnnouncement,
                .chargeAssistProgramLearnMoreView:
            handleChargeNavigation(navigation)
        case .serviceAppointmentInitialPage, .vehicleHealthDetailsPage,
                .vehicleHealthReport, .maintananceSchedule, .serviceHistory,
                .preferredDealer, .preferredServiceDealer, .selectPreferredDealer:
            handleServiceNavigation(navigation)
        case .financeOptionsPage, .financeCreatePaymentPage, .financeRequestMFA:
            handleFinanceNavigation(navigation)
        case .subscription, .navigateToSubscription, .insuranceDetails, .vehicleSoftware,
                .drivePulseAndTrips, .bannerConset, .evSwap, .announcements, .mdc,
                .evPartnerEnrollmentScreen, .notificationSettings, .evIonnaConsentScreen:
            handleOtherNavigation(navigation)
        default:
            handleCommonCases(navigation)
        }
    }

    private func handleVehicleNavigation(_ navigation: NativeNavigation) {
        switch navigation {
        case .addVehicle:
            handleAddVehicle()
        case .findStations:
            handleFindStations()
        case .createPHEVSchedule(let isDeparture, let isClimate):
            let v = chargeInfoContainer.createSchedulePHEV(isDeparture: isDeparture, isClimate: isClimate)
            navigationContainer.navigationUseCases.presentView(v)
        case .removeVehicle(let vin):
            handleRemoveVehicle(vin: vin)
        default:
            break
        }
    }

    private func handleChargeNavigation(_ navigation: NativeNavigation) {
        switch navigation {
        case .chargeInfo(let isChargeNow, let useWattTimeScreen):
            handleChargeInfo(isChargeNow: isChargeNow, useWattTimeScreen: useWattTimeScreen)
        case .chargingScreen:
            handleChargingScreen()
        case .chargeStatistics:
            let v = chargeInfoContainer.statisticsScreen()
            navigationContainer.navigationUseCases.presentView(v)
        case .chargeHistory:
            let v = chargeInfoContainer.chargeHistoryScreen()
            navigationContainer.navigationUseCases.presentView(v)
        case .chargeSchedule:
            let v = chargeInfoContainer.chargeScheduleScreen()
            navigationContainer.navigationUseCases.presentView(v)
        case .manualSchedule:
            let v = chargeInfoContainer.manualScheduleScreen()
            navigationContainer.navigationUseCases.presentView(v)
        case .createModifyChargeSchedule(let isChargeManagement):
            let v = chargeInfoContainer.createModifyScheduleScreen(isChargeManagement: isChargeManagement)
            navigationContainer.navigationUseCases.presentView(v)
        case .evgoComplimentaryPopup(let expiryDays, let walletSetup):
            let v = chargeInfoContainer.eVgoExpiryPopupView(expiryDays: expiryDays, walletSetup: walletSetup)
            navigationContainer.navigationUseCases.presentView(v, presentationStyle: .pageSheet, 
                                                               animate: true, detent: .medium())
        case .showLCFSDataConsent(let image, let isO32DVehicle):
            if isO32DVehicle {
                if isEVFlutterDepreciation {
                    let v = chargeInfoContainer.cleanAssistDetailView()
                    navigationContainer.navigationUseCases.presentView(v)
                } else {
                    FlutterUtility.shared.showFlutterPage(route: "CLEAN_ASSIST_DETAIL_PAGE")
                }
            } else {
                if let details = image {
                    let controller = LCFSCardDetailViewController(details: details)
                    UIApplication.getTopMostViewController()?.navigationController?
                        .pushViewController(controller, animated: true)
                }
            }
        case .chargeAssistAnnouncement(let program, let userDetails):
            let v = chargeAssistContainer.chargeAssistAnnouncement(program: program, userDetails: userDetails)
            navigationContainer.navigationUseCases.presentView(v,
                                                               presentationStyle: .pageSheet,
                                                               animate: true,
                                                               detent: .medium())
        case .chargeAssistDisableAnnouncement:
            let v = chargeAssistContainer.chargeAssistScheduleDisableAnnouncement()
            navigationContainer.navigationUseCases.presentView(v,
                                                               presentationStyle: .pageSheet,
                                                               animate: true,
                                                               detent: .medium())
        case .chargeAssistUnenrollAnnouncement:
            let v = chargeAssistContainer.chargeAssistUnenrollAnnouncement()
            navigationContainer.navigationUseCases.presentView(v,
                                                               presentationStyle: .pageSheet,
                                                               animate: true,
                                                               detent: .medium())
        case .chargeAssistProgramLearnMoreView(let program):
            let v = chargeAssistContainer.chargeAssistProgramLearnMoreView(program: program)
            navigationContainer.navigationUseCases.pushView(v, animate: true)
        case .chargeAssistLearnMore(let program, let userDetails):
            let v = chargeAssistContainer.chargeAssistLearnMore(program: program,
                                                                userDetails: userDetails)
            navigationContainer.navigationUseCases.presentView(v,
                                                               presentationStyle: .fullScreen,
                                                               animate: true,
                                                               detent: .medium())
        case .chargeAssistConsent(let program, let userDetails):
            let v = chargeAssistContainer.chargeAssistConsent(program: program,
                                                              userDetails: userDetails)
            navigationContainer.navigationUseCases.pushView(v, animate: true)
        case .chargeAssistAccountDetails(let program, let user):
            let v = chargeAssistContainer.chargeAssistAccountDetails(program: program, user: user)
            navigationContainer.navigationUseCases.pushView(v, animate: true)
        case .zipCodeNotAvailable:
            let v = chargeAssistContainer.zipCodeNotAvailable()
            navigationContainer.navigationUseCases.pushView(v, animate: true)
        case .zipCodeNotEligible:
            let v = chargeAssistContainer.zipCodeNotEligible()
            navigationContainer.navigationUseCases.presentView(v,
                                                               presentationStyle: .pageSheet,
                                                               animate: true,
                                                               detent: .medium())
        case .chargeAssistApplicationSubmitted:
            let v = chargeAssistContainer.chargeAssistApplicationSubmitted()
            navigationContainer.navigationUseCases.pushView(v, animate: true)
        case .chargeAssistSchedule:
            let v = chargeAssistContainer.chargeAssistScheduleScreen()
            navigationContainer.navigationUseCases.pushView(v, animate: true)
        case .chargeAssistScheduleLearnMore:
            let v = chargeAssistContainer.chargeAssistScheduleLearnMore()
            navigationContainer.navigationUseCases.pushView(v, animate: true)
        case .ecoChargeLearnMore(let wattTimeUrl):
            let v = chargeInfoContainer.ecoChargingLearnMoreView(wattTimeUrl: wattTimeUrl)
            navigationContainer.navigationUseCases.pushView(v, animate: true)
        default:
            break
        }
    }

    private func handleServiceNavigation(_ navigation: NativeNavigation) {
        switch navigation {
        case .serviceAppointmentInitialPage(let odometerValue, let odometerUnit):
            handleServiceAppointmentInitialPage(odometerValue: odometerValue, odometerUnit: odometerUnit)
        case .vehicleHealthDetailsPage(let navigationModel, let calldealer):
            handleVehicleHealthDetailsPage(navigationModel: navigationModel, calldealer: calldealer)
        case .vehicleHealthReport(let vehicleHealthDetailsResponse):
            handleVehicleHealthReport(vehicleHealthDetailsResponse: vehicleHealthDetailsResponse)
        case .maintananceSchedule(let odometerValue, let odometerUnit):
            handleMaintananceSchedule(odometerValue: odometerValue, odometerUnit: odometerUnit)
        case .serviceHistory(let serviceHistory):
            handleServiceHistory(serviceHistory: serviceHistory)
        case .preferredDealer(let dealerData, let odometerValue, let odometerUnit):
            handlePreferredDealer(dealerData: dealerData, odometerValue: odometerValue, odometerUnit: odometerUnit)
        case .preferredServiceDealer:
            handlePreferredServiceDealer()
        case .selectPreferredDealer(let emptyDealerData):
            handleSelectPreferredDealer(emptyDealerData: emptyDealerData)
        default:
            break
        }
    }

    private func handleFinanceNavigation(_ navigation: NativeNavigation) {
        switch navigation {
        case .financeOptionsPage(let accountData):
            handleFinanceOptionsPage(accountData: accountData)
        case .financeCreatePaymentPage(let accountData):
            handleFinanceCreatePaymentPage(accountData: accountData)
        case .financeRequestMFA(let authenticateRequest):
            handleFinanceRequestMFA(authenticateRequest: authenticateRequest)
        default:
            break
        }
    }

    private func handleOtherNavigation(_ navigation: NativeNavigation) {
        switch navigation {
        case .subscription(let arguments):
            handleSubscription(arguments: arguments)
        case .navigateToSubscription:
            handleNavigateToSubscription()
        case .insuranceDetails(let offerID, let showUbiCard):
            handleInsuranceDetails(offerID: offerID, showUbiCard: showUbiCard)
        case .vehicleSoftware(let is21MMUpdate, let notificationStatus):
            handleVehicleSoftware(is21MMUpdate: is21MMUpdate, notificationStatus: notificationStatus)
        case .drivePulseAndTrips(let tripsData):
            handleDrivePulseAndTrips(tripsData: tripsData)
        case .bannerConset:
            handleBannerConsent()
        case .evSwap(let subtitle):
            handleEvSwap(subtitle: subtitle)
        case .announcements(let list):
            handleAnnouncements(list: list)
        case .mdc(let consentId, let vin):
            handleMdc(consentId: consentId, vin: vin)
        case .evPartnerEnrollmentScreen(let partner):
            let v = chargeInfoContainer.partnerEnrollmentTermsScreen(partner: partner)
            navigationContainer.navigationUseCases.presentView(v)
        case .notificationSettings:
            handleNotificationSettings()
        case .evIonnaConsentScreen(let termsAndConditions):
            let v = chargeInfoContainer.ionnaConsentScreen(termsAndConditions: termsAndConditions)
            navigationContainer.navigationUseCases.presentView(v)
        // this should change depending fo the charging network in the future
        case .chargingStationsConsentScreen(let termsAndConditions):
            let v = chargeInfoContainer.ionnaConsentScreen(termsAndConditions: termsAndConditions)
            navigationContainer.navigationUseCases.presentView(v)
        default:
            break
        }
    }
    private func handleCommonCases(_ navigation: NativeNavigation) {
        switch navigation {
        case .userProfile:
            handleUserProfile()
        case .walletPage:
            handleWalletPage()
        case .evWalletPage:
            handleEvWalletPage()
        case .evWalletAddCardPage:
            handleEvWalletAddCardPage()
        case .chargeManagement(let evPublicChargingControl):
            handleChargeManagement(publicChargingSupport: evPublicChargingControl)
        case .notificationHistory:
            handleNotificationHistory()
        case .siriShortCuts:
            handleSiriShortCuts()
        case .appSuit:
            handleAppSuit()
        case .climate:
            handleClimate()
        case .driverAlert:
            handleDriverAlert()
        case .remoteActivation:
            handleRemoteActivation()
        case .destinations:
            handleDestinations()
        case .rentals:
            handleRentals()
        case .appointments:
            handleAppointments()
        case .gloveBox:
            handleGloveBox()
        case .financeLinkAccount:
            handleFinanceLinkAccount()
        case .financeOnlineAgreement(let brandName):
            handleFinanceOnlineAgreement(brandName: brandName)
        case .financeElectronicAgreement(let brandName):
            handleFinanceElectronicAgreement(brandName: brandName)
        case .tfsErrorPopup:
            handleErrorPopup()
        default:
            break
        }
    }

    private func handleUserProfile() {
        LocalizableHelper.hideLinkedAccountsPerRegion(completion: { _ in
            mainStore.dispatch(PushView(viewState: .accountSettings))
        })
    }

    private func handleAddVehicle() {
        mainStore.dispatch(PushView(viewState: .addVehicleQR))
    }

    private func handleFindStations() {
        if isEVFlutterDepreciation {
            let v = chargeInfoContainer.findStationsScreen()
            navigationContainer.navigationUseCases.presentView(v)
        } else {
            FlutterUtility.shared.showFlutterPage(
                route: "VEHICLE_SEARCH_CHARGE_STATION_LOCATION",
                routeData: ["isFromNative" : true])
        }
    }

    private func handleWalletPage() {
        mainStore.dispatch(PushView(viewState: .paymentMethods()))
    }

    private func handleEvWalletAddCardPage() {
        let view = eVWalletContainer.addCardScreen()
        navigationContainer.navigationUseCases.presentView(view)
    }
    
    private func handleEvWalletPage() {
        if nativeEVWalletEnabled {
            let view = eVWalletContainer.eVWalletHomeScreen()
            navigationContainer.navigationUseCases.presentView(view)
        } else {
            FlutterUtility.shared.showFlutterPage(
                route: "WALLET_HOME",
                presentationStyle: .fullScreen,
                channel: "native_flutter_navigation_channel",
                animationType: .bottomTop,
                isPush: false
            )
        }
    }

    private func handleChargingScreen() {
        let v = chargeInfoContainer.chargingScreen()
        navigationContainer.navigationUseCases.presentView(v)
    }

    private func handleChargeInfo(isChargeNow: Bool, useWattTimeScreen: Bool) {
        if isEVFlutterDepreciation {
            if useWattTimeScreen {
                let v = chargeInfoContainer.chargeInfoScreen(fromChargeNow: isChargeNow)
                navigationContainer.navigationUseCases.presentView(v)
            } else {
                let v = chargeInfoContainer.chargeManagementScreen()
                navigationContainer.navigationUseCases.presentView(v)
            }
        } else {
            FlutterUtility.shared.showFlutterPage(route: useWattTimeScreen ? "EV_CHARGE_INFO_PAGE"
                                                  : "VEHICLE_CHARGE_MANAGEMENT_SCREEN",
                                                  routeData: ["isChargeNow": isChargeNow],
                                                  animationType: .bottomTop, isPush: false)
        }
    }

    private func handleChargeManagement(publicChargingSupport: Bool) {
        if isEVFlutterDepreciation {
            if publicChargingSupport {
                handleChargingScreen()
            } else {
                let v = chargeInfoContainer.chargeInfoScreen()
                navigationContainer.navigationUseCases.presentView(v)
            }
        } else {
            FlutterUtility.shared.showFlutterPage(route: "DRIVER_CHARGE_MANAGEMENT_PAGE", animationType: .bottomTop)
            FlutterUtility.shared.showFlutterPage(route: "DRIVER_CHARGE_MANAGEMENT_PAGE", animationType: .bottomTop)
        }
    }
    
    private func handleServiceAppointmentInitialPage(odometerValue: String, odometerUnit: String) {
        if isNativeDSASupported {
            let screen = serviceContainer.serviceInitialScreen()
            navigationContainer.navigationUseCases.presentView(screen)
            analyticsContainer.analyticsUseCases.logEvent(AnalyticsEvents.ServicePage.makeAnAppointment)
        } else {
            let routeData = ["odometerValue": odometerValue, "odometerUnit": odometerUnit]
            FlutterUtility.shared.showFlutterPage(route: "VEHICLE_SERVICE_APPOINTMENT_INITIAL_PAGE", routeData: routeData)
        }
    }

    private func handleVehicleHealthDetailsPage(navigationModel: VehicleHealthNavigation,
                                                calldealer: @escaping () -> Void) {
        if vehicleHealthDetailsPageV2 {
            let screen = vehicleHealthContainer.vehicleHealthDetailsScreen(
                navigation: navigationModel,
                locale: AppState.preferredLanguage ?? "en-US",
                callDealer: calldealer
            )
            navigationContainer.navigationUseCases.presentView(screen)
        } else {
            FlutterUtility.shared.showFlutterPage(
                route: "VEHICLE_HEALTH_DETAIL_PAGE",
                presentationStyle: .fullScreen,
                routeData: navigationModel.dictionary ?? [:],
                isPush: false
            )
        }
    }

    private func handleVehicleHealthReport(vehicleHealthDetailsResponse: String?) {
        self.moveToHealthReportDetail(vehicleHealthDetailsResponse)
    }

    private func handleInsuranceDetails(offerID: String, showUbiCard: Bool) {
        if showUbiCard {
            let controller = UBIOptinViewController.createFromStoryboard()
            UIApplication.getTopMostViewController()?.navigationController?.pushViewController(controller,
                                                                                               animated: true)
        } else {
            self.loadUBIOffer(offerID: offerID)
        }
    }

    private func handleSubscription(arguments: SubscriptionNavigation) {
        self.addSubscription(arguments: arguments, selectedVehicle: AppState.currentVehicle)
    }

    private func handleNavigateToSubscription() {
        dashboardContainer.executeDeepLinkToSubscriptions()
    }

    private func handleNotificationHistory() {
        UIApplication.getTopMostViewController()?.navigationController?.pushViewController(NotificationHistoryController(), animated: true)
    }

    private func handleSiriShortCuts() {
        if let currentVehicle = AppState.currentVehicle {
            mainStore.dispatch(PushView(viewState: .siriShortcut(vehicle: currentVehicle)))
        }
    }

    private func handleAppSuit() {
        DispatchQueue.main.async {
            mainStore.dispatch(SetCurrentVehicleAction(currentVehicle: AppState.currentVehicle))
            if let currentVehicle = AppState.currentVehicle {
                let vehicle = DashboardVehicle(currentVehicle)
                self.appSuiteViewModel.handleAppSuiteAction(vehicle: vehicle)
            }
        }
    }

    private func handleVehicleSoftware(is21MMUpdate: Bool, notificationStatus: Int) {
        guard let currentVehicle = AppState.currentVehicle else { return }

        if is21MMUpdate && nativeVehicleSoftwareEnabled {
            let v = ota21MMUpdateContainer.ota21MMUpdateScreen(notificationStatus: notificationStatus)
            navigationContainer.navigationUseCases.pushView(v)
        } else if !is21MMUpdate && nativeVehicleSoftwareEnabled {
            let v = vehicleSoftwareContainer.vehicleSoftwareScreen(notificationStatus: notificationStatus)
            navigationContainer.navigationUseCases.pushView(v)
        } else {
            let route = "VEHICLE_INFO_SOFTWARE_UPDATE"
            let routeData: [String: Any] = ["notificationStatus": notificationStatus]
            FlutterUtility.shared.showFlutterPage(route: route, routeData: routeData)
        }
    }

    private func handleClimate() {
        if climateScreenV2 {
            let v = climateContainer.climateScreen()
            navigationContainer.navigationUseCases.presentView(v)
        } else {
            FlutterUtility.shared.showFlutterPage(route:"VEHICLE_CLIMATE_PAGE")
        }
    }

    private func handleDriverAlert() {
        if nativeGuestDriver {
            let screen = guestDriverContainer.guestDriverLandingScreen()
            navigationContainer.navigationUseCases.presentView(screen)
        } else {
            FlutterUtility.shared.showFlutterPage(route: "VEHICLE_DRIVER_ALERT_PAGE")
        }
    }

    private func handleRemoteActivation() {
        if let currentVehicle = AppState.currentVehicle {
            self.remoteServicesAuthRequired(for: currentVehicle)
        }
    }

    private func handleDestinations() {
        if let currentVehicle = AppState.currentVehicle {
            let vehicle = DashboardVehicle(currentVehicle)
            mainStore.dispatch(SetCurrentVehicleAction(currentVehicle: currentVehicle))
            POIService().myDestinations { myDestiations in
                let viewModel = MyDestinationsDashboardCardViewModel()
                viewModel.emptyVehicle = vehicle.isEmptyVehicle
                viewModel.setMyDestination(myDestiations)
                mainStore.dispatch(PushView(viewState: .myDestinations))
            }
        }
    }

    private func handleRentals() {
        FlutterUtility.shared.showFlutterPage(route: "FX_DETAIL_LANDING")
    }

    private func handleDrivePulseAndTrips(tripsData: VehicleTripStatusNavigation) {
        guard let tripsDataDict = tripsData.dictionary else {
            return
        }
        if drivePulseAndTrips {
            let screen = driverPulseAndTripsContainer.drivePulseAndTripsLandingScreen(tripsData: tripsDataDict)
            navigationContainer.navigationUseCases.presentView(screen)
        } else {
            FlutterUtility.shared.showFlutterPage(route: "VEHICLE_TRIP_PAGE", routeData: tripsDataDict)
        }
    }

    private func handleSelectPreferredDealer(emptyDealerData: SelectPreferredDealerNavigation) {
        guard let emptyDealerDataDict = emptyDealerData.dictionary else {
            return
        }
        if isNativeDSASupported {
            preferredDealerContainer.serviceNavigateToDealerSelect()
        } else {
            FlutterUtility.shared.showFlutterPage(route: "FX_SELECTPREFERDEALERPAGE", routeData: emptyDealerDataDict)
        }
    }

    private func handlePreferredDealer(dealerData: PreferredDealerNavigation,
                                       odometerValue: String,
                                       odometerUnit: String) {
        guard let dealerDataDict = dealerData.dictionary else {
            return
        }
        if isNativeDSASupported {
            let screen = preferredDealerContainer.vehicleDealerScreen()
            navigationContainer.navigationUseCases.presentView(screen)
        } else {
            let routeData:[String: Any] = ["odometerValue": odometerValue, "odometerUnit": odometerUnit]
            FlutterUtility.shared.showFlutterPage(route: "FX_VEHICLEDEALERDETAILPAGE",
                                                  routeData: dealerDataDict.merging(routeData) {
                (current, _) in current
            })
        }
    }

    private func handlePreferredServiceDealer() {
        if isNativeDSASupported {
            preferredDealerContainer.serviceNavigateToDealerSelect()
        } else {
            FlutterUtility.shared.showFlutterPage(route: "PREFERRED_SERVICE_DEALER",
                                                  routeData: ["dealerInfoPayload": ""], isPush: false)
        }
    }

    private func handleMaintananceSchedule(odometerValue: String, odometerUnit: String) {
        if isNativeDSASupported {
            let screen = serviceContainer.maintenanceScheduleScreen()
            navigationContainer.navigationUseCases.presentView(screen, presentationStyle: .fullScreen)
        } else {
            let routeData = ["odometerValue": odometerValue, "odometerUnit": odometerUnit]
            FlutterUtility.shared.showFlutterPage(route: "MAINTANANCE_SCHEDULE", routeData: routeData)
        }
    }

    private func handleServiceHistory(serviceHistory: ServiceHistoryNavigation) {
        guard let serviceHistoryDict = serviceHistory.dictionary else {
            return
        }
        if isNativeDSASupported {
            let screen = serviceContainer.serviceHistoryScreen()
            navigationContainer.navigationUseCases.presentView(screen, presentationStyle: .fullScreen)
        } else {
            FlutterUtility.shared.showFlutterPage(route: "SERVICE_HISTORY", routeData: serviceHistoryDict)
        }
    }

    private func handleAppointments() {
        if isNativeDSASupported {
            let screen = serviceContainer.appointmentsScreen()
            navigationContainer.navigationUseCases.presentView(screen)
        } else {
            FlutterUtility.shared.showFlutterPage(route: "APPOINTMENTS")
        }
    }

    private func handleRemoveVehicle(vin: String) {
        let vehicleList = mainStore.state.registrationState?.vehicleList
        DispatchQueue.main.async {
            let currentVehicle = vehicleList?.first(where: { $0.vin == vin })
            self.removeVehicle(selectedVehicle: currentVehicle)
        }
    }

    private func handleGloveBox() {
        if nativeVehicleGloveboxEnabled {
            let v = gloveboxContainer.gloveboxScreen()
            navigationContainer.navigationUseCases.pushView(v)
        } else {
            FlutterUtility.shared.showFlutterPage(route: "GLOVE_BOX_PAGE")
        }
    }

    private func handleFinanceOptionsPage(accountData: TFSAccountNavigation) {
        guard let accountDataDict = accountData.dictionary else {
            return
        }
        FlutterUtility.shared.showFlutterPage(route: "FINANCE_OPTIONS_PAGE", routeData: accountDataDict)
    }

    private func handleFinanceCreatePaymentPage(accountData: TFSAccountNavigation) {
        guard let accountDataDict = accountData.dictionary else {
            return
        }
        FlutterUtility.shared.showFlutterPage(route: "FINANCE_CREATE_PAYMENT_PAGE", routeData: accountDataDict)
    }

    private func handleFinanceRequestMFA(authenticateRequest: TFSRequestMFANavigation) {
        guard let authenticateRequestDict = authenticateRequest.dictionary else {
            return
        }
        FlutterUtility.shared.showFlutterPage(route: "FINANCE_ACCESS_ACCOUNT_PAGE", routeData: authenticateRequestDict)
    }

    private func handleFinanceLinkAccount() {
        if nativePayEnabled {
            let linkAccountScreen = linkAccountContainer.linkAccountScreen()
            navigationContainer.navigationUseCases.presentView(linkAccountScreen)
        } else {
            FlutterUtility.shared.showFlutterPage(route: "FINANCE_LINK_ACCOUNT_PAGE")
        }
    }

    private func handleErrorPopup() {
        let view = linkAccountContainer.errorPopupView()
        navigationContainer.navigationUseCases.presentView(view, presentationStyle: .pageSheet,
                                                           animate: true, detent: .medium())
    }

    private func handleFinanceOnlineAgreement(brandName: String) {
        if nativePayEnabled {
            FlutterUtility.shared.showFlutterPage(route: "FINANCE_ONLINE_AGREEMENT")
        } else {
            guard let privacyPolicyURL = PayHelper.shared.privacyPolicyURL(brandName: brandName),
                  let url = URL(string: privacyPolicyURL) else { return }
            let view = safariContainer.safariView(url: url)
            navigationContainer.navigationUseCases.presentView(view, presentationStyle: .fullScreen)
        }
    }

    private func handleFinanceElectronicAgreement(brandName: String) {
        if nativePayEnabled {
            FlutterUtility.shared.showFlutterPage(route: "FINANCE_ELECTRONIC_AGREEMENT")
        } else {
            guard let businessAgreementURl = PayHelper.shared.businessAgreementURl(brandName: brandName),
                  let url = URL(string: businessAgreementURl) else { return }
            let view = safariContainer.safariView(url: url)
            navigationContainer.navigationUseCases.presentView(view, presentationStyle: .fullScreen)
        }
    }

    private func handleShowLCFSDataConsent(image: String?, isO32DVehicle: Bool) {
        if isO32DVehicle {
            FlutterUtility.shared.showFlutterPage(route: "CLEAN_ASSIST_DETAIL_PAGE")
        } else {
            if let details = image {
                let controller = LCFSCardDetailViewController(details: details)
                UIApplication.getTopMostViewController()?.navigationController?.pushViewController(controller,
                                                                                                   animated: true)
            }
        }
    }

    private func handleBannerConsent() {
        navigateToBannerConsent()
    }

    private func handleEvSwap(subtitle: String) {
        let routeData = ["avlCredit": subtitle]
        FlutterUtility.shared.showFlutterPage(route: "VEHICLE_ANNOUNCEMENT_EV_SWAP_OVERVIEW", routeData: routeData)
    }

    private func handleAnnouncements(list: String) {
        if nativeAnnouncementCenterEnabled {
            let v = announcementCenterContainer.announcementCenterScreen()
            navigationContainer.navigationUseCases.pushView(v)
        } else {
            let routeData = ["announcementsList": list]
            FlutterUtility.shared.showFlutterPage(route: "VEHICLE_ANNOUNCEMENTS", routeData: routeData)
        }
    }

    private func handleMdc(consentId: String, vin: String) {
        let vehicleList = mainStore.state.registrationState?.vehicleList?.first(where: { $0.vin == vin })
        mainStore.dispatch(PushView(viewState: .updateMDC(flowType: .vehicleSwitch, consentId: consentId, vehicle: vehicleList)))
    }

    private func handleNotificationSettings() {
        mainStore.dispatch(PushView(viewState: .notificationSettings))
    }
}
