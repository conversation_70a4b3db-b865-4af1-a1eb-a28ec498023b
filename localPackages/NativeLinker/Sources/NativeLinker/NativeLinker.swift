// Copyright © 2023 Toyota. All rights reserved.

import Foundation

public struct OAUserLocation {
    public var latitude: Double
    public var longitude: Double

    public init(latitude: Double, longitude: Double) {
        self.latitude = latitude
        self.longitude = longitude
    }
}

public enum NativeLinkerType {
    case removeVehicle(Remove21mmVehicleInput, (Remove21mmVehicleResponse) -> Void)
    case fetchUserName((String) -> Void)
    case fetchUserAddress(showLoading: Bool?, (String) -> Void)
    case fetchUserPhoneNumber(showLoading: Bool?, (String) -> Void)
    case fetchGUID
    case logOutUser(() -> Void)
    case fetchATTToken((String?) -> Void)
    case fetchLocation((String) -> Void)
    case fetchLocationWithLatLon((OAUserLocation) -> Void)

    // MARK: Digital Key
    case beginDKDownload
    case beginDKSetup
    case openDKManage
    case selectKeyRotation(Bool)
    case cancelKeyRotation
    case acceptDKShare
    case declineDKShare
    case cancelDKShare
    case disappearDKShare
    case beginDKLock
    case beginDKUnlock
    case closeDKDownloadNotification

    case startRemotePark
    case startBLEManager

    // MARK: TFS/LFS
    case newTFSTokens(TfsTokens)
    case fetchTfsIdToken
    case fetchTfsNewRegisteredUser
    case setTfsNewRegisteredUser(Bool)
    case clearTfsTokens
    case getEnvironment
    case ftueBaseUrl

    case removeVehicleFromVehicleList(String)
    case isDKOwnerVehicle(String, (Bool) -> Void)
    case removeCurrentVehicle
    case updateVinList(String)
    case setStoreChargeSessionId(value: String?)
    case setStoreChargePartner(value: String?)
    case getStoreChargeSessionId
    case getStoreChargePartner
    case clearChargeSession
    case getEmail
    case getLocale

    case incrementAnnouncementShownCounter
    case timesChargeAssistAnnouncementShown
    case incrementChargeAssistShownAfterRejection
    case timesChargeAssistShownAfterRejection
    case incrementTimesInvalidZipShown
    case timesInvalidZipCodeShown
    case setInvalidZipCode(_ valid: Bool)
    case hasInvalidZipCode

    // MARK: EV
    case getEVgoResetPasswordURL
    case getEVgoURL
    case getWattTimeURL
    case getCPWebAuthBaseURL
    case getCPWebAuthCallbackURL
    case getFavorites
    case loadFavorites
    case addFavorite(String)
    case isFavorite([String])
    case removeFavorite(String)
    case sendToCar(String)
    case isChargeAssistAvailable
    case isEVIonnaEnabled
    case isEVTeslaEnabled
}

public typealias NativeCompletion = (NativeLinkerType) -> Any?

public class NativeLinker {
    var nativeLinkCompletion: NativeCompletion?
    private var accountUserProfileLogic = AccountUserProfileLogic(completion: nil)
    private var remove21mmVehicleLogic = Remove21mmVehicleLogic(completion: nil)
    private var removeCurrentVehicleLogic = RemoveCurrentVehicleLogic(completion: nil)
    private var digitalKeyLogic = DigitalKeyLogic(completion: nil)
    private var remoteParkLogic = RemoteParkLogic(completion: nil)
    private var financialServiceLogic = FinancialServiceLogic(completion: nil)
    private var firstTimeLinkerLogic = FirstTimeLinkerLogic(completion: nil)
    private var vehicleSwitchNativeLogic = VehicleSwitchNativeLogic(completion: nil)
    private var chargeManagementLogic = ChargeManagementLogic(completion: nil)
    private var chargeAssistAnnouncementLogic = ChargeAssistAnnouncementLogic(completion: nil)
    private var poiServiceLogic = PoiServiceLogic(completion: nil)

    public func configure(with completion: @escaping NativeCompletion) {
        self.nativeLinkCompletion = completion
        updateNativeLinkCompletions()
    }

    private func updateNativeLinkCompletions() {
        accountUserProfileLogic.completion = nativeLinkCompletion
        remove21mmVehicleLogic.completion = nativeLinkCompletion
        digitalKeyLogic.completion = nativeLinkCompletion
        remoteParkLogic.completion = nativeLinkCompletion
        financialServiceLogic.completion = nativeLinkCompletion
        firstTimeLinkerLogic.completion = nativeLinkCompletion
        vehicleSwitchNativeLogic.completion = nativeLinkCompletion
        removeCurrentVehicleLogic.completion = nativeLinkCompletion
        chargeManagementLogic.completion = nativeLinkCompletion
        chargeAssistAnnouncementLogic.completion = nativeLinkCompletion
        poiServiceLogic.completion = nativeLinkCompletion
    }

    public func accountUserProfileUseCases() -> AccountUserProfileUseCases {
        accountUserProfileLogic
    }

    public func remove21mmVehicleUseCases() -> Remove21mmVehicleUseCases {
        remove21mmVehicleLogic
    }

    public func removeCurrentVehicleUsesCase() -> RemoveCurrentVehicleUseCases {
        removeCurrentVehicleLogic
    }

    public func digitalKeyUseCases() -> DigitalKeyUseCases {
        digitalKeyLogic
    }

    public func remoteParkUseCases() -> RemoteParkUseCases {
        remoteParkLogic
    }

    public func financialServiceUseCases() -> FinancialServiceUseCases {
        financialServiceLogic
    }

    public func firstTimeLinkerUseCase() -> FirstTimeLinkerUseCases {
        firstTimeLinkerLogic
    }

    public func vehicleSwitchNativeUseCase() -> VehicleSwitchNativeUseCase {
        vehicleSwitchNativeLogic
    }

    public func chargeManagementUseCases() -> ChargeManagementUseCases {
        chargeManagementLogic
    }

    public func chargeAssistAnnouncementUseCases() -> ChargeAssistAnnouncementUseCases {
        chargeAssistAnnouncementLogic
	}

    public func poiServiceUseCases() -> PoiServiceUseCases {
        poiServiceLogic
    }

    public func isChargeAssistFeatureEnabled() -> Bool {
        guard let completion = self.nativeLinkCompletion else { return false }
        return completion(.isChargeAssistAvailable) as? Bool ?? false
    }

    public func isEVIonnaEnabled() -> Bool {
        guard let completion = self.nativeLinkCompletion else { return false }
        return completion(.isEVIonnaEnabled) as? Bool ?? false
    }

    public func isEVTeslaEnabled() -> Bool {
        guard let completion = self.nativeLinkCompletion else { return false }
        //return completion(.isEVTeslaEnabled) as? Bool ?? false
        return true
    }
}

public let nativeLinkerContainer = NativeLinker()
