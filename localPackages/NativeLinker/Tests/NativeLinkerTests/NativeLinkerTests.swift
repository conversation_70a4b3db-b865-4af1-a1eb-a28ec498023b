// Copyright © 2023 Toyota. All rights reserved.

import XCTest
@testable import NativeLinker

final class NativeLinkerTests: XCTestCase {

    var nativeLinker: NativeLinker!

    override func setUp() {
        super.setUp()
        nativeLinker = NativeLinker()
    }

    override func tearDown() {
        super.tearDown()
        nativeLinker = nil
    }

    // when you enable this test will fail, change it with the appropiate Assert
    func testIsEVTeslaEnabled() {
        XCTAssertFalse(nativeLinker.isEVTeslaEnabled())
    }

    func testIsEVTeslaDisabled() {
        XCTAssertTrue(!nativeLinker.isEVTeslaEnabled())
    }

}
