// Copyright © 2023 Toyota. All rights reserved.

import Foundation

public enum NavigableViewID: Equatable {
    /// This refer to the complete tabview
    case rootTabView
    /// This is for demo purpose only will be remove later
    case advancedRemote
    case parkRemote
    case vehicleSwitcher
    case vehicleInfo
    case vehicleHealthSheet
    case vehicleSoftware
    case ota21MMUpdateScreen
    case ota21MMUpdateDetails
    case ota21MMUpdateTerms
    case glovebox
    case vehicleSpecs
    case vehicleManuals
    case howToVideos
    case dashboardLights
    case accountSettings
    case signOut
    // Digital key popups
    case dkPopups
    case insuranceOffer
    case siriusXM
    case safari
    case subscription
    case skipFirstTime
    case toolTip
    case firstTimeUser
    case whatsNew
    case announcements
    // Native navigations
    case accountSettingsNative
    case addVehicleQRNative
    case nativeScreen(String)
    case shopAnnouncements
    case vehicleHealthDetailScreen
    case chargeInfo
    case chargeHistory
    case chargeSchedule
    case manualSchedule
    case createModifyScheduleScreen
    case newScheduleScreenPHEV
    case chargeStatistics
    case cleanAssistDetails
    case ionnaConsentScreen
    case chargingStationsConsentScreen
    case evPartnerEnrollment
    case evgoComplimentaryPopup
    case findStations
    case cas
    case casReportScreen
    case climate
    case climateDetail
    case climateScheduleClimate
    case announcementCenter
    case announcementCenterDetails
    case linkAccount
    case tfsErrorPopup
    case chargingScreen
    // Charge Assist
    case chargeAssistAnnouncement
    case chargeAssistLearnMore
    case chargeAssistScheduleLearnMore
    case chargeAssistAccountDetails
    case chargeAssistConsent
    case zipCodeNotAvailable
    case zipCodeNotEligible
    case chargeAssistApplicationSubmitted
    case chargeAssistSchedule
    case chargeAssistDisableAnnouncement
    case chargeAssistUnenrollAnnouncement
    case chargeAssistProgramLearnMoreView

    case ecoChargeLearnMore

    case guestDriverScreen
    case guestProfile
    case shareRemoteScreen
    case addDriverScreen
    case drivingLimitsScreen
    case drivingLimitDetailsScreen
    case evWalletHomeScreen
    case individualCardScreen
    case addCardScreen
    case transactionsScreen
    case transactionScreen
    // Service Feature
    case serviceInitialPageNative
    case maintenanceScheduleScreen
    case vehicleDealerDetails
    case selectPreferredDealer
    case makeAppointmentDetails
    case serviceEstimateScreen
    case dateAndTimeStepView
    case appointmentsScreen
    case serviceHistory
    case appointmentScreen
    case vehicleDealerFilters
    case appointmentDetailScreen
    case addAddressScreen
    case drivePulseAndTrips
    case optOutScreen
    case driverScoreInfoScreen
    case optInScreen
    case clearTripsScreen
    case tripDetailsScreen
    case addRecordView
}
