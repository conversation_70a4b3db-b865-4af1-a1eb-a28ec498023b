// Copyright © 2023 Toyota. All rights reserved.

import Foundation
import SwiftUI

public enum NativeNavigation {
    case userProfile
    case addVehicle
    case findStations
    case chargeInfo(isChargeNow: Bool, useWattTimeScreen: Bool)
    case chargingScreen
    case chargeHistory
    case chargeSchedule
    case manualSchedule
    case chargeAssistSchedule
    case chargeStatistics
    case createModifyChargeSchedule(isChargeManagement: Bool)
    case createPHEVSchedule(isDeparture: Bool, isClimate: Bool)
    case chargeManagement(evPublicChargingControl: Bool)
    case serviceAppointmentInitialPage(odometerValue: String, odometerUnit: String)
    case vehicleHealthDetailsPage(arguments: VehicleHealthNavigation, callDealer: () -> Void)
    case vehicleHealthReport(arguments: String?)
    case insuranceDetails(offerID: String, showUbiCard: Bool)
    case subscription(arguments: SubscriptionNavigation)
    case navigateToSubscription
    case notificationHistory
    case siriShortCuts
    case appSuit
    case vehicleSoftware(is21MMUpdate: Bool, notificationStatus: Int)
    case driverAlert
    case climate
    case walletPage
    case evWalletPage
    case evWalletAddCardPage
    case remoteActivation
    case destinations
    case rentals
    case preferredDealer(arguments: PreferredDealerNavigation, odometerValue: String, odometerUnit: String)
    case selectPreferredDealer(arguments: SelectPreferredDealerNavigation)
    case preferredServiceDealer
    case drivePulseAndTrips(arguments: VehicleTripStatusNavigation)
    case maintananceSchedule(odometerValue: String, odometerUnit: String)
    case serviceHistory(arguments: ServiceHistoryNavigation)
    case appointments
    case removeVehicle(vin: String)
    case gloveBox
    case financeOptionsPage(arguments: TFSAccountNavigation)
    case financeCreatePaymentPage(arguments: TFSAccountNavigation)
    case financeLinkAccount
    case financeOnlineAgreement(brandName: String)
    case financeElectronicAgreement(brandName: String)
    case financeRequestMFA(arguments: TFSRequestMFANavigation)
    case showLCFSDataConsent(image: String?, isO32DVehicle: Bool)
    case bannerConset
    case evSwap(subtitle: String)
    case announcements(routeData: String)
    case mdc(consentId: String, vin: String)
    case evPartnerEnrollmentScreen(partner: String)
    case evgoComplimentaryPopup(expiryDays: Int, walletSetup: Bool)
    case notificationSettings
    case tfsErrorPopup
    case chargeAssistAnnouncement(program: ChargeAssistProgramNavigation?,
                                  userDetails: UserPayloadNavigation)
    case chargeAssistLearnMore(program: ChargeAssistProgramNavigation?,
                               userDetails: UserPayloadNavigation)
    case chargeAssistConsent(program: ChargeAssistProgramNavigation?,
                             userDetails: UserPayloadNavigation)
    case chargeAssistAccountDetails(program: ChargeAssistProgramNavigation,
                                    user: UserPayloadNavigation)
    case zipCodeNotAvailable
    case zipCodeNotEligible
    case chargeAssistApplicationSubmitted
    case chargeAssistScheduleLearnMore
    case ecoChargeLearnMore(String)
    case chargeAssistDisableAnnouncement
    case chargeAssistUnenrollAnnouncement
    case chargeAssistProgramLearnMoreView(program: ChargeAssistProgramNavigation)
    case evIonnaConsentScreen(termsAndConditions: String)
    case chargingStationsConsentScreen(termsAndConditions: String)
}
