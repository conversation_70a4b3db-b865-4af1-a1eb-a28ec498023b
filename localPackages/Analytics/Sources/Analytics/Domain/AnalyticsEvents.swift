// Copyright © 2023 Toyota. All rights reserved.

import Foundation

public enum AnalyticsEvents {
    static let eventName = "eventName"
    static let eventError = "eventErrorName"

    enum Group: String, AnalyticsEvent {
        case dashboardCard = "dashboard_card"
        case digitalKey = "digitalKey"
        case vehicleSwitcher = "vehicle_switcher"
        case vehicleInfo = "vehicle_info"
        case vehicleGlovebox = "garage_select"
        case vehicleSoftware = "vehicle_software"
        case subscriptions = ""
        case accountNotifications = "acct_ntfns"
        case announcementCenter = "announcement_center"
        case shopTab = "bottomnav_shoptab"
        case payTab = "bottomnav_paytab"
        case find = "bottomnav_findtab"
        case vehicleHealth = "vehicle_health"
        case ftue = "ftue"
        case vehicleEvPubChgGroup = "vehicle_ev_pub_chg_group"
        case cas = "cas"
        case casReport = "cas_report_cta"
        case climateOn = "Remote_settings_toggle_ON"
        case climateOff = "Remote_settings_toggle_OFF"
        case wallet = "wallet_group"
        case serviceSchedule = "ServiceSchedule"
        var name: String {
            self.rawValue
        }
    }
}
