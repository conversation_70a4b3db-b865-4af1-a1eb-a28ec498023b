// Copyright © 2024 Toyota. All rights reserved.

import Foundation
import NetworkClients
import VehicleFeature

// swiftlint:disable function_parameter_count
public protocol EvRepo {
    func getChargingStatistics(
        vin: String,
        brand: String,
        generation: String,
        reportType: String,
        month: String?
    ) async -> ChargeStatistics?
    func getChargeHistoryList(
        vin: String,
        brand: String,
        generation: String,
        chargingType: String,
        month: String?,
        startDate: String?,
        endDate: String?
    ) async -> ChargeHistory?
    func getEnrollmentCheck(
        vin: String,
        brand: String,
        email: String
    ) async -> EVEnrollmentCheck?
    func getMultidayScheduleRemoteControlStatus(request: MultidayScheduleRequest) async -> EVMultidayScheduleResponse?
    func deleteMultidayScheduleRemoteControlStatus(
        vin: String,
        brand: String,
        generation: String,
        body: ChargeScheduleSetting
    ) async -> EVPostRealTimeStatusResponse?
    func createMultidayScheduleRemoteControlStatus(
        vin: String,
        brand: String,
        generation: String,
        body: ChargeScheduleSetting
    ) async -> EVPostRealTimeStatusResponse?
    func modifyMultidayScheduleRemoteControlStatus(
        vin: String,
        brand: String,
        generation: String,
        body: ChargeScheduleSetting
    ) async -> EVPostRealTimeStatusResponse?
    func postRealTimeStatus(
        vin: String,
        isEV: Bool,
        brand: String,
        generation: String
    ) async -> (String?, String?)
    func getChargeEcoSchedule(
        vin: String,
        brand: String,
        generation: String
    ) async -> EVEcoSchedulesResponse?
    func ecoEnrollment(
        vin: String,
        brand: String,
        generation: String,
        enabled: Bool
    ) async -> Bool?
    func createPHEVMultidayScheduleRemoteControlStatus(
        vin: String,
        brand: String,
        generation: String,
        body: ChargeScheduleSettingPHEV
    )
        async -> EVPostRealTimeStatusResponse?
    func fetchCleanAssistElegibility(
        vin: String,
        brand: String
    ) async -> CleanAssistElegibilityMapped
    func acceptCleanAssistConsent(
        generation: String,
        appBrand: String,
        vin: String?
    ) async -> Bool
    func acceptIonnaConsent(
        userAccepted: Bool,
        generation: String,
        appBrand: String,
        vin: String?
    ) async -> Bool
    func fetchPartnerEnrollmentTerms(
        brand: String,
        partner: String,
        language: String,
        make: String
    ) async -> LegalContentMapped?
    func registerEvPartner(
        _ params: PostEnrollmentParams
    ) async -> Result<
        PostEnrollment, ResponseFailure
    >

    func fetchLCFSDashboard(
        vin: String,
        brand: String,
        locale: String
    ) async -> LcfsDashboardMapped?
    func fetchWallet() async -> EvWallet?
    func getHydrogenStations(
        brand: String,
        generation: String,
        fuelType: String,
        radius: String,
        zip: String,
        region: String,
        latitude: String,
        longitude: String
    ) async -> EvNearestFuelStations?
    func getStationsList(
        brand: String,
        fuelType: String,
        zip: String,
        region: String,
        latitude: String,
        longitude: String,
        vin: String,
        filters: StationsFilter?
    )
        async -> FindEvStationsListResponse?
    func startCharging(
        make: String,
        vin: String,
        body: ChargeStartRequest
    ) async
        -> StartCharge?
    func stopCharging(
        make: String,
        vin: String,
        region: String,
        chargeId: String
    ) async
        -> EvChargeSession?
    func chargeSession(
        make: String,
        vin: String
    ) async
        -> Result<EvChargeSession?, OneapiError>
    func getVerifyElectricStatusRealTime(
        for vin: String,
        brand: VehicleMake,
        generation: Generation,
        requestNo: String
    ) async -> Bool
    func getVerifyElectricStatusRemoteControl(
        for vin: String,
        isEV: Bool,
        brand: VehicleMake,
        generation: Generation,
        requestNo: String
    ) async -> Bool
    func getRemoteControlStatus(
        vin: String,
        brand: String,
        generation: String,
        requestNo: String
    ) async -> EVMultidayScheduleResponse?
    func startHomeCharging(
        vin: String,
        generation: String,
        brand: String
    ) async -> String?
}
