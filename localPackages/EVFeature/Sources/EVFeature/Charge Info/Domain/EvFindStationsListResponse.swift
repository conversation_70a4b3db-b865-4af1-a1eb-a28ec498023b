// Copyright © 2024 Toyota. All rights reserved.

import Foundation

public struct FindEvStationsListResponse: Codable {
    public var evTotalNoOfRecords: Int?
    public var evStations: [EvStation]
}

public struct EvStation: Codable, Equatable {
    public var evId: String?
    public var evName: String?
    public var evTariffInfo: [EvTariffInfo]?
    public var evPhoneNumber: String?
    public var evPlaceId: String?
    public var evPostalCode: String?
    public var evProvince: String?
    public var evAddress: String?
    public var evCity: String?
    public var evCountry: String?
    public var evLatitude: Double?
    public var evLongitude: Double?
    public var evOperator: String?
    public var evStatusCode: String?
    public var evStatusSum: String?
    public var evTimeZone: String?
    public var evEvses: [EvEVSE]
    public var evEvDcFastNum: Int?
    public var evEvLevel1EvseNum: Int?
    public var evEvLevel2EvseNum: Int?
    public var evIsPartner: Bool
    public var evEvSource: String
    public var evOpeningTimes: EvOpeningTimes?
    public var evConnectorSum: EvConnectorSum?
    public var isFavourite: Bool
    public var partnerInfoName: String?
    public var partnerInfoId: String?

    public static func == (lhs: EvStation, rhs: EvStation) -> Bool {
        return lhs.isEqual(to: rhs)
    }

    private func isEqual(to other: EvStation) -> Bool {
        return self.evId == other.evId
    }

    public func formattedEvSource() -> String {
        switch self.evEvSource.lowercased() {
        case "evgo":
            return FindStationsConstants.partnerFilterEvgo
        case "chargepoint":
            return FindStationsConstants.partnerFilterChargePoint
        default:
            return self.evEvSource
        }
    }

    func isPartner(isWalletEnabled: Bool) -> Bool {
        if isWalletEnabled {
            return isRoamingPartner || evIsPartner
        } else {
            return !isRoamingPartner || !evIsPartner && !isChargePoint
        }
    }

    func isRoamingEVgo() -> Bool {
        return isRoamingPartner && partnerInfoName?.lowercased() == FindStationsConstants.evGo
    }

    var isRoamingPartner: Bool {
        return evEvSource.lowercased() == FindStationsConstants.chargePoint.lowercased()
    }

    private var isChargePoint: Bool {
        guard let partnerInfoName = partnerInfoName else {
            return false
        }
        return partnerInfoName.lowercased() == FindStationsConstants.chargePoint.lowercased() ||
            partnerInfoName.lowercased() == FindStationsConstants.partnerNameChargePointNetwork.lowercased()
    }

    func getLocation() -> String {
        let address = ChargingStationAddress(name: evName,
                                             streetAddress: evAddress,
                                             city: evCity,
                                             province: evProvince,
                                             postalCode: evPostalCode,
                                             country: evCountry)
        return address.formattedAddress
    }

}

public struct EvOpeningTimes: Codable {
    public var evRegularHour: String??
    public var evTiming: String?
}

public struct EvEVSE: Codable {
    public var uid: String?
    public var evseId: String?
    public var status: String?
    public var capabilities: [String?]?
    public var connectors: [EvConnector]?
    public var coordinates: Coordinates?
    public var floorLevel: String?
    public var parkingRestrictions: [String?]?
    public var physicalReference: String?
    public var lastUpdated: String?
    public var openingTimes: EvOpeningTimes?

    enum CodingKeys: String, CodingKey {
        case uid
        case evseId = "evse_id"
        case status
        case capabilities
        case connectors
        case coordinates
        case floorLevel = "floor_level"
        case parkingRestrictions = "parking_restrictions"
        case physicalReference = "physical_reference"
        case lastUpdated = "last_updated"
        case openingTimes = "opening_times"
    }

    public var isAvailable: Bool {
        status == FindStationsConstants.available
    }

    public enum Capability {
        static let unlockCapable = "UNLOCK_CAPABLE"
        }
}

public struct EvConnector: Codable {
    public var evAmperage: Int?
    public var evChargerLevel: Int?
    public var evChargerType: String?
    public var evFormat: String?
    public var evId: String?
    public var evLastUpdated: String?
    public var evMaxPower: Double?
    public var evPowerType: String?
    public var evStandard: String?
    public var evTariffId: String?
    public var evVoltage: Int?
}

public struct Coordinates: Codable {
    public var evLatitude: String?
    public var evLongitude: String?
}

public struct EvConnectorDetails: Codable {
    public let evActive: Int?
    public let evTotal: Int?

    public func toString() -> String? {
        guard let evTotal = evTotal, evTotal > 0 else {
            return nil
        }
        return "\(evActive ?? 0)/\(evTotal)"
    }
}

public struct EvConnectorSum: Codable {
    public let evCcs1: EvConnectorDetails?
    public let evChademo: EvConnectorDetails?
    public let evJ1772: EvConnectorDetails?
    public let evNacs: EvConnectorDetails?

    public func getPlugAvailabilityArray() -> [[String]] {
        let evCCs1String = evCcs1?.toString()
        let evChademoString = evChademo?.toString()
        let evj1772String = evJ1772?.toString()
        let evNacsString = evNacs?.toString()
        let plugAvailabilityArray: [[String?]] = [
            [PlugType.level2.displayName, evj1772String],
            [PlugType.dcFast.displayName, evCCs1String],
            [FindStationsConstants.chademoUppercase, evChademoString],
            [PlugType.nacs.displayName, evNacsString]
        ]
        return plugAvailabilityArray.compactMap {
            if $0[$0.count - 1] != nil {
                let first = $0.first ?? ""
                let last = $0.last ?? ""
                return [first ?? "", last ?? ""]
            } else {
                return nil
            }
        }
    }
}

public struct EvTariffInfo: Codable, Equatable, Hashable {
    public let evCurrency: String?
    public let evElements: [EvPriceElement]?
    public let evId: String?
    public let evPartnerName: String?
    public let evTariffAltText: [EvTariffAltText]?
    public let evTariffAltURL: String?
}

public struct EvTariffAltText: Codable, Equatable, Hashable {
    public let evLanguage: String?
    public let evText: String?
}

public struct EvPriceElement: Codable, Equatable, Hashable {
    public let evId = UUID()
    public let evPriceComponents: [EvPriceComponent]?
}

public struct EvPriceComponent: Codable, Comparable, Hashable {
    public static func < (lhs: EvPriceComponent, rhs: EvPriceComponent) -> Bool {
        comparePriceComp(lhs: lhs, rhs: rhs)
    }

    public let evId = UUID()
    public let evPrice: Double?
    public let evStepSize: Int?
    public let evType: String?

    private static func comparePriceComp(lhs: EvPriceComponent, rhs: EvPriceComponent) -> Bool {
        lhs.evId == rhs.evId
    }
}

public struct EvPriceUIHelper {
    public let evRate: Double?
    public let evUnit: String?
}

internal struct EvPlugTariffsHelper: Equatable, Hashable {
    public let id = UUID()
    public let plug: PlugType
    public let tariffs: [EvTariffInfo]
}

public extension EvConnector {
    func mapStandard() -> String {
        let mappedStandard: String
        switch evStandard {
        case "J1772":
            mappedStandard = PlugType.level2.displayName
        case "CCS1":
            mappedStandard = PlugType.dcFast.displayName
        case "IEC_62196_T1_COMBO", "TESLA_S":
            mappedStandard = PlugType.nacs.displayName
        case FindStationsConstants.chademoUppercase:
            mappedStandard = FindStationsConstants.chademo
        default:
            mappedStandard = evStandard ?? "Unknown"
        }
        return mappedStandard
    }

    internal func mapPlugType() -> PlugType? {
        let mappedPlug: PlugType?
        switch evStandard {
        case "J1772":
            mappedPlug = PlugType.level2
        case "CCS1", "IEC_62196_T1_COMBO":
            mappedPlug = PlugType.dcFast
        case "TESLA_S", "NACS":
            mappedPlug = PlugType.nacs
        default:
            mappedPlug = PlugType.level2
        }
        return mappedPlug
    }
}

extension EvStation: EvStationDisplayable {
    public var isUnlockCapable: Bool {
        for evse in evEvses {
            if let capabilities = evse.capabilities,
               capabilities.contains(where: { $0 == EvEVSE.Capability.unlockCapable }) {
                return true
            }
        }
        return false
    }
}
