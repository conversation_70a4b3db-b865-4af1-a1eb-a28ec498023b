// Copyright © 2024 Toyota. All rights reserved.
// swiftlint:disable type_body_length
import Foundation
import NetworkClients
import VehicleFeature

public class EvAPIRepo: EvRepo {
    let provider: VehicleRequestProvider
    let findProvider: FindStationsRequestProvider
    let elegibilityProvider: AnnouncementsRequestProvider
    let providerWallet: WalletRequestProvider
    let chargingProvider: PublicChargingRequestProvider

    init(
        provider: VehicleRequestProvider,
        findProvider: FindStationsRequestProvider,
        elegibilityProvider: AnnouncementsRequestProvider,
        providerWallet: WalletRequestProvider,
        chargingProvider: PublicChargingRequestProvider
    ) {
        self.provider = provider
        self.findProvider = findProvider
        self.elegibilityProvider = elegibilityProvider
        self.providerWallet = providerWallet
        self.chargingProvider = chargingProvider
    }

    public func startHomeCharging(
        vin: String,
        generation: String,
        brand: String
    ) async -> String? {
        let response = await chargingProvider.requestChargeCommand(
            generation: generation,
            vin: vin,
            brand: brand
        )
        switch response {
        case .success(let res):
            return res.payload?.appRequestNo
        case .failure:
            return nil
        }
    }

    public func getChargingStatistics(
        vin: String,
        brand: String,
        generation: String,
        reportType: String,
        month: String? = nil
    ) async -> ChargeStatistics? {
        let response = await provider.getChargeStatistics(
            vin: vin,
            brand: brand,
            generation: generation,
            reportType: reportType,
            month: month
        )
        switch response {
        case .success(let res):
            return mapChargeStatistics(res)
        case .failure:
            return nil
        }
    }

    public func getChargeHistoryList(
        vin: String,
        brand: String,
        generation: String,
        chargingType: String,
        month: String? = nil,
        startDate: String? = nil,
        endDate: String? = nil
    ) async -> ChargeHistory? {
        let response = await provider.getChargeHistoryList(
            vin: vin,
            brand: brand,
            generation: generation,
            chargingType: chargingType,
            month: month,
            startDate: startDate,
            endDate: endDate
        )
        switch response {
        case .success(let res):
            return mapChargeHistory(res)
        case .failure:
            return nil
        }
    }

    private func mapChargeHistory(_ res: ChargeHistoryResponse) -> ChargeHistory {
        var sessions: [EVLastCharge]? = []
        for row in 0..<(res.chargingSessions?.count ?? 0) {
            if let item = res.chargingSessions?[row] {
                sessions?
                    .append(
                        EVLastCharge(
                            chargeLocationType: item.chargeLocationType?.rawValue ?? "",
                            startTime: item.startTime,
                            endTime: item.endTime,
                            socBeforeCharging: item.socBeforeCharging,
                            socAfterCharging: item.socAfterCharging,
                            totalChargeKwhr: item.totalChargeKwhr,
                            latitude: item.latitude,
                            longitude: item.longitude,
                            watttimeDetails: EVWatttimeDetails(
                                chargeClasification:
                                    EVChargeClasification(
                                        rawValue:
                                            item.watttimeDetails?
                                            .chargeClasification?
                                            .rawValue ?? ""
                                    ),
                                region: item.watttimeDetails?.region,
                                co2Value: item.watttimeDetails?.co2Value,
                                healthDollarValue: item.watttimeDetails?.healthDollarValue
                            ),
                            address: item.address,
                            cdrDetails: EVCDRDetails(
                                cardLast4: item.cdrDetails?.cardLast4,
                                partnerName: item.cdrDetails?.partnerName,
                                totalAmount: item.cdrDetails?.totalAmount
                            )
                        )
                    )
            }
        }
        return ChargeHistory(
            vin: res.vin,
            chargingSessions: sessions
        )
    }

    private func mapChargeStatistics(_ res: ChargeStatsResponse) -> ChargeStatistics {
        let week = res.currentWeekReport
        let lastCharge = res.lastCharge
        let monthlyReports = res.monthlyReports
        let report = EVCurrentWeekReport(
            chargingCount: week?.chargingCount,
            leafCount: week?.leafCount,
            totalChargeKwhr: week?.totalChargeKwhr
        )
        let wt = lastCharge?.watttimeDetails
        let wattTimeDetails = EVWatttimeDetails(
            chargeClasification: EVChargeClasification(
                rawValue: wt?.chargeClasification?.rawValue ?? ""
            ),
            region: wt?.region,
            co2Value: wt?.co2Value,
            healthDollarValue: wt?.healthDollarValue
        )
        let lastChargeMap = mapLastCharge(lastCharge, wattTimeDetails: wattTimeDetails)
        var monthlyReportsMap: [EvMonthlyReport]? = []
        monthlyReports?
            .forEach({ report in
                monthlyReportsMap?
                    .append(
                        EvMonthlyReport(
                            monthName: report.monthName,
                            chargingCount: report.chargingCount,
                            leafCount: report.leafCount,
                            totalChargeKwhr: report.totalChargeKwhr,
                            totalCo2: report.totalCo2,
                            healthDollarPercentage: report.healthDollarPercentage,
                            totalCo2EquivalentTreesPlanted: report.totalCo2EquivalentTreesPlanted
                        )
                    )
            })
        return ChargeStatistics(
            currentWeekReport: report,
            monthlyReports: monthlyReportsMap,
            lastCharge: lastChargeMap,
            barChartData: prepareBarChartData(monthlyReports: monthlyReports)
        )
    }

    private func mapLastCharge(_ lastCharge: LastCharge?, wattTimeDetails: EVWatttimeDetails) -> EVLastCharge {
        EVLastCharge(
            chargeLocationType: lastCharge?.chargeLocationType,
            startTime: lastCharge?.startTime,
            endTime: lastCharge?.endTime,
            socBeforeCharging: lastCharge?.socBeforeCharging,
            socAfterCharging: lastCharge?.socAfterCharging,
            totalChargeKwhr: lastCharge?.totalChargeKwhr,
            latitude: lastCharge?.latitude,
            longitude: lastCharge?.longitude,
            watttimeDetails: wattTimeDetails,
            address: nil,
            cdrDetails: EVCDRDetails(
                cardLast4: lastCharge?.cdrDetails?.cardLast4,
                partnerName: lastCharge?.cdrDetails?.partnerName,
                totalAmount: lastCharge?.cdrDetails?.totalAmount
            )
        )
    }

    func prepareBarChartData(monthlyReports: [MonthlyReport]?) -> ChargeStatisticsBarData {
        guard let monthlyReports = monthlyReports, !monthlyReports.isEmpty else { return ChargeStatisticsBarData() }

        let lastMonthReport = monthlyReports.first
        let lastMonthId = lastMonthReport?.monthName?.month.id ?? 0

        var chartBarData: [ChargeStatisticsBar] = []
        var next3MonthsIds: [Int] = []
        var last8MonthsIds: [Int] = []

        // Prepare next 3 months
        next3MonthsIds.append(contentsOf: [lastMonthId + 1, lastMonthId + 2, lastMonthId + 3])
        next3MonthsIds = next3MonthsIds.map { $0 > 12 ? $0 - 12 : $0 }

        // Prepare last 8 months
        for index in 1...8 {
            last8MonthsIds.append(lastMonthId - index)
        }
        last8MonthsIds = last8MonthsIds.map { $0 <= 0 ? (12 + $0) : $0 }

        // Combine month IDs
        let customMonthIdsArray = last8MonthsIds.reversed() + [lastMonthId] + next3MonthsIds

        // Get month names
        let customMonthNamesArray = customMonthIdsArray.map { String($0.monthName) }

        let preparedReports = self.prepareAllMonthsData(
            customMonthsArray: customMonthNamesArray,
            actualMonthlyReport: monthlyReports,
            next3MonthsIds: next3MonthsIds
        )

        for eachMonthReport in preparedReports {
            let monthName = eachMonthReport.monthName ?? ""
            chartBarData.append(
                ChargeStatisticsBar(
                    value: eachMonthReport.totalChargeKwhr ?? 0,
                    label: monthName,
                    highlight: eachMonthReport.monthName == lastMonthReport?.monthName
                )
            )
        }

        return ChargeStatisticsBarData(
            bars: chartBarData,
            nonZeroBars: getNonZeroBarData(barData: chartBarData)
        )
    }

    func prepareAllMonthsData(
        customMonthsArray: [String] = [],
        actualMonthlyReport: [MonthlyReport]? = [],
        next3MonthsIds: [Int] = []
    ) -> [MonthlyReport] {
        var preparedReports: [MonthlyReport] = []
        for monthName in customMonthsArray {
            let defaultMonthlyReport = MonthlyReport(monthName: monthName, totalChargeKwhr: 0)
            if let foundReport = actualMonthlyReport?
                .first(where: { report in
                    (report.monthName?.uppercased() == monthName)
                        && !(next3MonthsIds.contains(report.monthName?.uppercased().month.id ?? 0))
                }) {
                preparedReports.append(foundReport)
            } else {
                preparedReports.append(defaultMonthlyReport)
            }
        }
        return preparedReports
    }

    func getNonZeroBarData(barData: [ChargeStatisticsBar]) -> [ChargeStatisticsBar] {
        let nonZeroValues = barData.filter { $0.value > 0 }
        return nonZeroValues
    }

    public func getEnrollmentCheck(
        vin: String,
        brand: String,
        email: String
    ) async -> EVEnrollmentCheck? {
        let response = await provider.getEnrollmentCheck(
            vin: vin,
            brand: brand,
            email: email
        )
        switch response {
        case .success(let res):
            return mapEnrollmentCheck(res)
        case .failure:
            return nil
        }
    }

    func mapEnrollmentCheck(_ res: EnrollmentCheckResponse) -> EVEnrollmentCheck {
        let messages = res.messages
        let payload = res.payload
        let enrollment = payload?.partnerEnrollment
        let pstat = enrollment?.partnerStatus
        var partnerlist: [EVPartnerStatus] = []
        pstat?
            .forEach { result in
                partnerlist.append(
                    EVPartnerStatus(
                        partnerName: result.partnerName,
                        status: result.status,
                        enrollmentDate: result.enrollmentDate,
                        expiryDate: result.expiryDate
                    )
                )
            }
        return EVEnrollmentCheck(
            messages: EVMessages(
                responseCode: messages?.responseCode,
                description: messages?.description
            ),
            payload: EVEnrollmentPayload(
                showAssociatedBanners: payload?.showAssociatedBanners,
                partnerEnrollment: EVPartnerEnrollment(
                    guid: enrollment?.guid,
                    wallet: enrollment?.wallet,
                    partnerStatus: partnerlist
                )
            )
        )
    }

    public func getMultidayScheduleRemoteControlStatus(
        request: MultidayScheduleRequest
    ) async -> EVMultidayScheduleResponse? {
        let response = await provider.getMultidayScheduleRemoteControlStatus(
            vin: request.vin,
            isEV: request.isEV,
            brand: request.brand,
            generation: request.generation,
            realTime: request.realTime,
            requestNo: request.requestNo
        )

        switch response {
        case .success(let success):
            return mapMultiDayChargingResponse(success)
        case .failure:
            return nil
        }
    }

    public func getRemoteControlStatus(
        vin: String,
        brand: String,
        generation: String,
        requestNo: String
    ) async -> EVMultidayScheduleResponse? {
        let response = await provider.getRemoteControlStatus(
            vin: vin,
            isEV: false,
            brand: brand,
            generation: generation,
            requestNo: requestNo
        )
        switch response {
        case .success(let success):
            return mapMultiDayChargingResponse(success)
        case .failure:
            return nil
        }
    }

}
