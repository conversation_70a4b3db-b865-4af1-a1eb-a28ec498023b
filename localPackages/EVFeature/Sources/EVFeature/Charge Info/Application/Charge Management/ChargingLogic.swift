// Copyright © 2024 Toyota. All rights reserved.
// swiftlint:disable type_body_length
import Foundation
import Combine
import VehicleFeature
import Analytics
import Navigation
import Components
import Theme
import SwiftUI
import Utility
import NativeLinker
import CoreLocation
import LocalizedStrings
import DashboardFeature

internal class ChargingLogic: ChargingUseCase {
    @Published var statePublished = ChargingState.initial
    var state: Published<ChargingState>.Publisher { $statePublished }
    let electricStatusUseCases: ElectricStatusUseCases
    var electricStatusState: ElectricStatusState?
    let telemetryUseCases: TelemetryUseCases
    var telemetryState: TelemetryState?
    var vehicle: Vehicle?
    let selectedVehicleUseCases: SelectedVehicleUseCases
    let analyticsUseCases: AnalyticsUseCases
    let repo: EvAPIRepo
    let navigationUseCases: NavigationUseCases
    let userDefaultsUseCases: UserDefaultsUseCases
    var nativeLinkerLogic: AccountUserProfileUseCases
    let customerCareUseCases: CustomerCareUseCases
    var cancellables = Set<AnyCancellable>()
    var sessionPollingTimer: AnyCancellable?
    let timeOut: Double = 150.0
    let chargingSessionTimeout: Double = 120.0
    var chargingSessionTimeSinceSuccess: Double = 0.0
    var requestNo: String?
    private func observeVehicle() {
        selectedVehicleUseCases.state.sink { [weak self] vehicle in
            guard let self = self else { return }
            self.vehicle = vehicle
        }.store(in: &cancellables)
        electricStatusUseCases.state.sink { [weak self] electricStatusState in
            guard let self = self else { return }
            self.electricStatusState = electricStatusState
            self.updateFuelState()

        }.store(in: &cancellables)
    }

    init(statePublished: ChargingState = ChargingState.initial,
         electricStatusUseCases: ElectricStatusUseCases,
         electricStatusState: ElectricStatusState? = nil,
         telemetryUseCases: TelemetryUseCases,
         telemetryState: TelemetryState? = nil,
         vehicle: Vehicle? = nil, selectedVehicleUseCases: SelectedVehicleUseCases,
         analyticsUseCases: AnalyticsUseCases, repo: EvAPIRepo,
         navigationUseCases: NavigationUseCases, userDefaultsUseCases: UserDefaultsUseCases,
         nativeLinkerLogic: AccountUserProfileUseCases,
         customerCareUseCases: CustomerCareUseCases,
         cancellables: Set<AnyCancellable> = Set<AnyCancellable>()) {
        self.statePublished = statePublished
        self.electricStatusUseCases = electricStatusUseCases
        self.electricStatusState = electricStatusState
        self.telemetryUseCases = telemetryUseCases
        self.telemetryState = telemetryState
        self.vehicle = vehicle
        self.selectedVehicleUseCases = selectedVehicleUseCases
        self.analyticsUseCases = analyticsUseCases
        self.repo = repo
        self.navigationUseCases = navigationUseCases
        self.userDefaultsUseCases = userDefaultsUseCases
        self.nativeLinkerLogic = nativeLinkerLogic
        self.customerCareUseCases = customerCareUseCases
        self.cancellables = cancellables
        observeVehicle()
        if nativeLinkerContainer.chargeManagementUseCases()
            .getStoreChargeSessionId()?.isNotEmpty ?? false {
            Task { [weak self] in
                await self?.fetchChargingSession()
                await self?.electricStatusUseCases.fetchElectricStatus(silentRefresh: false)
            }
        }
    }

    func startCharging(body: ChargeStartRequest) async {
        guard let vehicle = vehicle else { return }
        statePublished.startChargeState = .loading
        let response = await repo.startCharging(
            make: vehicle.make.mappedBrand.rawValue,
            vin: vehicle.vin, body: body)
        if response != nil {
            analyticsUseCases.logEvent(AnalyticsEvents.PublicCharging.startChargingSuccess)
            if response?.startChargeResponse.isSuccess ?? false {
                startTimer()
                statePublished.startChargeState = .success(response)
                nativeLinkerContainer.chargeManagementUseCases()
                    .setStoreChargePartner(value: body.partnerName)
                nativeLinkerContainer.chargeManagementUseCases()
                    .setStoreChargeSessionId(
                        value: response?.startChargeResponse.chargingId
                    )
            } else {
                statePublished.startChargeState = .failure(.startChargeFailure)
            }
            chargingSessionTimeSinceSuccess = Date().timeIntervalSince1970
        } else {
            analyticsUseCases.logEvent(AnalyticsEvents.PublicCharging.startChargingFailure)
            statePublished.startChargeState = .failure(.startChargeFailure)
        }

    }

    func startHomeCharging() async {
        guard let vehicle = vehicle else { return }
        ActivityProgressView.show()
        let response = await repo.startHomeCharging(vin: vehicle.vin,
                                                    generation: vehicle.generation.mappedGeneration.rawValue,
                                                    brand: vehicle.make.mappedBrand.rawValue)
        if let requestNo = response {
            analyticsUseCases.logEvent(AnalyticsEvents.PublicCharging.startHomeChargingSuccess)
            await fetchChargeManagementStatus(startTime: Date().timeIntervalSince1970,
                                              requestNo: requestNo)
        } else {
            analyticsUseCases.logEvent(AnalyticsEvents.PublicCharging.startHomeChargingFailure)
            ActivityProgressView.hide()
        }
    }

    func fetchChargeManagementStatus(startTime: Double,
                                     requestNo: String) async {
        guard let vehicle = vehicle else { return }
        let response = await repo.getRemoteControlStatus(
            vin: vehicle.vin,
            brand: vehicle.make.mappedBrand.rawValue,
            generation: vehicle.generation.mappedGeneration.rawValue,
            requestNo: requestNo)
        if let payload = response?.payload,
           payload.remoteControlResult.result == 0 && payload.remoteControlResult.status == 0 {
                await electricStatusUseCases.fetchElectricStatus(silentRefresh: true)
                ActivityProgressView.hide()
            } else {
                let currentTime = Date().timeIntervalSince1970
                let timeElapsed = currentTime - startTime
                if timeElapsed >= timeOut {
                    ActivityProgressView.hide()
                } else {
                    // Retry after a delay
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        Task {
                            await self.fetchChargeManagementStatus(startTime: startTime,
                                                                   requestNo: requestNo)
                        }
                    }
                }
            }
    }

    func stopCharging(chargingId: String) async {
        guard let vehicle = vehicle else { return }
        statePublished.screenAction = .startCdrTimer

        let response = await repo.stopCharging(
            make: vehicle.make.mappedBrand.rawValue,
            vin: vehicle.vin,
            region: vehicle.region.mappedRegion.rawValue,
            chargeId: chargingId
        )

        statePublished.chargeSessionState = .success(response)

        if response != nil,
           response?.evSession?.evId != nil {
            let status = response?.evSession?.evData?.getChargingStatus()
            if status == .chargingCompleted || status == .chargingStopped {
                analyticsUseCases.logEvent(AnalyticsEvents.PublicCharging.stopChargingSuccess)
                stopTimer()
                nativeLinkerContainer.chargeManagementUseCases().clearChargeSession()
                dashboardContainer.refreshDashboard()
            } else {
                statePublished.screenAction = .stopAtStation
                handleButtonText(response)
                return
            }
        }

        if response == nil {
            statePublished.chargingStatus = .chargingFailed
            statePublished.screenAction = .paymentInfo
            handleButtonText(response)
            analyticsUseCases.logEvent(AnalyticsEvents.PublicCharging.stopChargingFailure)
            return
        }

        if response?.evSession?.evData?.evTotalCost != nil,
           case .success(let success) = statePublished.fuelState,
           case .phev(let pHEVState) = success,
           case .ev(let eVState) = success {
            if pHEVState.plugStatus == .notCharging ||
                eVState.plugStatus == .notCharging {
                statePublished.chargingStatus = .chargingCompleted
                statePublished.screenAction = .stopCdrTimer
            }
        } else {
            statePublished.screenAction = .paymentPending
        }
        handleButtonText(response)
    }

    func whenToShowEnergyAlert(_ response: EvChargeSession) {
        if let kwh = response.evSession?.evData?.evKwh {
            if let temp = statePublished.tempKwhForRefresh,
               kwh == temp {
                statePublished.screenAction = .updatedEnergyMessage
            }
            statePublished.tempKwhForRefresh = kwh
        }
    }

    func fetchChargingSession(showLoading: Bool = true) async {
        guard let vehicle = vehicle else { return }
        if showLoading {
            statePublished.chargeSessionState = .loading
        }
        if chargingSessionTimeSinceSuccess.isAlmostEqual(to: 0.0) {
            chargingSessionTimeSinceSuccess = Date().timeIntervalSince1970
        }

        let result = await repo.chargeSession(
            make: vehicle.make.mappedBrand.rawValue,
            vin: vehicle.vin)
        switch result {
        case let .success(response):
            handleSessionResponse(response: response)
        case let .failure(error):
            handleSessionResponse(response: nil)
            // Timeout
            if case .message = error {
                stopTimer()
                dashboardContainer.refreshDashboard()
            }
        }
    }

    private func handleSessionResponse(response: EvChargeSession?) {
        statePublished.chargeSessionState = .success(response)
        if response == nil {
            let currentTime = Date().timeIntervalSince1970
            if currentTime - chargingSessionTimeSinceSuccess > chargingSessionTimeout {
                nativeLinkerContainer.chargeManagementUseCases().clearChargeSession()
                statePublished.chargingStatus = .chargingFailed
                statePublished.screenAction = .retry
                stopTimer()
            }
        }
        let savedChargingID = nativeLinkerContainer.chargeManagementUseCases()
            .getStoreChargeSessionId()
        if let response = response,
           response.evSession?.evChargingId == savedChargingID {
            chargingSessionTimeSinceSuccess = Date().timeIntervalSince1970
            nativeLinkerContainer.chargeManagementUseCases()
                .setStoreChargePartner(value: response.evSession?.evData?.evLocation?.evOperatorInfo?.evName)
            nativeLinkerContainer.chargeManagementUseCases()
                .setStoreChargeSessionId(value: response.evSession?.evChargingId)
            if let status = response.evSession?.evData?.getChargingStatus() {
                statePublished.chargingStatus = status
                if status.isChargingHalt() {
                    nativeLinkerContainer.chargeManagementUseCases().clearChargeSession()
                    stopTimer()
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                        self?.handleScreenAction()
                    }
                }
                if let analytic = status.analyticsEvent() {
                    analyticsUseCases.logEvent(analytic)
                }
            }
            // Used for refresh button to show energy alert
            whenToShowEnergyAlert(response)
        }
        handleButtonText(response)
    }

    func popView() {
        navigationUseCases.popView()
    }

    func findEvseWithRemoteStartStopCapable(stations: EvStationLocation?) -> Bool {
        guard let station = stations else {
            return false
        }
        let result = station.evEvses?
            .filter { $0.capabilities?.contains(FindStationsConstants.remoteStartStopCapable) == true } ?? []
        return !result.isEmpty
    }

    func handleButtonText(_ session: EvChargeSession?) {
        let evGoPartner = session?.evSession?.isEvgoSession() ?? false
        let sessionStatus = session?.evSession?.evData?.evStatus ?? FindStationsConstants.unknown
        statePublished.enableStopChargingButton = true
        let validSession = session?.evSession?.isValid() ?? false
        // Only disable the stop button for EVGo when active (they need to stop at station)
        let showStopAtStationMessage = evGoPartner && validSession
        let showStopChargingText = !evGoPartner && validSession
        let showErrorText = sessionStatus == FindStationsConstants.error
        let showFailedText = sessionStatus == FindStationsConstants.failed
        let showErrorOrFailed = showErrorText || showFailedText
        statePublished.showErrorOrFailed = showErrorOrFailed
        let isRemoteStartStopCapable = findEvseWithRemoteStartStopCapable(
            stations: session?.evSession?.evData?.evLocation
        )

        switch statePublished.chargingStatus {
        case .chargingInitiated:
            if showErrorOrFailed {
                statePublished.buttonText = Strings.Common.OkBt
                statePublished.errorText = showErrorText ? Strings.Charging.chargeSessionError :
                Strings.Charging.chargeSessionFailure
            } else if showStopChargingText {
                statePublished.enableStopChargingButton = validSession
                statePublished.buttonText = Strings.Charging.stopCharging
                statePublished.errorText = isRemoteStartStopCapable
                ? Strings.Charging.useStationToFinish
                : Strings.Charging.useStationToStop
            } else if showStopAtStationMessage {
                statePublished.buttonText = Strings.Common.continueBt
                statePublished.errorText = Strings.Charging.useStationToStop
            } else {
                statePublished.enableStopChargingButton = validSession
                statePublished.buttonText = Strings.Charging.stopCharging
                statePublished.errorText = nil
            }
        case .chargingFailed:
            statePublished.buttonText = Strings.Common.OkBt
            statePublished.errorText = showErrorText ? Strings.Charging.chargeSessionError :
            Strings.Charging.chargeSessionFailure
            statePublished.startChargeState = .initial
        case .chargingCompleted, .chargingStopped:
            statePublished.buttonText = Strings.Common.OkBt
            statePublished.errorText = nil
            statePublished.startChargeState = .initial
        case .chargingCompletedUnplug:
            statePublished.buttonText = Strings.Fuel.unplug
            statePublished.errorText = nil
            statePublished.startChargeState = .initial
        }
    }

    func stopChargeAction(_ session: EvChargeSession?) {
        Task { [weak self] in
            guard let self = self else { return }
            if let chargingId = session?.evSession?.evChargingId {
                await self.stopCharging(chargingId: chargingId)
            }
        }
    }

    func buttonAction() {
        if case .success(let session) = statePublished.chargeSessionState {
            let isEvGoPartner = session?.evSession?.isEvgoSession() ?? false
            let sessionStatus = session?.evSession?.evData?.evStatus ?? FindStationsConstants.unknown
            let validSession = session?.evSession?.isValid() ?? false
            // Updated to match our new showStopChargingText logic
            let showStopChargingText = !isEvGoPartner && validSession
            let showErrorText = sessionStatus == FindStationsConstants.error
            let showFailedText = sessionStatus == FindStationsConstants.failed
            let showErrorOrFailed = showErrorText || showFailedText

            switch statePublished.chargingStatus {
            case .chargingInitiated:
                if showErrorOrFailed {
                    popView()
                } else if showStopChargingText {
                    stopChargeAction(session)
                } else {
                    // Just pop the view for non-active, non-error states
                    popView()
                }
            case .chargingFailed, .chargingCompleted, .chargingStopped:
                popView()
            case .chargingCompletedUnplug:
                statePublished.showUnplugVehicle = true
            }
        }
    }
}
