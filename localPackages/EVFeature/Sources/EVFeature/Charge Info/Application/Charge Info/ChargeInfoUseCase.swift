// Copyright © 2024 Toyota. All rights reserved.

import Foundation
import SwiftUI

internal protocol ChargeInfoUseCases {
    var state: Published<ChargeInfoState>.Publisher { get }
    func load(polling: Bool) async
    func navigateToChargeHistory()
    func navigateToChargeSchedule()
    func navigateToManualSchedule()
    func fetchChargeHistory(startDate: String?,
                            endDate: String?,
                            chargingType: String) async
    func isEVGoExpired(_ res: EVPartnerStatus) -> Bool?
    func isEVGoExpiringSoon(_ res: EVPartnerStatus) -> Bool?
    func progressColor(_ percent: Int, isPHEV: Bool) -> Color
    func navigateToStatistics()
    func navigateToCleanAssistDetailPage()
    func fetchCleanAssistDetails() async
    func fetchIonnaConsentDetails() async
    func fetchConsents(for provider: CPOProvider) async
    func acceptCleanAssistTerms(_ userAccepted: Bool) async
    func acceptIonnaTerms(_ userAccepted: Bool) async
    func navigateToRegisterPartnerScreen(partner: String)
    func fetchLCFSDashboard() async
    func getBrandName() -> String
    func dismissPopup()
    func navigateToWallet(isSetupWallet: Bool)
    func showEvgoComplimentaryPopup(expiryDays: Int, walletSetup: Bool)
    func getLastUpdatedDate() -> String?
    func navigateToCreateScheduleScreen(isDeparture: Bool)
    func navigateToCreateModifyScheduleScreen()
    func navigateToFindStationsScreen()
    func navigateToIonnaConsentScreen(termsAndConditions: String)
    func fetchStatistics(reportType: String, month: String?) async
    func logStatisticsFilterTap()
    func logStatisticsLearnMore()
    func navigateToChargeAssistScheduleScreen()
    func getWattTimeUrl() -> String
    func getIonnaButtonTitle(acknowledged: Bool?) -> String
}
