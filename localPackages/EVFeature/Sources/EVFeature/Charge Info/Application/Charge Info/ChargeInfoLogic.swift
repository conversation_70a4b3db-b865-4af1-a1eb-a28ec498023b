// Copyright © 2024 Toyota. All rights reserved.
// swiftlint:disable type_body_length
// swiftlint:disable file_length
import Foundation
import Combine
import VehicleFeature
import Analytics
import Navigation
import Components
import Theme
import SwiftUI
import Utility
import NativeLinker
import DashboardFeature

internal class ChargeInfoLogic: ChargeInfoUseCases {

    @Published var statePublished = ChargeInfoState.initial
    var state: Published<ChargeInfoState>.Publisher { $statePublished }
    let electricStatusUseCases: ElectricStatusUseCases
    var electricStatusState: ElectricStatusState?
    let telemetryUseCases: TelemetryUseCases
    var telemetryState: TelemetryState?
    var vehicle: Vehicle?
    let selectedVehicleUseCases: SelectedVehicleUseCases
    let analyticsUseCases: AnalyticsUseCases
    let chargeInfoAPIRepo: EvAPIRepo
    let navigationUseCases: NavigationUseCases
    let userDefaultsUseCases: UserDefaultsUseCases
    var chargingUseCases: ChargingUseCase

    var cancellables = Set<AnyCancellable>()

    init(statePublished: ChargeInfoState = ChargeInfoState.initial,
         electricStatusUseCases: ElectricStatusUseCases,
         electricStatusState: ElectricStatusState? = nil,
         telemetryUseCases: TelemetryUseCases,
         telemetryState: TelemetryState? = nil,
         vehicle: Vehicle? = nil,
         selectedVehicleUseCases: SelectedVehicleUseCases,
         analyticsUseCases: AnalyticsUseCases,
         chargeInfoAPIRepo: EvAPIRepo,
         navigationUseCases: NavigationUseCases,
         userDefaultsUseCases: UserDefaultsUseCases,
         chargingUseCases: ChargingUseCase) {
        self.statePublished = statePublished
        self.electricStatusUseCases = electricStatusUseCases
        self.electricStatusState = electricStatusState
        self.telemetryUseCases = telemetryUseCases
        self.telemetryState = telemetryState
        self.vehicle = vehicle
        self.selectedVehicleUseCases = selectedVehicleUseCases
        self.analyticsUseCases = analyticsUseCases
        self.chargeInfoAPIRepo = chargeInfoAPIRepo
        self.navigationUseCases = navigationUseCases
        self.userDefaultsUseCases = userDefaultsUseCases
        self.chargingUseCases = chargingUseCases
        observeVehicle()
    }

    private func observeVehicle() {
        selectedVehicleUseCases.state.sink { [weak self] vehicle in
            self?.vehicle = vehicle
            if let vehicle = vehicle {
                self?.statePublished.isPublicChargingControlAllowed = vehicle.isFeatureEnabled(.evPublicChargingControl)
                self?.statePublished.isEvIonna = vehicle.isFeatureEnabled(.evIonna) &&
                nativeLinkerContainer.isEVIonnaEnabled()
                self?.statePublished.isEvTesla =
                vehicle.isFeatureEnabled(.teslaSuperChargerNetwork) && nativeLinkerContainer.isEVTeslaEnabled()
                self?.statePublished.isWallet = vehicle.isFeatureEnabled(.wallet)
            }
        }.store(in: &cancellables)
    }

    func load(polling: Bool = false) async {
        statePublished.enrollmentState = .loadinng
        statePublished.showSetTimerOnVehicleToast = false
        DispatchQueue.main.async { [weak self] in
            guard var cancellables = self?.cancellables else {
                return
            }
            self?.telemetryUseCases.state.sink { [weak self] telemetryState in
                self?.telemetryState = telemetryState
                self?.updateState()
            }.store(in: &cancellables)

            self?.electricStatusUseCases.state.sink { [weak self] electricStatusState in
                self?.electricStatusState = electricStatusState
                self?.updateState()
            }.store(in: &cancellables)
        }
        if polling {
            _ = await chargeInfoAPIRepo.postRealTimeStatus(vin: vehicle?.vin ?? "",
                                                           isEV: false,
                                                           brand: vehicle?.make
                .mappedBrand.rawValue ?? "",
                                                           generation: vehicle?
                .generation.mappedGeneration.rawValue ?? "")
            await electricStatusUseCases.fetchElectricStatus(silentRefresh: false)
        }
        let isToyota = vehicle?.make.mappedBrand == .toyota
        statePublished.showEvGo = vehicle?.isFeatureEnabled(.evGo) == true && isToyota
        statePublished.showWallet = vehicle?.isFeatureEnabled(.wallet) == true
        await fetchChargeHistory()
        await fetchStatistics()
        await fetchCleanAssistElegibility()
        await fetchPublicChargingSettingsElegibility()
        await fetchEnrollmentCheck()
    }

    func updateState() {
        /// Update odometer state
        updateOdometerState()
        /// Update Fuel state
        updateFuelState()
        /// Update other states
    }

    func fetchStatistics(reportType: String = "year_week", month: String? = nil) async {
        statePublished.statisticsState = .loadinng
        let stats = await chargeInfoAPIRepo.getChargingStatistics(vin: vehicle?.vin ?? "",
                                                                  brand: vehicle?.make.mappedBrand.rawValue ?? "",
                                                                  generation: vehicle?.generation.rawValue ?? "",
                                                                  reportType: reportType,
                                                                  month: month)
        if let stats = stats {
            statePublished.statisticsState = .success(stats)
        } else {
            statePublished.statisticsState = .notAvailable
        }
    }

    func fetchChargeHistory(startDate: String? = nil,
                            endDate: String? = nil,
                            chargingType: String = "") async {
        statePublished.historyState = .loadinng
        let stats = await chargeInfoAPIRepo.getChargeHistoryList(vin: vehicle?.vin ?? "",
                                                                 brand: vehicle?.make.mappedBrand.rawValue ?? "",
                                                                 generation: vehicle?.generation.rawValue ?? "",
                                                                 chargingType: chargingType,
                                                                 startDate: startDate,
                                                                 endDate: endDate)
        if let stats = stats {
            statePublished.historyState = .success(stats)
        } else {
            statePublished.historyState = .notAvailable
        }
    }

    func fetchEnrollmentCheck() async {
        guard let email = nativeLinkerContainer.accountUserProfileUseCases().getEmail() else { return }
        guard let vin = vehicle?.vin else { return }
        guard let brand = vehicle?.make.mappedBrand.rawValue else { return }
        DispatchQueue.main.async {
            self.statePublished.enrollmentState = .loadinng
        }
        let enrollment = await chargeInfoAPIRepo.getEnrollmentCheck(vin: vin,
                                                               brand: brand,
                                                               email: email)
        if let enrollment = enrollment {
            DispatchQueue.main.async {
                self.statePublished.enrollmentState = .success(enrollment)
            }
            if enrollment.payload?.partnerEnrollment?.wallet == "Found" {
                let lastFour = await chargeInfoAPIRepo.fetchWallet()?.card?.last4
                DispatchQueue.main.async {
                    self.statePublished.walletLast4 = lastFour
                }
            }
        } else {
            DispatchQueue.main.async {
                self.statePublished.enrollmentState = .notAvailable
            }
        }
    }

    private func updateFuelState() {
        guard let vehicle,
              telemetryState != nil,
              electricStatusState != nil else {
            statePublished.fuelState = .notAvailable
            return
        }
        let isEV = vehicle.fuelType == .pureElectric
        let isPHEV = vehicle.fuelType == .pluginHybrid
        if isPHEV {
            if vehicle.features.contains(.telemetry) {
                updatePHEVState()
            } else {
                statePublished.fuelState = .notAvailable
            }
        } else {
            if vehicle.features.contains(.telemetry) {
                if isEV {
                    updateElecticFuleState()
                } else if isPHEV {
                    updatePHEVState()
                } else {
                    statePublished.fuelState = .notAvailable
                }
            } else {
                statePublished.fuelState = .notAvailable
            }
        }
    }

    internal func mapTimerChargeInfo(_ status: SuccessState) -> [EVFeature.EVTimerChargeInfo] {
        status.electricStatus.evtimerChargeInfo.map { EVFeature.EVTimerChargeInfo(
            settingId: $0.settingId,
            enabled: $0.enabled,
            startTime: $0.startTime,
            endTime: $0.endTime,
            daysOfTheWeek: $0.daysOfTheWeek,
            nextSettingID: $0.nextSettingID)
        }
    }

    private func updateElecticFuleState() {
        guard let electricStatusState else {
            return
        }
        switch electricStatusState {
        case .loading:
            statePublished.fuelState = .loading
        case .notAvailable:
            statePublished.fuelState = .notAvailable
        case .success(let status):
            statePublished.fuelState = .success(.ev(mapElectric(status: status)))
        }
    }
    private func updatePHEVState() {
        guard let telemetryState,
              let electricStatusState else {
            return
        }
        switch (telemetryState,
                electricStatusState) {
        case (_,
              .loading),
            (.loading,
             _):
            statePublished.fuelState = .loading
        case (.success(let telemetry),
              .success(let electricStatus)):
            updateFuelState(with: telemetry,
                            eletricStatus: electricStatus.electricStatus)
        default:
            statePublished.fuelState = .notAvailable
        }
    }

    private func updateFuelState(with telemtery: Telemetry,
                                 eletricStatus: ElectricStatus) {
        let distanceToEmptyValue = telemtery.distanceToEmpty.value + eletricStatus.evevDistance
        let unitValue = UnitValue(value: distanceToEmptyValue,
                                  unit: telemtery.distanceToEmpty.unit)
        let timerChargeInfo = eletricStatus.evtimerChargeInfo.map { EVFeature.EVTimerChargeInfo(
            settingId: $0.settingId,
            enabled: $0.enabled,
            startTime: $0.startTime,
            endTime: $0.endTime,
            daysOfTheWeek: $0.daysOfTheWeek,
            nextSettingID: $0.nextSettingID)
        }
        let phevState = PHEVState(distanceToEmpty: unitValue,
                                  fuelLevel: telemtery.fuelLevel,
                                  chargeLevel: eletricStatus.evchargeRemainingAmount,
                                  plugStatus: mapPluginStatus(eletricStatus.evplugStatus),
                                  remainingchargetime: eletricStatus.evremainingchargetime,
                                  brand: vehicle?.make ?? .toyota,
                                  closeToHome: dashboardContainer.getCloseToHome(),
                                  chargeType: eletricStatus.evchargeInfo.chargeType ?? 0,
                                  evRange: eletricStatus.evevDistance,
                                  phRange: telemtery.distanceToEmpty.value,
                                  acquisitionDatetime: eletricStatus.evacquisitionDatetime ?? "",
                                  timerChargeInfo: timerChargeInfo)
        statePublished.fuelState = .success(.phev(phevState))
    }

    internal func mapPluginStatus(_ status: ElectricPlugStatus) -> PlugStatus {
        /// Have to handle this properly
        switch status {
        case .undefined:
            return .unplugged
        case .charging:
            return .charging
        case .notCharging:
            return .notCharging
        }
    }

    private func updateOdometerState() {
        guard let vehicle,
              let telemetryState else {
            statePublished.odometerState = .notAvailable
            return
        }
        var odometrState: OdometerState = .notAvailable
        if vehicle.features.contains(.telemetry) {
            odometrState = .loading
        }
        switch telemetryState {
        case .notAvailable:
            odometrState = .notAvailable
        case .loading:
            odometrState = .loading
        case .success(let telemetry):
            if vehicle.features.contains(.telemetry) {
                odometrState = .success(telemetry.odometer)
            } else {
                odometrState = .notAvailable
            }
        }
        statePublished.odometerState = odometrState
    }
    func navigateToChargeHistory() {
        analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.chargeHistory)
        navigationUseCases.pushToNativeNavigation(.chargeHistory)
    }

    func navigateToChargeSchedule() {
        analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.scheduleTile)
        navigationUseCases.pushToNativeNavigation(.chargeSchedule)
    }

    func navigateToManualSchedule() {
        navigationUseCases.pushToNativeNavigation(.manualSchedule)
    }

    func navigateToChargeAssistScheduleScreen() {
        navigationUseCases.pushToNativeNavigation(.chargeAssistSchedule)
    }

    func navigateToFindStationsScreen() {
        analyticsUseCases.logEvent(AnalyticsEvents.FuelEvents.vehicleEvDashboardFindStationsCta)
        navigationUseCases.pushToNativeNavigation(.findStations)
    }

    func isEVGoExpired(_ res: EVPartnerStatus) -> Bool? {
        if let date = res.expiryDate?.convertStringToDate {
            return Date.now.days(from: date) > 30
        } else {
            return false
        }
    }

    func isEVGoExpiringSoon(_ res: EVPartnerStatus) -> Bool? {
        if let date = res.expiryDate?.convertStringToDate {
            return Date.now.days(from: date) <= 30
        } else {
            return false
        }
    }

    func phevProgressColor(_ percent: Int) -> Color {
        switch percent {
        case ..<0,
            0...30:
            return AppTheme.colors.primary01
        default:
            return AppTheme.colors.secondary01
        }
    }

    func evProgressColor(_ percent: Int) -> Color {
        switch percent {
        case ..<0,
            0...19:
            return AppTheme.colors.error01
        default:
            return AppTheme.colors.button03d
        }
    }
    func progressColor(_ percent: Int,
                       isPHEV: Bool) -> Color {
        if isPHEV {
            return phevProgressColor(percent)
        } else {
            return evProgressColor(percent)
        }
    }
    func navigateToStatistics() {
        navigationUseCases.pushToNativeNavigation(.chargeStatistics)
    }
    func fetchCleanAssistElegibility() async {
        guard let vehicle = vehicle else { return }
        let response = await chargeInfoAPIRepo
            .fetchCleanAssistElegibility(vin: vehicle.vin,
                                         brand: vehicle.make.mappedBrand.rawValue)

        statePublished.cleanAssistEnrollmentState = .init(
            lcfsOptIn: response.lcfsOptIn,
            lcfsEligible: response.lcfsEligible == "true")
    }

    func fetchPublicChargingSettingsElegibility() async {
        guard let vehicle = vehicle else { return }

        statePublished.showChargingSettings = vehicle.isFeatureEnabled(.evPublicChargingControl)
    }
    func navigateToCleanAssistDetailPage() {
        if statePublished.cleanAssistEnrollmentState?.lcfsOptIn?.lowercased() == "in" {
            analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.cleanAssistView)
        } else {
            analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.cleanAssistEnroll)
        }
        navigationUseCases.pushToNativeNavigation(.showLCFSDataConsent(image: nil, isO32DVehicle: true))
    }

    func isChargeAssistAvailable() -> Bool {
        return vehicle?.isFeatureEnabled(.chargeAssist) ?? false
    }

    func getWattTimeUrl() -> String {
        return nativeLinkerContainer.chargeManagementUseCases().getWattTimeURL()
    }
}
