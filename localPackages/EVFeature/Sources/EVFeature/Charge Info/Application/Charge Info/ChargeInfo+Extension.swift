// Copyright © 2024 Toyota. All rights reserved.

import Foundation
import Combine
import VehicleFeature
import Analytics
import Navigation
import Components
import Theme
import SwiftUI
import Utility
import Native<PERSON>inker
import LocalizedStrings
import DashboardFeature

extension ChargeInfoLogic {
    func acceptCleanAssistTerms(_ userAccepted: Bool) async {
        guard let vehicle = vehicle else { return }
        if userAccepted {
            analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.cleanAssistEnroll)
            ActivityProgressView.show()
            let generation = vehicle.generation.mappedGeneration.rawValue
            let brand = vehicle.make.mappedBrand.rawValue
            let result = await chargeInfoAPIRepo.acceptCleanAssistConsent(generation: generation,
                                                                          appBrand: brand,
                                                                          vin: vehicle.vin)
            ActivityProgressView.hide()
            if result {
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                    self.navigationUseCases.popView()
                }
            }
        } else {
            analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.cleanAssistDecline)
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                self.navigationUseCases.popView()
            }
        }
    }

    func navigateToRegisterPartnerScreen(partner: String) {
        if partner.lowercased() == "evgo" {
            analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.evGoRegister)
        } else {
            analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.vehicleEvChargePointRegister)
        }
        navigationUseCases.pushToNativeNavigation(.evPartnerEnrollmentScreen(partner: partner))
    }

    func fetchLCFSDashboard() async {
        guard let vehicle = vehicle else { return }
        guard let locale = nativeLinkerContainer.accountUserProfileUseCases().getLocale() else { return }
        statePublished.cleanAssistLCFS = .loading
        let lcfsResponse = await chargeInfoAPIRepo.fetchLCFSDashboard(vin: vehicle.vin,
                                                                      brand: vehicle.make.mappedBrand.rawValue,
                                                                      locale: locale)

        if let lcfsResponse = lcfsResponse {
            statePublished.cleanAssistLCFS = .success(lcfsResponse)
        } else {
            statePublished.cleanAssistLCFS = .notAvailable
        }
    }

    func getBrandName() -> String {
        vehicle?.make.getBrandName ?? "Toyota"
    }

    func dismissPopup() {
        navigationUseCases.popView(.evgoComplimentaryPopup)
    }

    func navigateToWallet(isSetupWallet: Bool) {
        navigationUseCases.pushToNativeNavigation(isSetupWallet ? .evWalletAddCardPage
         : .evWalletPage)
    }

    func showEvgoComplimentaryPopup(expiryDays: Int, walletSetup: Bool) {
        if expiryDays <= 30 && expiryDays >= 0 {
            if userDefaultsUseCases.getEvgoExpiringSoonPopupCount() <= 3 {
                userDefaultsUseCases.incrementEvgoExpiringSoonPopupCount()
                navigationUseCases.pushToNativeNavigation(
                    .evgoComplimentaryPopup(expiryDays: expiryDays,
                                            walletSetup: walletSetup))
            }
        } else {
            if expiryDays <= 0 && userDefaultsUseCases.getEvgoExpiredPopupCount() <= 3 {
                    userDefaultsUseCases.incrementEvgoExpiredPopupCount()
                    navigationUseCases.pushToNativeNavigation(
                        .evgoComplimentaryPopup(expiryDays: expiryDays,
                                                walletSetup: walletSetup))
            }
        }
    }

    func getLastUpdatedDate() -> String? {
        guard let vehicle = vehicle else { return nil }
        var result: String?

        if case .success(let fuelType) = statePublished.fuelState {
            if case .phev(let pHEVState) = fuelType {
                result = pHEVState.acquisitionDatetime.convertStringToDate?
                    .lastUpdatedTime(region: vehicle.region.mappedRegion.rawValue)
            } else if case .ev(let eVState) = fuelType {
                result = eVState.acquisitionDatetime.convertStringToDate?
                    .lastUpdatedTime(region: vehicle.region.mappedRegion.rawValue)
            } else {
                result = nil
            }
        }

        return result
    }

    private func getChargeType() -> Int? {
        switch statePublished.fuelState {
        case .notAvailable:
            return nil
        case .loading:
            return nil
        case .success(let vehicleFuel):
            return mapVehicleFuel(vehicleFuel)
        }
    }

    func mapVehicleFuel(_ vehicleFuel: VehicleFuel) -> Int? {
        switch vehicleFuel {
        case .gas:
            return nil
        case .phev(let pHEVState):
            return pHEVState.chargeType
        case .ev(let eVState):
            return eVState.chargeType
        }
    }

    func navigateToCreateScheduleScreen(isDeparture: Bool) {
        if let chargeType = getChargeType(), chargeType != 0,
           chargeType != 15 {
            if isDeparture {
                analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.scheduleDepTime)
            } else {
                analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.scheduleStartTime)
            }
            navigationUseCases.pushToNativeNavigation(.createPHEVSchedule(isDeparture: isDeparture,
                                                                          isClimate: chargeType == 3))
        } else {
            statePublished.showSetTimerOnVehicleToast = true
        }
    }

    func fetchCleanAssistDetails() async {
        guard let vehicle = vehicle else { return }
        statePublished.cleanAssistDetails = .loading
        let response = await chargeInfoAPIRepo.fetchMarketingConsent(vehicle: vehicle,
                                                                     productCodes: [],
                                                                     eligibleConsent: "lcfs",
                                                                     flowType: "Banner")
        switch response {
        case .success(let consents):
            if let cleanAssistDetails = consents.eligibleConsents.first(where: { $0.name == "Clean Assist" }) {
                statePublished.cleanAssistDetails = .success(cleanAssistDetails)
            } else {
                statePublished.cleanAssistDetails = .notAvailable
            }
        case .failure:
            statePublished.cleanAssistDetails = .notAvailable
        }
    }

    func fetchIonnaConsentDetails() async {
        guard let vehicle = vehicle else { return }
        statePublished.ionnaConsentDetails = .loading
        let response = await chargeInfoAPIRepo.fetchMarketingConsent(vehicle: vehicle,
                                                                     productCodes: [],
                                                                     eligibleConsent: "ionna",
                                                                     flowType: "Banner")
        switch response {
        case .success(let consents):
            guard let ionnaDetails = consents.eligibleConsents.first(where: { $0.name?.lowercased() == "ionna" }) else {
                statePublished.ionnaConsentDetails = .notAvailable
                return
            }
            var isAccepted: Bool?
            if let alreadyAcknowledged = ionnaDetails.alreadyAcknowledged, alreadyAcknowledged,
               let acknowledgedConsents = consents.acknowledgedConsents.first(where: { $0.consentId == "A20" }) {
                if acknowledgedConsents.consentStatus?.lowercased() == "accepted" {
                    isAccepted = true
                } else {
                    isAccepted = false
                }
            }
            statePublished.ionnaConsentDetails = .success(isAccepted, ionnaDetails.description?.terms)
        case .failure:
            statePublished.ionnaConsentDetails = .notAvailable
        }
    }

    func fetchConsents(for provider: CPOProvider) async {
        guard let vehicle = vehicle else { return }
        statePublished.teslaConsentDetails = .loading
        let response = await chargeInfoAPIRepo.fetchMarketingConsent(vehicle: vehicle,
                                                                     productCodes: [],
                                                                     eligibleConsent: provider.rawValue,
                                                                     flowType: "Banner")
        switch response {
        case .success(let consents):
            guard let teslaDetails =
                consents.eligibleConsents.first(where: { $0.name?.lowercased() == provider.rawValue }) else {
                statePublished.teslaConsentDetails = .notAvailable
                return
            }
            var isAccepted: Bool?
            if let alreadyAcknowledged = teslaDetails.alreadyAcknowledged, alreadyAcknowledged,
               let acknowledgedConsents = consents.acknowledgedConsents.first(
                where: { $0.consentId == provider.consentCode
                }) {
                if acknowledgedConsents.consentStatus?.lowercased() == "accepted" {
                    isAccepted = true
                } else {
                    isAccepted = false
                }
            }
            statePublished.teslaConsentDetails = .success(isAccepted, teslaDetails.description?.terms)
        case .failure:
            statePublished.teslaConsentDetails = .notAvailable
        }
    }

    func acceptIonnaTerms(_ userAccepted: Bool) async {
        guard let vehicle = vehicle else { return }
        analyticsUseCases.logEvent(
            userAccepted ? AnalyticsEvents.ChargeInfo.ionnaEnroll
            : AnalyticsEvents.ChargeInfo.ionnaDecline
        )
        ActivityProgressView.show()
        let generation = vehicle.generation.mappedGeneration.rawValue
        let brand = vehicle.make.mappedBrand.rawValue
        _ = await chargeInfoAPIRepo.acceptIonnaConsent(userAccepted: userAccepted,
                                                                generation: generation,
                                                                appBrand: brand,
                                                                vin: vehicle.vin)
        ActivityProgressView.hide()
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.navigationUseCases.popView()
        }
    }

    func getIonnaButtonTitle(acknowledged: Bool?) -> String {
        return acknowledged.map { $0 ? Strings.Common.acceptedBt : Strings.Common.declinedBt }
        ?? Strings.EvPartnerRegistration.register
    }

    func navigateToIonnaConsentScreen(termsAndConditions: String) {
        navigationUseCases.pushToNativeNavigation(.evIonnaConsentScreen(termsAndConditions: termsAndConditions))
    }

    func mapElectric(status: SuccessState) -> EVState {
        return EVState(estimateDistance: UnitValue(value: status.electricStatus.evevDistanceAC,
                                                          unit: status.electricStatus.evevDistanceUnit),
                              estimateDistanceClimateOff: UnitValue(value: status.electricStatus.evevDistance,
                                                                    unit: status.electricStatus.evevDistanceUnit),
                              chargeLevel: status.electricStatus.evchargeRemainingAmount,
                              plugStatus: mapPluginStatus(status.electricStatus.evplugStatus),
                              remainingchargetime: status.electricStatus.evremainingchargetime,
                              closeToHome: dashboardContainer.getCloseToHome(),
                              chargingDetailsAvailable: status.chargeStatus?.id.isEmpty == false,
                              acquisitionDatetime: status.electricStatus.evacquisitionDatetime ?? "",
                              chargeType: status.electricStatus.evchargeInfo.chargeType ?? 0,
                              timerChargeInfo: mapTimerChargeInfo(status))
    }

    func logStatisticsFilterTap() {
        analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.chargeStatsDateFilter)
    }

    func logStatisticsLearnMore() {
        analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.learnMore)
    }

    func navigateToCreateModifyScheduleScreen() {
        navigationUseCases.pushToNativeNavigation(.createModifyChargeSchedule(isChargeManagement: true))
    }
}
