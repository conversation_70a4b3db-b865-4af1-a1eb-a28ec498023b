// Copyright © 2024 Toyota. All rights reserved.
import Foundation
import VehicleFeature

internal struct ChargeInfoState {
    var odometerState: OdometerState
    var fuelState: FuelState
    var statisticsState: StatisticsState
    var historyState: HistoryState
    var enrollmentState: EnrollmentCheckState
    var showChargingSettings: Bool?
    var cleanAssistEnrollmentState: CleanAssistEnrollmentState?
    var cleanAssistDetails: CleanAssistDetails
    var ionnaConsentDetails: IonnaConsentDetails
    var teslaConsentDetails: TeslaConsentDetails
    var cleanAssistLCFS: CleanAssistLCFS
    var showSetTimerOnVehicleToast: Bool
    var walletLast4: String?
    var isPHEV: Bool
    var showEvGo: Bool
    var showWallet: Bool
    var isPublicChargingControlAllowed: Bool
    var isEvIonna: Bool
    var isEvTesla: Bool
    var isWallet: Bool
    static var initial = ChargeInfoState(odometerState: .notAvailable,
                                         fuelState: .notAvailable,
                                         statisticsState: .notAvailable,
                                         historyState: .notAvailable,
                                         enrollmentState: .notAvailable,
                                         showChargingSettings: false,
                                         cleanAssistEnrollmentState: .init(lcfsOptIn: "", lcfsEligible: false),
                                         cleanAssistDetails: .notAvailable,
                                         ionnaConsentDetails: .notAvailable,
                                         teslaConsentDetails: .notAvailable,
                                         cleanAssistLCFS: .notAvailable, showSetTimerOnVehicleToast: false,
                                         walletLast4: nil,
                                         isPHEV: false,
                                         showEvGo: false,
                                         showWallet: false,
                                         isPublicChargingControlAllowed: false,
                                         isEvIonna: false,
                                         isEvTesla: false,
                                         isWallet: false)

    var isEVChargeNow: Bool {
        if case .success(let fuel) = fuelState, case .ev(let eVState) = fuel {
            let chargeNow = eVState.closeToHome && eVState.plugStatus == .notCharging
            if chargeNow {
                return true
            }
        }
        return false
    }
}

internal enum OdometerState {
    case notAvailable
    case loading
    case success(UnitValue)
}

internal enum StatisticsState {
    case notAvailable
    case loadinng
    case success(ChargeStatistics)
}

internal enum HistoryState {
    case notAvailable
    case loadinng
    case success(ChargeHistory)
}

internal enum FuelState: Equatable {
    case notAvailable
    case loading
    case success(VehicleFuel)
}

internal enum VehicleFuel: Equatable {
    case gas(GasVehicleState)
    case phev(PHEVState)
    case ev(EVState)
}

internal struct GasVehicleState: Equatable {
    let distanceToEmpty: UnitValue
    let fuelLevel: Int
    let brand: VehicleMake
}

internal struct PHEVState: Equatable {
    let distanceToEmpty: UnitValue
    let fuelLevel: Int
    let chargeLevel: Int
    let plugStatus: PlugStatus
    let remainingchargetime: Int
    let brand: VehicleMake
    let closeToHome: Bool
    let chargeType: Int
    let evRange: Int
    let phRange: Int
    let acquisitionDatetime: String
    let timerChargeInfo: [EVTimerChargeInfo]
}

internal struct EVState: Equatable {
    let estimateDistance: UnitValue
    let estimateDistanceClimateOff: UnitValue
    let chargeLevel: Int
    let plugStatus: PlugStatus
    let remainingchargetime: Int
    let closeToHome: Bool
    let chargingDetailsAvailable: Bool
    let acquisitionDatetime: String
    let chargeType: Int
    let timerChargeInfo: [EVTimerChargeInfo ]
}

internal enum PlugStatus: Equatable {
    // Plugged, Charging
    case charging
    // Plugged, Not charging
    case notCharging
    // Plugged, Full charged
    case fullCharge
    // Will display Find station if battery is low
    case unplugged
}

internal enum EnrollmentCheckState {
    case notAvailable
    case loadinng
    case success(EVEnrollmentCheck)
}

internal struct CleanAssistEnrollmentState {
    let lcfsOptIn: String?
    let lcfsEligible: Bool?

}

internal enum CleanAssistDetails {
    case notAvailable
    case loading
    case success(EligibleConsents)
}

internal enum IonnaConsentDetails {
    case notAvailable
    case loading
    case success(Bool?, String?)
}

internal enum TeslaConsentDetails {
    case notAvailable
    case loading
    case success(Bool?, String?)
}

internal enum CleanAssistLCFS {
    case notAvailable
    case loading
    case success(LcfsDashboardMapped)
}
