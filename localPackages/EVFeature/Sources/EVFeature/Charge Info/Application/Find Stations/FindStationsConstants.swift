// Copyright © 2024 Toyota. All rights reserved.

import Foundation

internal enum FindStationsConstants {
    static let weeklyOpen24 = "MON: 24 hours | TUE: 24 hours | WED: 24 hours | THU: 24 hours | " +
                              "FRI: 24 hours | SAT: 24 hours | SUN: 24 hours"
    static let open24Hours = "24 hours daily"
    static let twentyFourHoursAday = "24 hours a day"
    static let found = "Found"
    static let notFound = "Not Found"
    static let chargePoint = "chargepoint"
    static let ionna = "ionna"
    static let evGo = "evgo"
    static let nrel = "NREL"
    static let partnerFilterEvConnect = "EV Connect"
    static let partnerFilterEvgo = "EVgo"
    static let partnerFilterFloNetwork = "FLO Network"
    static let partnerFilterFloUSNetwork = "FLO US Network"
    static let partnerFilterGreenLots = "Greenlots"
    static let partnerFilterShellRecharge = "Shell Recharge"
    static let partnerFilterChargePoint = "ChargePoint"
    static let partnerFilterIonna = "IONNA"
    static let partnerFilterTesla = "tesla"
    static let partnerNameChargePointNetwork = "ChargePoint Network"
    static let chademo = "chademo"
    static let chademoUppercase = "CHADEMO"
    static let error = "ERROR"
    static let failed = "FAILED"
    static let stopped = "STOPPED"
    static let stopping = "STOPPING"
    static let completed = "COMPLETED"
    static let active = "ACTIVE"
    static let started = "STARTED"
    static let unknown = "UNKNOWN"
    static let rejected = "REJECTED"
    static let remoteStartStopCapable = "REMOTE_START_STOP_CAPABLE"
    static let available = "AVAILABLE"
}
