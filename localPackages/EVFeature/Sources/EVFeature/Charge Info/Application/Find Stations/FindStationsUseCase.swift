// Copyright © 2024 Toyota. All rights reserved.

import Foundation
import SwiftUI

internal protocol FindStationsUseCase {
    var state: Published<FindStationsState>.Publisher { get }

    func load() async
    func isEVGoExpired(_ res: EVPartnerStatus) -> Bool?
    func isEVGoExpiringSoon(_ res: EVPartnerStatus) -> Bool?
    func navigateToRegisterPartnerScreen(partner: String)
    func getBrandName() -> String
    func dismissPopup()
    func navigateToWallet(isSetupWallet: Bool)
    func showEvgoComplimentaryPopup(expiryDays: Int, walletSetup: Bool)
    func search(lat: String?, lon: String?)
    func getStationsCount(level1: String?, level2: String?, dcFast: String?) -> String
    func distanceBetweenLocations(lat: Double, lon: Double) -> String?
    func fetchMapPinAnnotations()
    func callEVStationButton(station: EvStation, screen: EVScreen)
    func getEVStationStatusTitle(station: EvStation) async
    func getPartnersList() -> [String]
    func handleFilters(selectedPartners: [String],
                       selectedPlugTypes: [String]) async
    func toggleFavorites(placeId: String, isFavorite: Bool, isShowFavorites: Bool)
    func getDirections(latitude: Double, longitude: Double, destinationAddress: String?)
    func sendToCar(address: String) async -> Bool
    func oopenWebSite(url: String)
    func callStation(number: String)
    func focusMap(to location: LocationToCenter)
    func nextPage()
    func resetPageCount()
    func startChargingWithConnector(station: EvStation, evseUid: String, connectorId: String)
    func removeAllPartnerFilter(partner: String?)
    func removeAllPlugtypeFilter(plugType: String?)
    func appendPartnerFilter(partner: String)
    func appendPlugtypeFilter(plugType: String)
    func updateConnectorSelectionVisibility(show: Bool)
    func isMyDestinationEnabled() -> Bool
    func fetchIonnaConsentDetails() async
    func navigateToIonnaConsentScreen(termsAndConditions: String)
    func shouldDisplayRegisterButton(for station: EvStation) -> Bool
    func shouldDisplayAdaptorDisclaimer(for evSource: String) -> Bool
}
