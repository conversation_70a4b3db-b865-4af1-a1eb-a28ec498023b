// Copyright © 2024 Toyota. All rights reserved.
// swiftlint:disable file_length
import Foundation
import Combine
import VehicleFeature
import Analytics
import Navigation
import Components
import Theme
import SwiftUI
import Utility
import Native<PERSON>inker
import CoreLocation
import LocalizedStrings

internal class FindStationsLogic: FindStationsUseCase {
    @Published var statePublished = FindStationsState.initial
    var state: Published<FindStationsState>.Publisher { $statePublished }
    let electricStatusUseCases: ElectricStatusUseCases
    var electricStatusState: ElectricStatusState?
    let telemetryUseCases: TelemetryUseCases
    var telemetryState: TelemetryState?
    var vehicle: Vehicle?
    let selectedVehicleUseCases: SelectedVehicleUseCases
    let analyticsUseCases: AnalyticsUseCases
    let chargeInfoAPIRepo: EvAPIRepo
    let navigationUseCases: NavigationUseCases
    let userDefaultsUseCases: UserDefaultsUseCases
    var nativeLinkerLogic: AccountUserProfileUseCases
    var chargingUseCases: ChargingUseCase
    let chargeInfoUseCases: ChargeInfoUseCases
    var chargingState: ChargingState?
    var chargeInfoState: ChargeInfoState?
    var teslaFlagMapper: TeslaFlagMapping

    var cancellables = Set<AnyCancellable>()

    private func observeVehicle() {
        selectedVehicleUseCases.state.sink { [weak self] vehicle in
            self?.vehicle = vehicle
            self?.setupIsFindStationsPlus()
        }.store(in: &cancellables)
        chargingUseCases.state.sink { [weak self] chargingState in
            self?.chargingState = chargingState
        }.store(in: &cancellables)
    }

    private func setupFlagMapper() {
        chargeInfoUseCases.state.sink { [weak self] chargeInfoState in
            self?.chargeInfoState = chargeInfoState

            guard let teslaFlags = self?.teslaFlagMapper.map(self?.chargeInfoState) else { return }
            self?.statePublished.teslaState.isVinEligibleForTesla = teslaFlags.0
            self?.statePublished.teslaState.hasRegisteredTesla = teslaFlags.1
        }.store(in: &cancellables)
    }

    init(statePublished: FindStationsState = FindStationsState.initial,
         electricStatusUseCases: ElectricStatusUseCases,
         electricStatusState: ElectricStatusState? = nil,
         telemetryUseCases: TelemetryUseCases,
         telemetryState: TelemetryState? = nil,
         vehicle: Vehicle? = nil, selectedVehicleUseCases: SelectedVehicleUseCases,
         analyticsUseCases: AnalyticsUseCases, chargeInfoAPIRepo: EvAPIRepo,
         navigationUseCases: NavigationUseCases, userDefaultsUseCases: UserDefaultsUseCases,
         nativeLinkerLogic: AccountUserProfileUseCases,
         chargingState: ChargingState? = nil,
         cancellables: Set<AnyCancellable> = Set<AnyCancellable>(),
         chargingUseCases: ChargingUseCase,
         chargeInfoUseCases: ChargeInfoUseCases,
         teslaFlagMapper: TeslaFlagMapping) {
        self.statePublished = statePublished
        self.electricStatusUseCases = electricStatusUseCases
        self.electricStatusState = electricStatusState
        self.telemetryUseCases = telemetryUseCases
        self.telemetryState = telemetryState
        self.vehicle = vehicle
        self.selectedVehicleUseCases = selectedVehicleUseCases
        self.analyticsUseCases = analyticsUseCases
        self.chargeInfoAPIRepo = chargeInfoAPIRepo
        self.navigationUseCases = navigationUseCases
        self.userDefaultsUseCases = userDefaultsUseCases
        self.nativeLinkerLogic = nativeLinkerLogic
        self.cancellables = cancellables
        self.chargingUseCases = chargingUseCases
        self.chargingState = chargingState
        self.chargeInfoUseCases = chargeInfoUseCases
        self.teslaFlagMapper = teslaFlagMapper
        observeVehicle()
        setupFlagMapper()
    }

    func load() async {
        guard let vehicle = vehicle else { return }
        analyticsUseCases.logEvent(AnalyticsEvents.FindStationsEvents.vehicleChargeStationPage)
        telemetryUseCases.state.sink { [weak self] telemetryState in
            self?.telemetryState = telemetryState
            self?.updateState()
        }.store(in: &cancellables)

        electricStatusUseCases.state.sink { [weak self] electricStatusState in
            self?.electricStatusState = electricStatusState
            self?.updateState()
        }.store(in: &cancellables)
        switch statePublished.locationToCenter {
        case .curentLocation:
            await fetchUserLocation()
        case .vehicleLocation:
            fetchVehicleLocation()
        }
        await getEnrollment()

        if statePublished.isHydrogenFUelCell || statePublished.isPHEV || vehicle.region == .canada {
            await getHydrogenStations()
        } else {
            if loadFavorites() {
                statePublished.isFavoritesLoaded = true
            }
            await getStations()
        }
        fetchMapPinAnnotations()
    }

    func getEnrollment() async {
        guard let email = nativeLinkerContainer.accountUserProfileUseCases().getEmail() else { return }
        guard let vin = vehicle?.vin else { return }
        guard let brand = vehicle?.make.mappedBrand.rawValue else { return }
        statePublished.enrollmentState = .loadinng
        let response = await chargeInfoAPIRepo.getEnrollmentCheck(vin: vin,
                                                                  brand: brand,
                                                                  email: email)
        if let response = response {
            statePublished.enrollmentState = .success(response)
        }
    }

    func getHydrogenStations(lat: String? = nil, lon: String? = nil) async {
        guard let vehicle = vehicle else { return }
        statePublished.hydrogenStations = .loading
        let response = await chargeInfoAPIRepo.getHydrogenStations(
            brand: vehicle.make.mappedBrand.rawValue,
            generation: vehicle.generation.mappedGeneration.rawValue,
            fuelType: vehicle.fuelType.rawValue,
            radius: vehicle.fuelType == .hydrogenFuelCell ? "60" : "10",
            zip: "",
            region: vehicle.region.mappedRegion.rawValue,
            latitude: lat ?? String(statePublished.locationCoordinates.latitude),
            longitude: lon ?? String(statePublished.locationCoordinates.longitude))
        statePublished.hydrogenStations = .success(response)
        if response != nil {
            analyticsUseCases.logEvent(AnalyticsEvents.FindStationsEvents.vehicleFetchChargeStationListSuccess)
        } else {
            analyticsUseCases.logEvent(AnalyticsEvents.FindStationsEvents.vehicleFetchChargeStationListFailure)
        }
    }

    func updateState() {
        /// Update Fuel state
        updateFuelState()
        /// Update other states
    }

    func fetchIonnaConsentDetails() async {
        await chargeInfoUseCases.fetchIonnaConsentDetails()
    }

    func navigateToIonnaConsentScreen(termsAndConditions: String) {
        chargeInfoUseCases.navigateToIonnaConsentScreen(termsAndConditions: termsAndConditions)
    }

    private func updateFuelState() {
        guard let vehicle,
              telemetryState != nil,
              electricStatusState != nil else {
            statePublished.fuelState = .notAvailable
            return
        }
        let isEV = vehicle.fuelType == .pureElectric
        let isPHEV = vehicle.fuelType == .pluginHybrid
        let isGas = vehicle.fuelType == .gas
        if isGas {
            if vehicle.features.contains(.telemetry) {
                updateGasFuelState()
            } else {
                statePublished.fuelState = .notAvailable
            }
        } else if isPHEV {
            if vehicle.features.contains(.telemetry) {
                updatePHEVState()
            } else {
                statePublished.fuelState = .notAvailable
            }
        } else {
            if vehicle.features.contains(.telemetry) {
                if isEV {
                    updateElecticFuleState()
                } else if isPHEV {
                    updatePHEVState()
                } else {
                    updateGasFuelState()
                }
            } else {
                statePublished.fuelState = .notAvailable
            }
        }
    }

    private func updateElecticFuleState() {
        guard let electricStatusState else {
            return
        }
        switch electricStatusState {
        case .loading:
            statePublished.fuelState = .loading
        case .notAvailable:
            statePublished.fuelState = .notAvailable
        case .success(let status):
            handleSuccessEvState(status)
        }
    }

    private func updatePHEVState() {
        guard let telemetryState,
              let electricStatusState else {
            return
        }
        switch (telemetryState,
                electricStatusState) {
        case (_,
              .loading),
            (.loading,
             _):
            statePublished.fuelState = .loading
        case (.success(let telemetry),
              .success(let electricStatus)):
            updateFuelState(with: telemetry,
                            eletricStatus: electricStatus.electricStatus)
        default:
            statePublished.fuelState = .notAvailable
        }
    }

    private func updateGasFuelState() {
        guard let telemetryState else { return }
        switch telemetryState {
        case .loading:
            statePublished.fuelState = .loading
        case .notAvailable:
            statePublished.fuelState = .notAvailable
        case .success(let telemetry):
            let gasState = GasVehicleState(distanceToEmpty: telemetry.distanceToEmpty,
                                           fuelLevel: telemetry.fuelLevel,
                                           brand: vehicle?.make ?? .toyota)
            statePublished.fuelState = .success(.gas(gasState))
        }
    }

    private func updateFuelState(with telemtery: Telemetry,
                                 eletricStatus: ElectricStatus) {
        let distanceToEmptyValue = telemtery.distanceToEmpty.value + eletricStatus.evevDistance
        let unitValue = UnitValue(value: distanceToEmptyValue,
                                  unit: telemtery.distanceToEmpty.unit)
        let timerChargeInfo = eletricStatus.evtimerChargeInfo
            .map({EVFeature.EVTimerChargeInfo(settingId: $0.settingId,
                                              enabled: $0.enabled,
                                              startTime: $0.startTime,
                                              endTime: $0.endTime,
                                              daysOfTheWeek: $0.daysOfTheWeek,
                                              nextSettingID: $0.nextSettingID)
            })
        let phevState = PHEVState(distanceToEmpty: unitValue,
                                  fuelLevel: telemtery.fuelLevel,
                                  chargeLevel: eletricStatus.evchargeRemainingAmount,
                                  plugStatus: mapPluginStatus(eletricStatus.evplugStatus),
                                  remainingchargetime: eletricStatus.evremainingchargetime,
                                  brand: vehicle?.make ?? .toyota, closeToHome: false,
                                  chargeType: eletricStatus.evchargeInfo.chargeType ?? 0,
                                  evRange: eletricStatus.evevDistance,
                                  phRange: telemtery.distanceToEmpty.value,
                                  acquisitionDatetime: eletricStatus.evacquisitionDatetime ?? "",
                                  timerChargeInfo: timerChargeInfo)
        statePublished.fuelState = .success(.phev(phevState))
    }
}

internal extension FindStationsLogic {
    func fetchUserLocation() async {
        if let location = await nativeLinkerLogic.fetchLocationWithLatLon(),
           statePublished.locationToCenter == .curentLocation {
            statePublished.locationCoordinates = LocationCoordinate(
                latitude: location.latitude, longitude: location.longitude)
        }
    }

    func mapPluginStatus(_ status: ElectricPlugStatus) -> PlugStatus {
        /// Have to handle this properly
        switch status {
        case .undefined:
            return .unplugged
        case .charging:
            return .charging
        case .notCharging:
            return .notCharging
        }
    }

    func isEVGoExpired(_ res: EVPartnerStatus) -> Bool? {
        if let date = res.expiryDate?.convertStringToDate {
            return Date.now.days(from: date) > 30
        } else {
            return false
        }
    }

    func isEVGoExpiringSoon(_ res: EVPartnerStatus) -> Bool? {
        if let date = res.expiryDate?.convertStringToDate {
            return Date.now.days(from: date) <= 30
        } else {
            return false
        }
    }

    func navigateToRegisterPartnerScreen(partner: String) {
        if partner.lowercased() == "evgo" {
            analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.evGoRegister)
        } else {
            analyticsUseCases.logEvent(AnalyticsEvents.ChargeInfo.vehicleEvChargePointRegister)
        }
        navigationUseCases.pushToNativeNavigation(.evPartnerEnrollmentScreen(partner: partner))
    }

    func getBrandName() -> String {
        vehicle?.make.getBrandName ?? "Toyota"
    }

    func dismissPopup() {
        navigationUseCases.popView(.evgoComplimentaryPopup)
    }

    func setupIsFindStationsPlus() {
        guard let vehicle = vehicle else { return }
        statePublished.isFindStationsPlus = vehicle.isFeatureEnabled(.evPublicChargingControl) &&
        vehicle.fuelType != .pluginHybrid
        statePublished.isHydrogenFUelCell = vehicle.fuelType == .hydrogenFuelCell
        statePublished.isPHEV = vehicle.fuelType == .pluginHybrid
        statePublished.isWallet = vehicle.isFeatureEnabled(.wallet)
        statePublished.isSubaru = vehicle.make == .subaru
    }

    func handleSuccessEvState(_ status: SuccessState) {
        let electricStatus = status.electricStatus
        let chargeStatus = status.chargeStatus
        let timerChargeInfo = electricStatus.evtimerChargeInfo
            .map({EVFeature.EVTimerChargeInfo(settingId: $0.settingId,
                                              enabled: $0.enabled,
                                              startTime: $0.startTime,
                                              endTime: $0.endTime,
                                              daysOfTheWeek: $0.daysOfTheWeek,
                                              nextSettingID: $0.nextSettingID)
            })
        let evState = EVState(estimateDistance: UnitValue(value: electricStatus.evevDistanceAC,
                                                          unit: electricStatus.evevDistanceUnit),
                              estimateDistanceClimateOff: UnitValue(value: electricStatus.evevDistance,
                                                                    unit: electricStatus.evevDistanceUnit),
                              chargeLevel: electricStatus.evchargeRemainingAmount,
                              plugStatus: mapPluginStatus(electricStatus.evplugStatus),
                              remainingchargetime: electricStatus.evremainingchargetime,
                              closeToHome: false,
                              chargingDetailsAvailable: chargeStatus?.id.isEmpty == false,
                              acquisitionDatetime: electricStatus.evacquisitionDatetime ?? "",
                              chargeType: electricStatus.evchargeInfo.chargeType ?? 0,
                              timerChargeInfo: timerChargeInfo)
        statePublished.fuelState = .success(.ev(evState))

    }

    func removeAllFilters(from array: inout [String], value: String?) {
        if let value = value {
            array.removeAll { $0 == value }
        } else {
            array.removeAll()
        }
    }

    func appendFilter(to array: inout [String], value: String) {
        array.append(value)
    }

    func removeAllPartnerFilter(partner: String?) {
        removeAllFilters(from: &statePublished.selectedPartners, value: partner)
    }

    func removeAllPlugtypeFilter(plugType: String?) {
        removeAllFilters(from: &statePublished.selectedPlugTypes, value: plugType)
    }

    func appendPartnerFilter(partner: String) {
        appendFilter(to: &statePublished.selectedPartners, value: partner)
    }

    func appendPlugtypeFilter(plugType: String) {
        appendFilter(to: &statePublished.selectedPlugTypes, value: plugType)
    }
}

internal extension FindStationsLogic {
    func shouldDisplayAdaptorDisclaimer(for evSource: String) -> Bool {
        evSource.lowercased().contains("tesla")
    }

    func shouldDisplayRegisterButton(for station: EvStation) -> Bool {
        station.evEvSource.lowercased().contains("tesla")
        && statePublished.teslaState.isVinEligibleForTesla
        && !statePublished.teslaState.hasRegisteredTesla
    }
}
