// Copyright © 2024 Toyota. All rights reserved.
import Foundation
import VehicleFeature
import SwiftUI
import Utility
import CoreLocation

internal struct FindStationsState {
    var odometerState: OdometerState
    var fuelState: FuelState
    var enrollmentState: EnrollmentCheckState
    var isFindStationsPlus: Bool
    var isHydrogenFUelCell: Bool
    var isPHEV: Bool
    var isWallet: Bool
    var isSubaru: Bool
    var locationCoordinates: LocationCoordinate
    var hydrogenStations: NearestHydrogenStations
    var stations: StationsState
    var isFavoritesLoaded: Bool
    var mapPins: [FindStationsAnnotationItem]?
    var isVehicleLocationAvailable: Bool
    var locationToCenter: LocationToCenter
    var offset: Int
    var chargeLevel: String
    var stationDetailButtonState: StationDetailButtonState
    var ionnaConsentDetails: IonnaConsentDetails?
    var teslaState: TeslaState
    public var isLoading = false
    public var stationList: [EvStation] = []
    public var error: Error?
    public var pageCount: Int = 0
    public var hasMoreStations = true
    public var routeToChargePoint: String?
    public var selectedStation: EvStation?
    public var showConnectorSelection = false
    public var selectedPartners: [String] = []
    public var selectedPlugTypes: [String] = []
    static var initial = FindStationsState(odometerState: .notAvailable,
                                           fuelState: .notAvailable,
                                           enrollmentState: .notAvailable,
                                           isFindStationsPlus: false,
                                           isHydrogenFUelCell: false,
                                           isPHEV: false,
                                           isWallet: false,
                                           isSubaru: false,
                                           locationCoordinates: .initial(),
                                           hydrogenStations: .initial,
                                           stations: .initial,
                                           isFavoritesLoaded: false,
                                           mapPins: [],
                                           isVehicleLocationAvailable: false,
                                           locationToCenter: .vehicleLocation,
                                           offset: 0,
                                           chargeLevel: "0",
                                           stationDetailButtonState: .initial,
                                           ionnaConsentDetails: nil,
                                           teslaState: TeslaState(),
                                           selectedPartners: [],
                                           selectedPlugTypes: [])
}

internal enum LocationToCenter {
    case curentLocation
    case vehicleLocation
}

internal enum NearestHydrogenStations {
    case initial
    case loading
    case success(EvNearestFuelStations?)
}

internal enum StationsState {
    case initial
    case loading
    case success(FindEvStationsListResponse, Bool)
}

internal struct FindStationsAnnotationItem: Identifiable {
    let id = UUID()
    var coordinate: CLLocationCoordinate2D
    let evStation: EvStation?
    let evFuelStation: EvFuelStation?

    init(coordinate: CLLocationCoordinate2D, evStation: EvStation? = nil, evFuelStation: EvFuelStation? = nil) {
        self.coordinate = coordinate
        self.evStation = evStation
        self.evFuelStation = evFuelStation
    }
}

internal enum StationDetailButtonState {
    case initial
    case loading
    case success(String, EVScreen)
}

internal enum EVScreen: Equatable {
    case none
    case chargingScreen
    case unlockStation
    case setupWallet
    case termsAndConditions
    case ionnaTermsAndConditions(String)
}

enum PlugType: String, CaseIterable, Comparable {
    case level2
    case dcFast
    case nacs
    case chademo

    static func < (lhs: PlugType, rhs: PlugType) -> Bool {
        return lhs.rawValue < rhs.rawValue
    }

    func toPlugCode() -> String {
        switch self {
        case .level2:
            return "J1772"
        case .dcFast:
            return "CCS1"
        case .chademo:
            return "CHADEMO"
        case .nacs:
            return "NACS"
        }
    }

    var displayName: String {
        switch self {
        case .level2:
            return "J1772"
        case .dcFast:
            return "CCS1"
        case .chademo:
            return "CHADEMO"
        case .nacs:
            return "NACS"
        }
    }

    var name: String {
        switch self {
        case .level2:
            return "Level 2"
        case .dcFast:
            return "CCS1"
        case .chademo:
            return "CHADEMO"
        case .nacs:
            return "NACS"
        }
    }

    var type: String {
        switch self {
        case .level2:
            return "Level 2"
        case .dcFast:
            return "DCFast"
        case .chademo:
            return "CHADEMO"
        case .nacs:
            return "NACS"
        }
    }

    var image: Image {
        switch self {
        case .level2:
            return ChargeInfoImages.Icons.chargerLevel2
        case .dcFast, .chademo:
            return ChargeInfoImages.Icons.ichargerDCcFast
        case .nacs:
            return ChargeInfoImages.Icons.ichargerNACS
        }
    }
}

internal struct TeslaState {
    var isVinEligibleForTesla = false
    var hasRegisteredTesla = false
}
