// Copyright © 2024 Toyota. All rights reserved.
// swiftlint:disable file_length
import Foundation
import Combine
import VehicleFeature
import Analytics
import Navigation
import Components
import Theme
import SwiftUI
import Utility
import NativeLinker
import CoreLocation
import LocalizedStrings

internal extension FindStationsLogic {
    func navigateToWallet(isSetupWallet: Bool) {
        navigationUseCases.pushToNativeNavigation(isSetupWallet ?
            .evWalletAddCardPage : .evWalletPage)
    }

    func showEvgoComplimentaryPopup(expiryDays: Int, walletSetup: Bool) {
        if expiryDays <= 30 && expiryDays >= 0 {
            if userDefaultsUseCases.getEvgoExpiringSoonPopupCount() <= 3 {
                userDefaultsUseCases.incrementEvgoExpiringSoonPopupCount()
                navigationUseCases.pushToNativeNavigation(
                    .evgoComplimentaryPopup(expiryDays: expiryDays,
                                            walletSetup: walletSetup))
            }
        } else {
            if expiryDays <= 0 && userDefaultsUseCases.getEvgoExpiredPopupCount() <= 3 {
                userDefaultsUseCases.incrementEvgoExpiredPopupCount()
                navigationUseCases.pushToNativeNavigation(
                    .evgoComplimentaryPopup(expiryDays: expiryDays,
                                            walletSetup: walletSetup))
            }
        }
    }

    func search(lat: String?, lon: String?) {
        Task { [weak self] in
            if self?.statePublished.isHydrogenFUelCell == true ||
                self?.statePublished.isPHEV == true {
                await self?.getHydrogenStations(lat: lat, lon: lon)
            } else {
                await self?.getStations(lat: lat, lon: lon)
            }
            // Set camera anchor to search location if search location is valid
            if let lat = lat, let lon = lon,
               let latDouble = Double(lat), let lonDouble = Double(lon) {
                self?.statePublished.locationCoordinates = LocationCoordinate(
                    latitude: latDouble,
                    longitude: lonDouble)
            }
        }
    }

    func getStationsCount(level1: String?, level2: String?, dcFast: String?) -> String {
        let l1 = Int(level1 ?? "0") ?? 0
        let l2 = Int(level2 ?? "0") ?? 0
        let fast = Int(dcFast ?? "0") ?? 0
        let totalCount = l1 + l2 + fast
        return String(totalCount)
    }

    func distanceBetweenLocations(lat: Double, lon: Double) -> String? {
        guard let vehicle = vehicle else { return nil }
        let location1 = CLLocation(latitude: statePublished.locationCoordinates.latitude,
                                   longitude: statePublished.locationCoordinates.longitude)
        let location2 = CLLocation(latitude: lat, longitude: lon)

        let distanceInMeters = location1.distance(from: location2)
        if vehicle.region == .usa {
            // Convert meters to miles
            let distance = distanceInMeters / 1_609.34
            return "\(String(format: "%.2f", distance)) mi"
        } else {
            // Convert meters to kilometers
            let distance = distanceInMeters / 1_000.0
            return "\(String(format: "%.2f", distance)) km"

        }
    }

    func fetchMapPinAnnotations() {
        if statePublished.isFindStationsPlus {
            switch statePublished.stations {
            case .initial, .loading:
                statePublished.mapPins = []
            case .success(let response, _):
                var stations: [EvStation]
                let selectedPartners = Set(statePublished.selectedPartners.map { $0.lowercased() })
                if selectedPartners.isEmpty {
                    stations = response.evStations
                } else {
                    stations = response.evStations.filter { station in
                        selectedPartners.contains(station.partnerInfoName?.lowercased() ?? "")
                    }
                }
                statePublished.mapPins = stations
                    .map({ station in
                        let lat = CLLocationDegrees(station.evEvses.first?.coordinates?.evLatitude ?? "")
                        let lon = CLLocationDegrees(station.evEvses.first?.coordinates?.evLongitude ?? "")
                        return FindStationsAnnotationItem(
                            coordinate: CLLocationCoordinate2D(
                                latitude: lat ?? .zero,
                                longitude: lon ?? .zero
                            ),
                            evStation: station)
                    })
            }
        } else {
            switch statePublished.hydrogenStations {
            case .initial, .loading:
                statePublished.mapPins = []
            case .success(let response):
                statePublished.mapPins = response?.payload.fuelStations
                    .map({ station in
                        FindStationsAnnotationItem(
                            coordinate: CLLocationCoordinate2D(
                                latitude: station.evLatitude,
                                longitude: station.evLongitude
                            ),
                            evFuelStation: station)
                    })
            }
        }
    }

    func callEVStationButton(station: EvStation, screen: EVScreen) {
        switch screen {
        case .none:
            break
        case .chargingScreen:
            navigationUseCases.pushToNativeNavigation(.chargingScreen)
        case .unlockStation:
            chargingStates(station: station)
        case .setupWallet:
            navigateToWallet(isSetupWallet: true)
        case .termsAndConditions:
            if let partnerName = station.evOperator {
                navigateToRegisterPartnerScreen(partner: partnerName.lowercased())
            }
        case .ionnaTermsAndConditions(let terms):
            navigationUseCases.pushToNativeNavigation(.evIonnaConsentScreen(termsAndConditions: terms))
        }

    }

    func getEVStationStatusTitle(station: EvStation) async {
        if case .success(let startCharge) = chargingState?.startChargeState,
           startCharge?.startChargeResponse.chargingId != nil {
            statePublished.stationDetailButtonState = .success(Strings.Common.continueBt, .chargingScreen)
        } else {
            let partnerName = station.evOperator?.lowercased()
            if partnerName == FindStationsConstants.ionna {
                await handleIonnaStation(station: station)
            } else {
                await handleOtherStations(station: station, partnerName: partnerName)
            }
        }
    }

    private func handleIonnaStation(station: EvStation) async {
        statePublished.stationDetailButtonState = .loading

        guard case .success(let enrollment) = statePublished.enrollmentState else {
            return
        }

        let partnerEnrollment = enrollment.payload?.partnerEnrollment
        let walletNotFound = partnerEnrollment?.wallet?.lowercased() == FindStationsConstants.notFound.lowercased()
        if walletNotFound {
            statePublished.stationDetailButtonState = .success(
                Strings.EvgoExpiryPopup.setupWallet,
                .setupWallet
            )
            return
        }
        await chargeInfoUseCases.fetchIonnaConsentDetails()
        if case .success(let isAccepted, let termsAndConditions) = self.chargeInfoState?.ionnaConsentDetails,
           let termsAndConditions = termsAndConditions {
            if let isAccepted, isAccepted {
                let buttonTitle = station.isUnlockCapable
                ? Strings.FindStations.unlockStation
                : Strings.FindStations.startCharging

                statePublished.stationDetailButtonState = .success(
                    buttonTitle,
                    .unlockStation
                )
            } else {
                statePublished.stationDetailButtonState = .success(
                    chargeInfoUseCases.getIonnaButtonTitle(acknowledged: isAccepted),
                    .ionnaTermsAndConditions(termsAndConditions)
                )
            }
        } else {
            statePublished.stationDetailButtonState = .initial
        }
    }

    private func handleOtherStations(station: EvStation, partnerName: String?) async {
        guard let vehicle,
              case .success(let enrollment) = statePublished.enrollmentState
        else {
            return
        }
        let isLexus = vehicle.make == .lexus
        let partnerEnrollment = enrollment.payload?.partnerEnrollment
        let walletFound = checkWalletFoundOrNot(
            partnerEnrollment: partnerEnrollment,
            partnerName: partnerName,
            isLexus: isLexus
        )
        let walletNotFound = partnerEnrollment?.wallet?.lowercased() == FindStationsConstants.notFound.lowercased()
        let chargePointStatusNotFound = partnerEnrollment?.partnerStatus?.first(where: {
            $0.partnerName == FindStationsConstants.chargePoint
        })?.status?.lowercased() == FindStationsConstants.notFound.lowercased()

        let evgoStatusNotFound = partnerEnrollment?.partnerStatus?.first(where: {
            $0.partnerName == FindStationsConstants.evGo
        })?.status?.lowercased() == FindStationsConstants.notFound.lowercased()

        if walletFound || (walletNotFound && chargePointStatusNotFound && evgoStatusNotFound) {
            checkPartnerEnrollment(
                partnerEnrollment: partnerEnrollment ?? EVPartnerEnrollment(guid: "", wallet: "", partnerStatus: []),
                partnerName: partnerName,
                chargeStationLocationInfo: station,
                isLexus: isLexus
            )
        } else {
            if partnerName == FindStationsConstants.chargePoint {
                statePublished.stationDetailButtonState = .success(
                    Strings.EvgoExpiryPopup.setupWallet, .setupWallet
                )
            } else {
                statePublished.stationDetailButtonState = .success(
                    Strings.EvPartnerRegistration.register, .termsAndConditions
                )
            }
        }
    }

    func checkWalletFoundOrNot(
        partnerEnrollment: EVPartnerEnrollment?,
        partnerName: String?,
        isLexus: Bool
    ) -> Bool {
        if !isLexus {
            return partnerEnrollment?.wallet?.lowercased() == FindStationsConstants.found.lowercased() ||
            (partnerName == FindStationsConstants.evGo && checkEvgoSubscriptionNotEnded(partnerEnrollment))
        } else {
            return partnerEnrollment?.wallet?.lowercased() == FindStationsConstants.found.lowercased()
        }
    }

    func checkPartnerEnrollment(
        partnerEnrollment: EVPartnerEnrollment,
        partnerName: String?,
        chargeStationLocationInfo: EvStation,
        isLexus: Bool
    ) {
        guard let partnerName = partnerName?.lowercased(),
              let partnerStatusList = partnerEnrollment.partnerStatus else {
            return
        }

        for partnerStatus in partnerStatusList where partnerStatus.partnerName?.lowercased() == partnerName {
            let lowercasedPartnerName = partnerStatus.partnerName?.lowercased()
            let isEvgo = lowercasedPartnerName == FindStationsConstants.evGo
            let isChargePoint = lowercasedPartnerName == FindStationsConstants.chargePoint
            let evgoSubscriptionActive = checkEvgoSubscriptionNotEnded(partnerEnrollment)
            let isFoundStatus = partnerStatus.status?.lowercased() == FindStationsConstants.found.lowercased()
            let walletNotFound = partnerEnrollment.wallet?.lowercased() == FindStationsConstants.notFound.lowercased()

            let chargePointPartner = partnerEnrollment.partnerStatus?.first(where: {
                $0.partnerName?.lowercased() == FindStationsConstants.chargePoint
            })
            let chargePointStatusFound =
            chargePointPartner?.status?.lowercased() == FindStationsConstants.found.lowercased()

            let evgoCheck = isEvgo && evgoSubscriptionActive && isFoundStatus
            let evgoExpiredCheck = isEvgo && !evgoSubscriptionActive && isFoundStatus && chargePointStatusFound
            let chargePointCheck = isChargePoint && chargePointStatusFound

            if evgoCheck || evgoExpiredCheck || chargePointCheck {
                let buttonName = isChargePoint && !chargeStationLocationInfo.isRoamingEVgo()
                ? Strings.FindStations.unlockStation
                : Strings.FindStations.startCharging
                statePublished.stationDetailButtonState = .success(buttonName, .unlockStation)
                return
            }

            if isEvgo && isFoundStatus && !evgoSubscriptionActive {
                statePublished.stationDetailButtonState = .success(
                    Strings.EvgoExpiryPopup.registerWithChargePoint, .termsAndConditions
                )
                return
            }
            let chargePointWalletNotFound = walletNotFound && isChargePoint
            let evgoWalletNotFound = walletNotFound && isEvgo && evgoExpired(partnerEnrollment)
            if chargePointWalletNotFound || evgoWalletNotFound {
                statePublished.stationDetailButtonState = .success(Strings.EvgoExpiryPopup.setupWallet, .setupWallet)
                return
            }
            statePublished.stationDetailButtonState = .success(
                Strings.EvPartnerRegistration.register, .termsAndConditions
            )
        }
    }

    func checkEvgoSubscriptionNotEnded(_ partnerEnrollment: EVPartnerEnrollment?) -> Bool {
        guard let partnerEnrollment = partnerEnrollment,
              let partnerStatus = partnerEnrollment.partnerStatus,
              let evgoInfo = partnerStatus.first(where: { $0.partnerName == FindStationsConstants.evGo }),
              let expiryDateString = evgoInfo.expiryDate,
              let expiryDate = expiryDateString.convertStringToDate
        else { return false }
        return expiryDate >= Date()
    }

    func evgoExpired(_ partnerEnrollment: EVPartnerEnrollment?) -> Bool {
        guard let partnerEnrollment = partnerEnrollment,
              let partnerStatus = partnerEnrollment.partnerStatus,
              let evgoInfo = partnerStatus.first(where: { $0.partnerName == FindStationsConstants.evGo }),
              let expiryDateString = evgoInfo.expiryDate,
              let expiryDate = expiryDateString.convertStringToDate
        else { return false }
        return expiryDate < Date()
    }

    private struct StationDetails {
        let locationId: String
        let evseUid: String
        let connectorId: String
    }

    private func handleIonnaStation(_ station: EvStation) -> StationDetails {
        if let firstEvse = station.evEvses.first {
            let locationId = station.evId ?? ""
            let evseUid = firstEvse.evseId ?? ""
            let connectorId = firstEvse.connectors?.first?.evId ?? ""
            return StationDetails(
                locationId: locationId,
                evseUid: evseUid,
                connectorId: connectorId
            )
        } else {
            return StationDetails(
                locationId: station.evId ?? "",
                evseUid: "",
                connectorId: ""
            )
        }
    }

    private func handleNonIonnaStation(_ station: EvStation) -> StationDetails {
        let locationId = station.partnerInfoId?.isEmpty == false ?
        station.partnerInfoId ?? "" : station.evId ?? ""
        let evseUid = station.evEvses.first?.uid ?? ""

        let connectorId: String
        if station.evEvSource.lowercased() == "evgo" {
            if let connector = station.evEvses.first?.connectors?.first {
                connectorId = connector.evId ?? evseUid
            } else if let physRef = station.evEvses.first?.physicalReference,
                      !physRef.isEmpty {
                connectorId = physRef
            } else {
                connectorId = evseUid
            }
        } else {
            connectorId = station.evEvses.first?.connectors?.first?.evId ??
            station.partnerInfoId ?? ""
        }

        return StationDetails(
            locationId: locationId,
            evseUid: evseUid,
            connectorId: connectorId
        )
    }

    private func chargingStates(station: EvStation) {
        Task { [weak self] in
            guard let self = self else { return }
            let wallet = await self.chargeInfoAPIRepo.fetchWallet()

            // Check if there are multiple connectors available to select from
            let connectors = station.evEvses.flatMap { $0.connectors ?? [] }
            let physicalReferences = station.evEvses.compactMap { $0.physicalReference }

            // If there are multiple connectors, show selection UI first
            if connectors.count > 1 || physicalReferences.count > 1 {
                await MainActor.run {
                    self.statePublished.selectedStation = station
                    self.updateConnectorSelectionVisibility(show: true)
                }
                return
            }

            // Get partner name and determine if it's an IONNA station
            let partnerName = self.chargeStartRequestPartnerName(station) ?? ""
            let isIonna = partnerName.lowercased() == "ionna"

            // Get station details based on partner type
            let stationDetails: StationDetails
            if isIonna {
                stationDetails = handleIonnaStation(station)
            } else {
                stationDetails = handleNonIonnaStation(station)
            }

            // Create and send the charge request
            let chargeRequest = ChargeStartRequest(
                partnerName: partnerName,
                locationId: stationDetails.locationId,
                evseUid: stationDetails.evseUid,
                connectorId: stationDetails.connectorId,
                paymentMethod: wallet?.card?.id ?? "",
                useEvseUid: isIonna
            )

            await self.chargingUseCases.startCharging(body: chargeRequest)

            // Handle navigation after successful charge start
            let chargingState = self.chargingState
            let navigationUseCases = self.navigationUseCases

            await MainActor.run {
                if case .success(let startCharge) = chargingState?.startChargeState,
                   startCharge?.startChargeResponse.chargingId != nil {
                    navigationUseCases.pushToNativeNavigation(.chargingScreen)
                } else {
                    print("Error with charging State start charging")
                }
            }
        }
    }

    func chargeStartRequestPartnerName(_ station: EvStation) -> String? {
        return station.partnerInfoName?.lowercased() ?? station.evOperator?.lowercased()
    }

    func getPartnersList() -> [String] {
        var partners = [
            Strings.ChargeInfo.chargePoint,
            FindStationsConstants.partnerFilterEvConnect,
            FindStationsConstants.partnerFilterEvgo,
            FindStationsConstants.partnerFilterFloNetwork,
            FindStationsConstants.partnerFilterGreenLots,
            FindStationsConstants.partnerFilterShellRecharge,
            FindStationsConstants.partnerFilterIonna
        ]
        if !nativeLinkerContainer.isEVIonnaEnabled() {
            partners.remove(FindStationsConstants.partnerFilterIonna)
        }
        if nativeLinkerContainer.isEVTeslaEnabled() {
            partners.append(Strings.ChargeInfo.tesla)
        }
        return partners
    }

    func handleFilters(selectedPartners: [String],
                       selectedPlugTypes: [String]) async {
        let correctedPartners = selectedPartners.map {
            switch $0 {
            case Strings.ChargeInfo.chargePoint:
                return FindStationsConstants.partnerFilterChargePoint
            case Strings.ChargeInfo.tesla:
                return FindStationsConstants.partnerFilterTesla
            default:
                return $0
            }
        }
        let operatorName = correctedPartners.isEmpty ? nil :
        correctedPartners.joined(separator: ",")
        let connectors = selectedPlugTypes.isEmpty ? nil :
        selectedPlugTypes.joined(separator: ",")
        let filters = StationsFilter(limit: nil,
                                     radius: nil,
                                     partnertype: nil,
                                     connector: connectors,
                                     open: nil,
                                     operatorname: operatorName)
        if correctedPartners.isEmpty, selectedPlugTypes.isEmpty {
            await getStations()
        } else {
            await getStations(filters: filters)
        }
    }

    func getFavorites() -> String {
        nativeLinkerContainer.poiServiceUseCases().getFavorites()
    }

    func loadFavorites() -> Bool {
        nativeLinkerContainer.poiServiceUseCases().loadFavorites()
    }

    func addFavorite(poi: String) -> Bool {
        let id = "https://www.google.com/maps/place/?place_id=\(poi)"
        return nativeLinkerContainer.poiServiceUseCases().addFavorite(poi: id)
    }

    func removeFavorite(placeIds: String) -> Bool {
        let id = "https://www.google.com/maps/place/?place_id=\(placeIds)"
        return nativeLinkerContainer.poiServiceUseCases().removeFavorite(placeId: id)
    }

    func toggleFavorites(placeId: String, isFavorite: Bool, isShowFavorites: Bool) {
        if isFavorite {
            _ = removeFavorite(placeIds: placeId)
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                if self.loadFavorites() {
                    self.statePublished.isFavoritesLoaded = true
                }
            }
        } else {
            _ = addFavorite(poi: placeId)
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                if self.loadFavorites() {
                    self.statePublished.isFavoritesLoaded = true
                }
            }
        }
        if case .success(let response, _) = statePublished.stations {
            var stations = response.evStations
            for index in stations.indices where stations[index].evPlaceId == placeId {
                stations[index].isFavourite.toggle()
            }
            if isShowFavorites && isFavorite {
                stations.removeAll { $0.evPlaceId == placeId }
            }
            statePublished.stations = .success(
                FindEvStationsListResponse(
                    evTotalNoOfRecords: response.evTotalNoOfRecords,
                    evStations: stations
                ), true)
        }
    }

    func oopenWebSite(url: String) {
        guard let website = URL(string: url) else { return }
        utilityContainer.applicationUseCases.openURL(website)
    }
}

// The mapFavorite function has been moved to FindStationsLogic+MapOperations.swift
