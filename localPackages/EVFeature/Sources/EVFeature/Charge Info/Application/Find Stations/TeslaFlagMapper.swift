// Copyright © 2025 Toyota. All rights reserved.

import Foundation

protocol TeslaFlagMapping {
    func map(_ cis: ChargeInfoState?) -> (<PERSON><PERSON>, <PERSON><PERSON>)
}

class TeslaFlagMapper: TeslaFlagMapping {
    func map(_ cis: ChargeInfoState?) -> (<PERSON><PERSON>, <PERSON><PERSON>) {
        let eligible = cis?.isEvTesla ?? false
        let registered: <PERSON><PERSON>
        switch cis?.teslaConsentDetails {
        case .success(let accepted, _):
            registered = accepted ?? false
        default:
            registered = false
        }
        return (eligible, registered)
    }
}
