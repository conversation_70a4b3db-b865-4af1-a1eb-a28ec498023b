// Copyright © 2024 Toyota. All rights reserved.

import Foundation
import SwiftUI
import VehicleFeature
import Analytics
import Navigation
import NetworkClients
import Utility
import Native<PERSON>inker
import VehicleShopFeature
import DashboardFeature

public final class ChargeInfoContainer {
    lazy var dashboardUsecases: ChargeInfoUseCases = {
        ChargeInfoLogic(electricStatusUseCases: vehicleContainer.selectedVehicleElectricStatus(),
                        telemetryUseCases: vehicleContainer.selectedVehicleTelemetry(),
                        selectedVehicleUseCases: vehicleContainer.selectedVehicleUseCases(),
                        analyticsUseCases: analyticsContainer.analyticsUseCases,
                        chargeInfoAPIRepo: chargeInfoAPIRepo,
                        navigationUseCases: navigationContainer.navigationUseCases,
                        userDefaultsUseCases: utilityContainer.userDefaultUseCases,
                        chargingUseCases: chargingUseCase)
    }()

    lazy var chargeScheduleUsecases: ChargeScheduleUseCase = {
        ChargeScheduleLogic(electricStatusUseCases: vehicleContainer.selectedVehicleElectricStatus(),
                            telemetryUseCases: vehicleContainer.selectedVehicleTelemetry(),
                            selectedVehicleUseCases: vehicleContainer.selectedVehicleUseCases(),
                            analyticsUseCases: analyticsContainer.analyticsUseCases,
                            chargeInfoAPIRepo: chargeInfoAPIRepo,
                            navigationUseCases: navigationContainer.navigationUseCases)
    }()

    lazy var enrollmentUseCases: EnrollmentUseCase = {
        EnrollmentLogic(selectedVehicleUseCases: vehicleContainer.selectedVehicleUseCases(),
                        analyticsUseCases: analyticsContainer.analyticsUseCases,
                        chargeInfoAPIRepo: chargeInfoAPIRepo,
                        navigationUseCases: navigationContainer.navigationUseCases,
                        customerCareUseCases: dashboardContainer.customerCareUseCases)
    }()

    lazy var findStationsUseCase: FindStationsUseCase = {
        FindStationsLogic(electricStatusUseCases: vehicleContainer.selectedVehicleElectricStatus(),
                          telemetryUseCases: vehicleContainer.selectedVehicleTelemetry(),
                          selectedVehicleUseCases: vehicleContainer.selectedVehicleUseCases(),
                          analyticsUseCases: analyticsContainer.analyticsUseCases,
                          chargeInfoAPIRepo: chargeInfoAPIRepo,
                          navigationUseCases: navigationContainer.navigationUseCases,
                          userDefaultsUseCases: utilityContainer.userDefaultUseCases,
                          nativeLinkerLogic: nativeLinkerContainer.accountUserProfileUseCases(),
                          chargingUseCases: chargingUseCase,
                          chargeInfoUseCases: dashboardUsecases,
                          teslaFlagMapper: teslaFlagMapper)
    }()

    lazy var chargingUseCase: ChargingUseCase = {
        ChargingLogic(electricStatusUseCases: vehicleContainer.selectedVehicleElectricStatus(),
                          telemetryUseCases: vehicleContainer.selectedVehicleTelemetry(),
                          selectedVehicleUseCases: vehicleContainer.selectedVehicleUseCases(),
                          analyticsUseCases: analyticsContainer.analyticsUseCases,
                          repo: chargeInfoAPIRepo,
                          navigationUseCases: navigationContainer.navigationUseCases,
                          userDefaultsUseCases: utilityContainer.userDefaultUseCases,
                      nativeLinkerLogic: nativeLinkerContainer.accountUserProfileUseCases(),
                      customerCareUseCases: dashboardContainer.customerCareUseCases)
    }()

    lazy var teslaFlagMapper: TeslaFlagMapping = {
        TeslaFlagMapper()
    }()

    lazy var chargeInfoStateNotifier: ChargeInfoStateNotifier = {
        ChargeInfoStateNotifier(useCases: dashboardUsecases,
                                selectedVehicleUseCases: vehicleContainer.selectedVehicleLogic)
    }()

    lazy var chargeInfoAPIRepo: EvAPIRepo = {
        EvAPIRepo(provider: OneapiContainer.vehicle,
                  findProvider: OneapiContainer.findStations,
                  elegibilityProvider: OneapiContainer.announcements,
                  providerWallet: OneapiContainer.wallet,
                  chargingProvider: OneapiContainer.publicCharging)
    }()

    private lazy var findStationsStateNotifier: FindStationsStateNotifier = {
        FindStationsStateNotifier(useCases: findStationsUseCase)
    }()

    public lazy var chargeScheduleStateNotifier: ChargeScheduleStateNotifier = {
        ChargeScheduleStateNotifier(useCases: chargeScheduleUsecases)
    }()

    private lazy var enrollmentStateNotifier: PartnerEnrollmentStateNotifier = {
        PartnerEnrollmentStateNotifier(useCases: enrollmentUseCases)
    }()

    private lazy var chargingStateNotifier: ChargingStateNotifier = {
        ChargingStateNotifier(useCases: chargingUseCase)
    }()

    public func chargeInfoScreen(fromChargeNow: Bool = false) -> ChargeInfoScreen {
        return ChargeInfoScreen(stateNotifier: chargeInfoStateNotifier,
                                chargingNotifier: chargingStateNotifier,
                                isFromChargeNow: fromChargeNow)
    }

    public func chargeManagementScreen() -> ChargeManagementScreen {
        return ChargeManagementScreen(
            stateNotifier: chargeInfoStateNotifier,
            scheduleNotifier: chargeScheduleStateNotifier, chargingNotifier: chargingStateNotifier
        )
    }

    public func chargeHistoryScreen() -> ChargeHistoryScreen {
        return ChargeHistoryScreen(stateNotifier: chargeInfoStateNotifier)
    }

    public func chargeScheduleScreen(isWattTimeDisabled: Bool = false) -> ChargeScheduleScreen {
        return ChargeScheduleScreen(stateNotifier: chargeScheduleStateNotifier, isWattTimeDisabled: isWattTimeDisabled)
    }

    public func manualScheduleScreen() -> ManualScheduleScreen {
        return ManualScheduleScreen(stateNotifier: chargeScheduleStateNotifier, isWattTimeDisabled: false)
    }

    public func createModifyScheduleScreen(isChargeManagement: Bool) -> CreateModifyScheduleScreen {
        return CreateModifyScheduleScreen(stateNotifier: chargeScheduleStateNotifier, startDate: .now,
                                          endDate: .now, isOffPeakHours: false, showEndTime: false,
                                          selectedDays: Array(repeating: false, count: 7),
                                          settingId: nil,
                                          isChargeManagement: isChargeManagement,
                                          isModify: false)
    }

    public func createSchedulePHEV(isDeparture: Bool, isClimate: Bool) -> NewSchedulePHEVView {
        return NewSchedulePHEVView(stateNotifier: chargeScheduleStateNotifier,
                                   isDeparture: isDeparture,
                                   isClimate: isClimate)
    }

    public func statisticsScreen() -> StatisticsScreen {
        return StatisticsScreen(stateNotifier: chargeInfoStateNotifier)
    }

    public func cleanAssistDetailView() -> CleanAssistDetailView {
        return CleanAssistDetailView(stateNotifier: chargeInfoStateNotifier)
    }

    public func partnerEnrollmentTermsScreen(partner: String) -> PartnerEnrollmentTermsScreen {
        PartnerEnrollmentTermsScreen(partner: partner, stateNotifier: enrollmentStateNotifier)
    }

    public func eVgoExpiryPopupView(expiryDays: Int, walletSetup: Bool) -> EVgoExpiryPopupView {
        EVgoExpiryPopupView(expiryDays: expiryDays, walletSetup: walletSetup, stateNotifier: chargeInfoStateNotifier)
    }

    public func findStationsScreen() -> FindStationsScreen {
        return FindStationsScreen(stateNotifier: findStationsStateNotifier)
    }

    public func chargingScreen() -> ChargingScreen {
        return ChargingScreen(stateNotifier: chargingStateNotifier)
    }

    public func ionnaConsentScreen(termsAndConditions: String) -> IonnaConsentScreen {
        return IonnaConsentScreen(stateNotifier: chargeInfoStateNotifier,
                                  termsAndConditions: termsAndConditions)
    }

    public func ecoChargingLearnMoreView(wattTimeUrl: String) -> EcoChargeLearnMoreView {
        return EcoChargeLearnMoreView(brandName: chargeInfoStateNotifier.getBrandName(), wattTimeUrl: wattTimeUrl)
    }
}

public let chargeInfoContainer = ChargeInfoContainer()
