// Copyright © 2024 Toyota. All rights reserved.
// swiftlint:disable type_body_length
import SwiftUI
import Components
import Theme
import LocalizedStrings

struct FindStationsPlusDetailView: View {
    @ObservedObject var stateNotifier: FindStationsStateNotifier
    var station: EvStation
    var source: String?
    var distance: String?
    var availability: String?
    var isFavorite: Bool
    var isPartner: Bool
    var header: String
    var plugAvailability: [[String]]
    var backAction: () -> Void
    var toggleFavorite: () -> Void
    var getDirections: () -> Void
    var sendToCarAction: () -> Void
    var pricingAction: () -> Void
    @State private var faveIconState = false

    init(stateNotifier: FindStationsStateNotifier,
         station: EvStation,
         plugAvailability: [[String]],
         backAction: @escaping () -> Void,
         toggleFavorite: @escaping () -> Void,
         getDirections: @escaping () -> Void,
         sendToCarAction: @escaping () -> Void,
         pricingAction: @escaping () -> Void) {
        self.stateNotifier = stateNotifier
        self.station = station
        self.plugAvailability = plugAvailability
        self.backAction = backAction
        self.toggleFavorite = toggleFavorite
        self.getDirections = getDirections
        self.sendToCarAction = sendToCarAction
        self.pricingAction = pricingAction
        let evSource = station.evOperator ?? station.formattedEvSource()
        let isPartnerOverride = station.isPartner(isWalletEnabled: stateNotifier.state.isWallet)
        header = isPartnerOverride ?
            Strings.FindStations.partnerStationHeading :
            Strings.FindStations.nonPartnerStationHeading
        source = evSource
        distance = stateNotifier.distanceBetweenLocations(
            lat: station.evLatitude ?? 0.0,
            lon: station.evLongitude ?? 0.0
        ) ?? ""
        availability = station.evOpeningTimes?.evTiming ?? ""
        isFavorite = station.isFavourite
        isPartner = isPartnerOverride
    }

    var body: some View {
        VStack {
            RoundedRectangle(cornerRadius: 2)
                .frame(width: 24, height: 4)
                .foregroundColor(AppTheme.colors.tertiary10)
                .padding()
            navBar
                .padding(.bottom)
            contentView
            Spacer()

            if stateNotifier.shouldShowRegisterButton(for: station) {
                teslaRegisterButton()
            }

            if isPartner {
                actionButtonView
            }
        }
        .background(AppTheme.colors.tertiary15)
        .onAppear {
            faveIconState = isFavorite
        }
        .task {
            await stateNotifier.getEVStationStatusTitle(station: station)
            await stateNotifier.fetchIonnaConsent()
        }
    }

    @ViewBuilder
    private var topRow: some View {
        HStack {
            if let source = source {
                OATextView.body04Tertiary03(source)
                    .lineLimit(2, reservesSpace: false)
            }
            Spacer()
            if let distance = distance {
                OATextView.callout01Tertiary05(distance)
            }
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 2)
    }

    @ViewBuilder
    private var centerRow: some View {
        VStack(alignment: .leading) {
            if !station.isTitleEmpty {
                OATextView.callout01Tertiary05(station.trimmedTitle)
                    .lineLimit(2, reservesSpace: false)
            }
            if !station.isAddressEmpty {
                OATextView.callout01Tertiary05(station.trimmedAddressWithCity)
            }
        }
        .multilineTextAlignment(.leading)
        .padding(.horizontal, 16)
    }

    @ViewBuilder
    private var directionsView: some View {
        VStack {
            OACircleIconView(image: ChargeInfoImages.Icons.findDirections)
            .padding(.trailing, 8)
            OATextView.body03Tertiary03(Strings.FindStations.directions)
        }
        .padding(.vertical, 8)
    }

    @ViewBuilder
    private var pricing: some View {
        VStack {
            OACircleIconView(image: ChargeInfoImages.Icons.pricing)
                .padding(.trailing, 8)
            OATextView.body03Tertiary03(Strings.FindStations.pricing)
        }
        .padding(.vertical, 8)
    }

    @ViewBuilder
    private var sendToCar: some View {
        VStack {
            OACircleIconView(image: ChargeInfoImages.Icons.sendToCar)
                .padding(.trailing, 8)
            OATextView.body03Tertiary03(Strings.FindStations.sendToCar)
        }
        .padding(.vertical, 8)
    }

    @ViewBuilder
    private var actionsList: some View {
        HStack(alignment: .center) {
            Spacer()
            if isPartner && station.evTariffInfo != nil {
                pricing
                    .accessibilityAddTraits(.isButton)
                    .onTapGesture {
                        pricingAction()
                    }
            }
            Spacer()
            sendToCar
                .padding(.horizontal, 16)
                .accessibilityAddTraits(.isButton)
                .accessibilityIdentifier(FindStationsScreen.OAAccessibilityIdentifiers.sendToCar)
                .onTapGesture {
                    sendToCarAction()
                }
            Spacer()
            directionsView
                .padding(.horizontal, 16)
                .accessibilityIdentifier(FindStationsScreen.OAAccessibilityIdentifiers.directions)
                .accessibilityAddTraits(.isButton)
                .onTapGesture {
                    getDirections()
                }
            Spacer()
        }
    }

    @ViewBuilder
    private var availablePlugsGroup: some View {
        HStack {
            HStack(spacing: 4) {
                if plugAvailability.first?.last != "0" ||
                    plugAvailability.last?.last != "0" {
                    OATextView.body04Tertiary03(Strings.FindStations.availablePlugs)
                }
                Spacer()
                if plugAvailability.count >= 1 {
                    plugs
                }
            }
            .padding(.horizontal, 16)
        }
        .background(RoundedRectangle(cornerRadius: 12).fill(AppTheme.colors.tile05))
        .padding(.horizontal, 16)
    }

    @ViewBuilder
    private var plugs: some View {
        let filteredPlugs = plugAvailability.filter {
            let count = $0.last ?? ""
            return count.isNotEmpty && count != "0/0"
        }
        if filteredPlugs.count == 1, let plug = filteredPlugs.first {
            AvailablePlugsView(level: plug.first ?? "",
                               count: plug.last ?? "")
        } else {
            ScrollView(.horizontal) {
                HStack {
                    Spacer()
                    ForEach(filteredPlugs, id: \.self) { plug in
                        AvailablePlugsView(level: plug.first ?? "",
                                           count: plug.last ?? "")
                    }
                }
            }
        }
    }

    @ViewBuilder
    private var contentView: some View {
        VStack(alignment: .leading) {
            topRow
            centerRow
            HStack {
                AvailabilityView(availability: availability)
                Spacer()
                if stateNotifier.isMyDestinationEnabled(),
                   let placeId = station.evPlaceId, placeId.isNotEmpty {
                    FavoritesIconView(isFavorite: faveIconState)
                        .onTapGesture {
                            toggleFavorite()
                            faveIconState.toggle()
                        }
                }
            }
            .padding(.horizontal, 16)
            actionsList
                .padding(.top)
            Group {
                if isPartner {
                    availablePlugsGroup
                } else {
                    OATextView.body04Tertiary00(Strings.FindStations.chargingNotAvailable)
                        .multilineTextAlignment(.center)
                        .frame(maxWidth: .infinity, alignment: .center)
                }
            }
            .padding(.top)
        }
        .padding(.horizontal, 16)
    }

    @ViewBuilder
    private var actionButtonView: some View {
        switch stateNotifier.state.stationDetailButtonState {
        case .initial:
            EmptyView()
        case .loading:
            ProgressView()
        case .success(let buttonTitle, let eVScreen):
            if eVScreen == .chargingScreen {
                OATextView.callout01Tertiary05(Strings.EvSchedule.chargingInProgress)
                    .padding(.bottom, 8)
            }
            OAButtonView.primary(buttonTitle) {
                stateNotifier.callEVStationButton(station: station, screen: eVScreen)
            }
            .padding(.horizontal, 92)
            .padding(.bottom, 8)
        }

    }

    @ViewBuilder
    private var navBar: some View {
        ZStack {
            HStack {
                OACircleIconView(image: IconImages.Common.chevronLeft)
                    .padding(.leading, 16)
                    .onTapGesture {
                        backAction()
                    }
                Spacer()
            }
            HStack {
                Spacer()
                OATextView.subheadline03Tertiary03(header)
                Spacer()
            }
        }
    }

    @ViewBuilder
    func teslaRegisterButton() -> some View {
        VStack {
            switch stateNotifier.state.ionnaConsentDetails {
            case .loading, .notAvailable:
                OALoadingView(height: 60,
                              cornerRadius: 10,
                              showShadow: true)
                .padding(.all, 16)
            case .success(_, let termsAndConditions):
                OAButtonView.primary(Strings.EvPartnerRegistration.register) {
                    stateNotifier.navigateToIonnaConsent(termsAndConditions: termsAndConditions ?? "")
                }
                .padding(.horizontal, 92)
                .padding(.bottom, 8)
                .accessibilityIdentifier("register_tesla_button")
            default:
                EmptyView()
            }
        }
    }
}

/*#Preview {
    FindStationsPlusDetailView(title: "ChargePoint",
                               address: "123 heaven lane",
                               distance: "1.2 mi",
                               availability: "24 hours daily",
                               isFavorite: true,
                               isPartner: true,
                               header: Strings.FindStations.chargeStationDetails,
                               plugAvailability: [
                                ["1/2", "level 1"],
                                ["2/3", "level 2"]
                               ],
                               backAction: {},
                               buttonTitle: "Register",
                               buttonAction: {},
                               toggleFavorite: {},
                               getDirections: {},
                               sendToCarAction: {},
                               pricingAction: {})
}*/
