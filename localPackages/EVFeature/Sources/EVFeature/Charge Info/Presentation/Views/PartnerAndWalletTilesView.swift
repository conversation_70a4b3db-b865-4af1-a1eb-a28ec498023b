// Copyright © 2024 Toyota. All rights reserved.

import SwiftUI
import Components
import Theme
import VehicleFeature
import Navigation
import LocalizedStrings

internal struct PartnerAndWalletTilesView: View {

    @ObservedObject var stateNotifier: ChargeInfoStateNotifier
    let showHeading: Bool

    private var state: ChargeInfoState {
        stateNotifier.state
    }

    var body: some View {
        VStack {
            switch state.enrollmentState {
            case .notAvailable:
                EmptyView()
            case .loadinng:
                if showHeading {
                    contentTitle
                }
                OALoadingView(height: 60,
                              cornerRadius: 10,
                              showShadow: true)
                    .padding(.all, 16)
            case .success(let enrollment):
                if showHeading {
                    contentTitle
                }
                enrollmentContent(enrollment)
            }
        }
    }

    @ViewBuilder
    func evgoContent(_ enrollment: EVEnrollmentCheck) -> some View {
        if let evgo = enrollment.payload?.partnerEnrollment?.partnerStatus?
            .first(where: { $0.partnerName == "evgo" }),
           stateNotifier.isEVGoExpired(evgo) == false {
            if evgo.status == "Found" {
                ChargeInfoSimpleTile(icon: ChargeInfoImages.Icons.lightingBolt,
                                     text: Strings.ChargeInfo.evgo, description: evgo
                    .expiryDate?.isNotEmpty ?? false ?
                                     Strings.EvgoExpiryPopup
                    .evgoExpiryDate.replacingOccurrences(of: "%0",
                                                         with: evgo.expiryDate?.toMMMddYYYY() ?? "") :
                                        Strings.EvgoExpiryPopup.evgoFreeOneYearText,
                                     registered: true)
                .onFirstAppear {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        stateNotifier.showEvgoComplimentaryPopup(
                            expiryDays: Date.daysBetween(dateString: evgo.expiryDate ?? "") ?? 0,
                            walletSetup: enrollment.payload?.partnerEnrollment?.wallet == "Found"
                        )
                    }
                }
            } else {
                HStack {
                    ChargeInfoSimpleTile(
                        icon: ChargeInfoImages.Icons.lightingBolt,
                        text: Strings.ChargeInfo.evgo,
                        description: Strings.EvgoExpiryPopup.evgoFreeOneYearText,
                        buttonTitle: Strings.EvPartnerRegistration.register
                    ) {
                        stateNotifier.navigateToRegisterPartnerScreen(partner: "EVgo")
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
        }
    }

    @ViewBuilder
    var chargePointNotFound: some View {
        ChargeInfoSimpleTile(icon: ChargeInfoImages.Icons.lightingBolt,
                             text: Strings.ChargeInfo.chargePoint, description: "",
                             buttonTitle: Strings.EvPartnerRegistration.register) {
            stateNotifier.navigateToRegisterPartnerScreen(partner: "chargepoint")
        }
    }

    @ViewBuilder
    var chargePointFound: some View {
        ChargeInfoSimpleTile(icon: ChargeInfoImages.Icons.lightingBolt,
                             text: Strings.ChargeInfo.chargePoint, description: "",
                             registered: true) {
            /* CP Active, no action*/
        }
    }

    @ViewBuilder
    func chargePointContent(_ enrollment: EVEnrollmentCheck) -> some View {
        if let chargepoint = enrollment.payload?
            .partnerEnrollment?.partnerStatus?
            .first(where: { $0.partnerName == "chargepoint" }) {
            if chargepoint.status == "Found" {
                chargePointFound
            } else {
                chargePointNotFound
            }
        }
    }

    @ViewBuilder
    func chargingStationsContent() -> some View {
        VStack {
            switch state.teslaConsentDetails {
            case .notAvailable:
                EmptyView()
            case .loading:
                OALoadingView(height: 60,
                              cornerRadius: 10,
                              showShadow: true)
                .padding(.all, 16)
            case .success(let isAccepted, let termsAndConditions):
                HStack {
                    ChargeInfoSimpleTile(
                        icon: ChargeInfoImages.Icons.lightingBolt,
                        // Replaced this values temporaryly for consent and rolling in unifying
                        text: "Charging Stations",
                        // text: Strings.ChargeInfo.ionna,
                        description: getDescription(),
                        // description: "",
                        buttonTitle: stateNotifier.getIonnaButtonTitle(
                            acknowledged: isAccepted
                        ),
                        registered: isAccepted == true
                    ) {
                        if let termsAndConditions, !(isAccepted ?? false) {
                            stateNotifier.navigateToIonnaConsentScreen(termsAndConditions: termsAndConditions)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
        }
        .task {
            await stateNotifier.fetchConsentDetails(for: .tcn)
        }
    }

    @ViewBuilder
    func teslaTileContent() -> some View {
        VStack {
            switch state.teslaConsentDetails {
            case .notAvailable:
                EmptyView()
            case .loading:
                OALoadingView(height: 60,
                              cornerRadius: 10,
                              showShadow: true)
                .padding(.all, 16)
            case .success(let isAccepted, let termsAndConditions):
                HStack {
                    TeslaTile(
                        icon: ChargeInfoImages.Icons.lightingBolt,
                        title: Strings.ChargeInfo.tesla,
                        registerbuttonLabel: stateNotifier.getIonnaButtonTitle(
                            acknowledged: isAccepted
                        ), // Strings.EvPartnerRegistration.register,
                        statusLabel: Strings.ChargeInfo.chargePointRegistered,
                        registered: isAccepted ?? false,
                        action: {
                            if isAccepted ?? false {
                                guard let termsAndConditions else { return }
                                stateNotifier.showConsentScreen(termsAndConditions: termsAndConditions)
                            }
                        }
                    )
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
            }
        }
        .task {
            await stateNotifier.fetchConsentDetails(for: .tcn)
        }
    }

    func getDescription() -> String {
        return [
            state.isEvIonna ? "Ionna" : nil,
            state.isEvTesla ? "Tesla" : nil
        ]
        .compactMap { $0 }
        .joined(separator: ", ")
    }

    @ViewBuilder
    var walletFound: some View {
        ChargeInfoSimpleTile(icon: ChargeInfoImages.Icons.wallet,
                             text: Strings.Pay.wallet, description: "",
                             last4ofWallet: state.walletLast4) {
            stateNotifier.navigateToWallet(isSetupWallet: false)
        }
    }

    @ViewBuilder
    var walletNotFound: some View {
        ChargeInfoSimpleTile(icon: ChargeInfoImages.Icons.wallet,
                             text: Strings.Pay.wallet, description: "",
                             buttonTitle: Strings.Pay.walletSetupText) {
            stateNotifier.navigateToWallet(isSetupWallet: true)
        }
    }

    @ViewBuilder
    var contentTitle: some View {
        HStack {
            OATextView.headline1(Strings.ChargeInfo.chargeSettingsText)
            Spacer()
        }.padding(16)
    }

    @ViewBuilder
    func walletConditions(_ enrollment: EVEnrollmentCheck) -> some View {
        if enrollment.payload?.partnerEnrollment?.wallet == "Found" {
            walletFound
        } else {
            walletNotFound
        }
    }

    @ViewBuilder
    func enrollmentContent(_ enrollment: EVEnrollmentCheck) -> some View {
        VStack {
            if state.showWallet {
                walletConditions(enrollment)
            }
            if state.isPublicChargingControlAllowed {
                chargePointContent(enrollment)
            }
            if state.showEvGo {
                evgoContent(enrollment)
            }
            if state.isEvTesla || state.isEvIonna {
                chargingStationsContent()
            }
        }
        .padding(.bottom, 16)
    }
}
