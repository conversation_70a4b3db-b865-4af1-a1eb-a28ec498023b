// Copyright © 2024 Toyota. All rights reserved.

import SwiftUI
import Components
import Theme
import LocalizedStrings

// swiftlint:disable function_parameter_count

extension FindStationsPlusView {
    func handleDirections(latitude: Double,
                          longitude: Double,
                          destinationAddress: String?) {
        stateNotifier.getDirections(latitude: latitude,
                                    longitude: longitude,
                                    destinationAddress: destinationAddress)
    }

    func stationsListContent(station: EvStation, myDestination: Bool) -> some View {
        let lat = Double(station.evEvses.first?.coordinates?.evLatitude ?? "") ?? 0.00
        let lon = Double(station.evEvses.first?.coordinates?.evLongitude ?? "") ?? 0.00
        let evSource = station.evOperator ?? station.formattedEvSource()
        let displayName = station.trimmedTitle
        let nameToShow = !station.isTitleEmpty ? displayName : nil
        let isPartnerOverride = station.evIsPartner

        return stationRow(name: nameToShow ?? "",
                          showAddress: !station.isAddressEmpty,
                   distance: stateNotifier.distanceBetweenLocations(
                       lat: station.evLatitude ?? 0.0,
                       lon: station.evLongitude ?? 0.0) ?? "",
                   address: station.trimmedAddressWithCity,
                   availability: isPartnerOverride ? station
            .evOpeningTimes?.evTiming ?? "" : "",
                          plugAvailability: station.evConnectorSum?.getPlugAvailabilityArray() ?? [],
                          isPartner: isPartnerOverride,
                          evSource: evSource,
                          isFavorite: station.isFavourite,
                   placeId: station.evPlaceId ?? "",
                          lat: lat, lon: lon,
                          myDestination: myDestination,
                          location: station.getLocation())
        .onTapGesture {
            handleStationTap(station)
        }
    }

    @ViewBuilder
    public var stationsList: some View {
        switch state.stations {
        case .initial:
            EmptyView()
        case .loading:
            VStack {
                Spacer()
                ProgressView()
                Spacer()
            }
        case .success(let nearestFuelStations, let myDestination):
            stationsListSuccessView(nearestFuelStations: nearestFuelStations, myDestination: myDestination)
        }
    }

    @ViewBuilder
    private func stationCountView(count: Int) -> some View {
        HStack {
            if case .success(let fuel) = state.fuelState {
                if case .ev = fuel {
                    OATextView.body04Tertiary03(Strings.FindStations.chargeStations)
                } else if case .phev = fuel {
                    OATextView.body04Tertiary03(Strings.FindStations.chargeStations)
                } else {
                    OATextView.body04Tertiary03(Strings.FindStations.fuelStations)
                }
            }
            Spacer()
            OATextView.callout01Tertiary05("\(count) \(Strings.FindStations.countNearBy)")
        }.padding(16)
    }

    private func loadMoreStations() {
        stateNotifier.nextPage()
         Task {
             await handleFilters()
         }
    }

    @ViewBuilder
    private func stationsListSuccessView(nearestFuelStations: FindEvStationsListResponse,
                                         myDestination: Bool) -> some View {
        if nearestFuelStations.evStations.isEmpty {
            OATextView.callout01Tertiary05(Strings.FindStations.noChargeStation)
                .padding()
        } else {
            let filteredStations = nearestFuelStations.evStations.filter { station in
                if showFavorites {
                    return station.isFavourite
                }
                return !station.evEvSource.isEmpty
            }

            if showFavorites && filteredStations.isEmpty {
                OATextView.callout01Tertiary05(Strings.FindStations.noFavoriteStation)
                    .padding()
            } else {
                stationCountView(count: filteredStations.count)
                List {
                    ForEach(filteredStations, id: \.evId) { station in
                        stationsListContent(station: station, myDestination: myDestination)
                            .onAppear {
                                // filteredStation greater than 10 so that
                                // if the station at bottom is visible on first
                                // load no infinate loading loop will occur.
                                if filteredStations.count >= 10 &&
                                    station == filteredStations.last {
                                    loadMoreStations()
                                }
                            }
                    }
                }
                .listStyle(PlainListStyle())
                .background(AppTheme.colors.tertiary15)
            }
        }
        Spacer()
    }

    @ViewBuilder
    func stationRowTop(name: String, evSource: String, distance: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                OATextView.body04Tertiary03(evSource)
                Spacer()
                OATextView.callout01Tertiary05(distance)
            }
            if stateNotifier.shouldDisplayAdaptorDisclaimer(for: evSource) {
                OATextView.callout01Tertiary05(
                    Strings.EvTeslaStations.adaptorMayBeNeededWarning
                )
                .foregroundColor(.secondary)
            }

            OATextView.callout01Tertiary05(name)
        }
    }

    @ViewBuilder
    private func availablePlugsContainerView(_ plugAvailability: [[String]]) -> some View {
        let filteredPlugs = plugAvailability.filter {
            let count = $0.last ?? ""
            return count.isNotEmpty && count != "0/0"
        }
        ScrollView(.horizontal) {
            HStack {
                ForEach(filteredPlugs, id: \.first) { plug in
                    AvailablePlugsView(level: plug.first ?? "",
                                       count: plug.last ?? "")
                }
            }
        }
    }

    @ViewBuilder
    func stationRowBottom(
        showAddress: Bool,
        address: String,
        availability: String,
        plugAvailability: [[String]],
        isPartner: Bool,
        evSource: String,
        isFavorite: Bool,
        placeId: String,
        lat: Double,
        lon: Double,
        myDestination: Bool,
        location: String
    ) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                if showAddress {
                    OATextView.callout01Tertiary05(address)
                }
                AvailabilityView(availability: availability)
                HStack {
                    let isPartnerOverride = isPartner
                    if isPartnerOverride {
                        availablePlugsContainerView(plugAvailability)
                    } else {
                        OATextView.callout01Tertiary07(Strings.FindStations.nonPartnerStationHeading)
                    }
                    Spacer()
                    actionsGroup(placeId: placeId,
                                 lat: lat,
                                 lon: lon,
                                 address: address,
                                 isFavorite: isFavorite,
                                 myDestination: myDestination,
                                 location: location)
                }
                .padding(.top, 16)
            }
        }
    }

    @ViewBuilder
    private func actionsGroup(placeId: String,
                              lat: Double,
                              lon: Double,
                              address: String,
                              isFavorite: Bool,
                              myDestination: Bool,
                              location: String) -> some View {
        HStack(spacing: 30) {
            createIconView(icon: ChargeInfoImages.Icons.sendToCar) {
                handleSendToCarAction(location)
            }
            createIconView(icon: ChargeInfoImages.Icons.findDirections) {
                handleDirections(latitude: lat, longitude: lon, destinationAddress: address)
            }
            if myDestination && placeId.isNotEmpty {
                FavoritesIconView(isFavorite: isFavorite)
                    .onTapGesture {
                        stateNotifier.toggleFavorites(placeId: placeId,
                                                      isFavorite: isFavorite,
                                                      isShowFavorites: showFavorites)
                    }
            } else {
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: 24, height: 24)
            }
        }
    }

    private func createIconView(icon: Image, action: @escaping () -> Void) -> some View {
        icon
            .applyColor(AppTheme.colors.tertiary03)
            .frame(width: 24, height: 24)
            .onTapGesture(perform: action)
    }

    @ViewBuilder
    func stationRow(name: String,
                    showAddress: Bool,
                    distance: String,
                    address: String,
                    availability: String,
                    plugAvailability: [[String]],
                    isPartner: Bool,
                    evSource: String,
                    isFavorite: Bool,
                    placeId: String,
                    lat: Double,
                    lon: Double,
                    myDestination: Bool,
                    location: String) -> some View {
        VStack {
            stationRowTop(name: name, evSource: evSource, distance: distance)
            stationRowBottom(showAddress: showAddress,
                             address: address,
                             availability: availability,
                             plugAvailability: plugAvailability,
                             isPartner: isPartner,
                             evSource: evSource,
                             isFavorite: isFavorite,
                             placeId: placeId,
                             lat: lat,
                             lon: lon,
                             myDestination: myDestination,
                             location: location)
        }
    }

    func handleFilters() async {
        await stateNotifier.handleFilters(
            selectedPartners: selectedPartners,
            selectedPlugTypes: selectedPlugTypes)
    }
}
