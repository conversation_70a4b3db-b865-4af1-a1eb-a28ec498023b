// Copyright © 2024 Toyota. All rights reserved.

import Theme
import SwiftUI
import Components
import Navigation
import VehicleFeature
import NetworkClients
import LocalizedStrings
import AccountSettingsFeature

internal struct ChargeInfoSimpleTile: View {
    var icon: Image
    var text: String
    var description: String
    var boldDescription = false
    var buttonTitle: String?
    var registered: Bool? = false
    var last4ofWallet: String?
    var showChevron: Bool?
    var action: (() -> Void)?

    public init(icon: Image,
                text: String,
                description: String,
                boldDescription: Bool? = false,
                buttonTitle: String? = nil,
                registered: Bool? = false,
                last4ofWallet: String? = nil,
                showChevron: Bool? = false,
                action: (() -> Void)? = nil) {
        self.icon = icon
        self.text = text
        self.description = description
        self.boldDescription = boldDescription ?? false
        self.buttonTitle = buttonTitle
        self.registered = registered
        self.last4ofWallet = last4ofWallet
        self.action = action
    }

    @ViewBuilder
    var textView: some View {
        HStack {
            if boldDescription {
                OATextView.subheadline01(text)
            } else {
                OATextView.body04Tertiary03(text)
            }
            Spacer()
        }.padding(.top, 2)
    }

    @ViewBuilder
    var descriptionView: some View {
        HStack {
            Group {
                if boldDescription {
                    OATextView.footnoteTertiary03(description)
                } else {
                    OATextView.footnoteTertiary05(description)
                }
            }
            .padding(.bottom)
            Spacer()
        }
    }

    @ViewBuilder
    var iconView: some View {
        icon
            .applyColor(AppTheme.colors.tertiary03)
            .frame(width: 24, height: 24)
    }

    @ViewBuilder
    var rightSideView: some View {
        if let last4ofWallet = last4ofWallet,
           last4ofWallet.isNotEmpty {
            OATextView.callout01Tertiary03("••••\(last4ofWallet)")
                .padding(.vertical, 30)
        } else if let showChevron = showChevron,
               showChevron == true {
            ChargeInfoImages.Icons.rightArrow
                .frame(width: 24, height: 24)
        } else {
            borderButton()
        }
    }

    var body: some View {
        OATileView1(innerPadding: 0.0, content: {
            VStack {
                HStack {
                    iconView
                    VStack {
                        textView
                        if description.isNotEmpty {
                            descriptionView
                        }
                    }
                    .padding(.leading, 8)
                    .padding(.trailing, 8)
                    rightSideView
                }.onTapGesture {
                    if let action = action {
                        action()
                    }
                }
            }.padding(.horizontal, 16)
        }).padding(.horizontal, 16)
    }

    func borderButton() -> some View {
        return Group {
            if let buttonTitle = buttonTitle, let registered, !registered {
                buttonTitleView(buttonTitle: buttonTitle)
                    .onTapGesture {
                        if let action = action {
                            action()
                        }
                    }
            } else if
                itIsEligibleForRegistration(text: text, registered: registered) {
                HStack {
                    registeredView
                        .padding(.vertical, 30)
                }
            } else {
                EmptyView()
            }
        }
    }

    func itIsEligibleForRegistration(text: String, registered: Bool?) -> Bool {
        let reg = registered ?? false
        switch text {
        case Strings.ChargeInfo.chargePoint,
            Strings.ChargeInfo.ionna,
            Strings.ChargeInfo.tesla,
            Strings.EvWallet.chargingStationsTitle:
            return true && reg

        default: return false && reg
        }
    }

    @ViewBuilder
    private func buttonTitleView(buttonTitle: String) -> some View {
        Group {
            OATextView.buttonLinkButton02a(buttonTitle)
                .padding(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(AppTheme.colors.button02c, lineWidth: 3)
                )
        }
        .padding(.vertical, 16)
    }

    @ViewBuilder
    private var registeredView: some View {
        OATextView.callout01Tertiary03(Strings.ChargeInfo.chargePointRegistered)
        ChargeInfoImages.Icons.checkmark
            .resizable()
            .scaledToFit()
            .frame(width: 16, height: 16)
            .foregroundColor(AppTheme.colors.button03b)
    }
}
