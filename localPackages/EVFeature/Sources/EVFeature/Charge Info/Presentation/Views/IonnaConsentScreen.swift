// Copyright © 2024 Toyota. All rights reserved.

import SwiftUI
import Navigation
import Components
import Theme
import LocalizedStrings

public struct IonnaConsentScreen: View {

    @ObservedObject var stateNotifier: ChargeInfoStateNotifier
    let termsAndConditions: String
    private var parsedSections: [ContentSection] = []

    public init(stateNotifier: ChargeInfoStateNotifier, termsAndConditions: String) {
        self.stateNotifier = stateNotifier
        self.termsAndConditions = termsAndConditions
        self.parsedSections = self.parseContentSections(termsAndConditions)
    }

    public var body: some View {
        OAScreen {
            VStack {
                navigationBar
                ScrollView(.vertical, showsIndicators: true) {
                    VStack(alignment: .leading, spacing: 24) {
                        Text("Ionna Service Guidelines")
                            .font(AppTheme.fontStyles.headline1)
                            .foregroundColor(AppTheme.colors.tertiary03)
                            .padding(.bottom, 8)
                        contentSections()
                    }
                    .padding(.vertical, 32)
                    .padding(.horizontal, 16)
                    .accessibilityIdentifier(OAAccessibilityIdentifiers.ionnaTermsBody)
                    enrollmentFooterView
                }
                .padding(.all)
            }
            .frame(maxHeight: .infinity, alignment: .top)
        }
    }

    @ViewBuilder
    private var enrollmentFooterView: some View {
        VStack {
            OAButtonView.borderless(Strings.Common.declineBt, color: AppTheme.colors.tertiary12) {
                Task {
                    await stateNotifier.acceptIonnaTerms(false)
                }
            }
            .accessibilityIdentifier(OAAccessibilityIdentifiers.back)
            OAButtonView.primary(Strings.Common.acceptBt) {
                Task {
                    await stateNotifier.acceptIonnaTerms(true)
                }
            }
            .accessibilityIdentifier(OAAccessibilityIdentifiers.back)
            .padding(.horizontal, 96)
            .padding(.bottom)
        }
    }

    @ViewBuilder
    private var navigationBar: some View {
        ZStack {
            HStack {
                OACircleIconView(image: IconImages.Navigation.backArrow)
                    .onTapGesture {
                        navigationContainer.navigationUseCases.popView()
                    }
                    .accessibilityIdentifier(OAAccessibilityIdentifiers.back)
                    .accessibilityLabel(OAaccessibilityLabels.back)
                    .accessibilityAddTraits(.isButton)
                    .accessibilityRemoveTraits(.isImage)
                Spacer()
            }
            OATextView.subheadline03("Charging Stations"/*Strings.ChargeInfo.chargingStations*/)
            // OATextView.subheadline03(Strings.ChargeInfo.ionna.uppercased())
                .foregroundColor(AppTheme.colors.tertiary03)
                .frame(maxWidth: .infinity, alignment: .center)
        }
    }

    @ViewBuilder
    private func contentSections() -> some View {
        ForEach(parsedSections, id: \.title) { section in
            contentSection(
                title: section.title,
                content: section.content
            )
        }
    }

    private func contentSection(title: String, content: AttributedString) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(title)
                .font(AppTheme.fontStyles.subheadline)
                .foregroundColor(AppTheme.colors.tertiary03)

            Text(content)
                .font(AppTheme.fontStyles.callout01Regular)
                .foregroundColor(AppTheme.colors.tertiary05)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.bottom, 24)
    }

    private struct ContentSection {
        let title: String
        let content: AttributedString
    }

    private func parseContentSections(_ content: String) -> [ContentSection] {
        guard let cleanContent = try? NSAttributedString(
            data: content.data(using: .utf8) ?? Data(),
            options: [
                .documentType: NSAttributedString.DocumentType.html,
                .characterEncoding: String.Encoding.utf8.rawValue
            ],
            documentAttributes: nil
        ).string else {
            return []
        }

        let sectionTitles = [
            "Description",
            "Access & Services",
            "Network Details",
            "Payment",
            "Disputes + Support"
        ]

        var sections: [ContentSection] = []

        for title in sectionTitles {
            if let range = cleanContent.range(of: title) {
                let afterTitle = cleanContent[range.upperBound...]
                var sectionContent = ""

                if let nextSection = sectionTitles.first(where: {
                    afterTitle.contains($0) && $0 != title
                }),
                let nextRange = afterTitle.range(of: nextSection) {
                    sectionContent = String(afterTitle[..<nextRange.lowerBound])
                } else {
                    sectionContent = String(afterTitle)
                }

                // Remove duplicate header
                sectionContent = sectionContent
                    .replacingOccurrences(of: "Ionna Service Guidelines", with: "")
                    .trimmingCharacters(in: .whitespacesAndNewlines)

                if let attributed = makeParagraph(from: sectionContent) {
                    sections.append(ContentSection(title: title, content: attributed))
                }
            }
        }

        return sections
    }

    private func makeParagraph(from paragraph: String) -> AttributedString? {
        guard let data = paragraph.data(using: .utf8) else {
            return nil
        }

        let options: [NSAttributedString.DocumentReadingOptionKey: Any] = [
            .documentType: NSAttributedString.DocumentType.html,
            .characterEncoding: String.Encoding.utf8.rawValue
        ]

        guard let attributedString = try? NSAttributedString(
            data: data,
            options: options,
            documentAttributes: nil
        ) else {
            return nil
        }

        var descriptionString = AttributedString(attributedString)
        descriptionString.font = AppTheme.fontStyles.callout01Regular
        descriptionString.foregroundColor = AppTheme.colors.tertiary05

        for run in descriptionString.runs where run.link != nil {
            descriptionString[run.range].underlineStyle = .single
            descriptionString[run.range].foregroundColor = AppTheme.colors.tertiary03
        }

        return descriptionString
    }

    private enum OAaccessibilityLabels {
        static let back = "Back"
    }

    private enum OAAccessibilityIdentifiers {

        static let back = "ionna_consent_back_button"
        static let acceptButton = "ionna_consent_accept_button"
        static let declineButton = "ionna_consent_decline_button"
        static let ionnaTermsBody = "ionna_consent_terms_body"
    }
}

extension IonnaConsentScreen: NavigableView {
    public var viewIdentifier: NavigableViewID {
        .ionnaConsentScreen
    }
}
