// Copyright © 2024 Toyota. All rights reserved.

import Combine
import SwiftUI
import Utility
import VehicleFeature
import NativeLinker

public class ChargeInfoStateNotifier: ObservableObject {
    @MainActor @Published var state = ChargeInfoState.initial

    private let useCases: ChargeInfoUseCases
    private let selectedVehicleUseCases: SelectedVehicleUseCases
    private var cancellable: [AnyCancellable?] = []
    private var vehicle: Vehicle?

    private func setupState(_ newState: Published<ChargeInfoState>.Publisher.Output) async {
        await MainActor.run { [weak self] in
            self?.state = newState
        }
    }

    init(useCases: ChargeInfoUseCases,
         selectedVehicleUseCases: SelectedVehicleUseCases) {
        self.useCases = useCases
        self.selectedVehicleUseCases = selectedVehicleUseCases
        let useCasesCancellable = useCases.state.sink { newState in
            Task {
                await self.setupState(newState)
            }
        }
        cancellable.append(useCasesCancellable)
        let selectedVehicleCancellable = selectedVehicleUseCases.state.sink(receiveValue: { [weak self] vehicle in
            self?.vehicle = vehicle
        })
        cancellable.append(selectedVehicleCancellable)
    }

    func load(polling: Bool = false) async {
        await useCases.load(polling: polling)
    }
    func fetchChargeHistory(startDate: String? = nil,
                            endDate: String? = nil,
                            chargingType: String? = "") async {
        await useCases.fetchChargeHistory(startDate: startDate,
                                          endDate: endDate,
                                          chargingType: chargingType ?? "")
    }

    func navigateToChargeHistory() {
        useCases.navigateToChargeHistory()
    }

    func navigateToChargeSchedule() {
        useCases.navigateToChargeSchedule()
    }

    func navigateToManualSchedule() {
        useCases.navigateToManualSchedule()
    }

    func navigateToChargeAssistScheduleScreen() {
        useCases.navigateToChargeAssistScheduleScreen()
    }

    func isEVGoExpired(_ res: EVPartnerStatus) -> Bool? {
        useCases.isEVGoExpired(res)
    }

    func isEVGoExpiringSoon(_ res: EVPartnerStatus) -> Bool? {
        useCases.isEVGoExpiringSoon(res)
    }
    func progressColor(_ percent: Int, isPHEV: Bool) -> Color {
        useCases.progressColor(percent, isPHEV: isPHEV)
    }

    func navigateToStatistics() {
        useCases.navigateToStatistics()
    }

    func navigateToCleanAssistDetailPage() {
        useCases.navigateToCleanAssistDetailPage()
    }

    func fetchCleanAssistDetails() async {
        await useCases.fetchCleanAssistDetails()
    }

    func acceptCleanAssistTerms(_ userAccepted: Bool) async {
        await useCases.acceptCleanAssistTerms(userAccepted)
    }

    func navigateToRegisterPartnerScreen(partner: String) {
        useCases.navigateToRegisterPartnerScreen(partner: partner)
    }

    func fetchLCFSDashboard() async {
        await useCases.fetchLCFSDashboard()
    }

    func getBrandName() -> String {
        useCases.getBrandName()
    }

    func dismissPopup() {
        useCases.dismissPopup()
    }

    func navigateToWallet(isSetupWallet: Bool) {
        useCases.navigateToWallet(isSetupWallet: isSetupWallet)
    }

    func showEvgoComplimentaryPopup(expiryDays: Int, walletSetup: Bool) {
        useCases.showEvgoComplimentaryPopup(expiryDays: expiryDays, walletSetup: walletSetup)
    }

    func getLastUpdatedDate() -> String? {
        useCases.getLastUpdatedDate()
    }

    func navigateToCreateScheduleScreen(isDeparture: Bool) {
        useCases.navigateToCreateScheduleScreen(isDeparture: isDeparture)
    }

    func navigateToCreateModifyScheduleScreen() {
        useCases.navigateToCreateModifyScheduleScreen()
    }
    func fetchStatistics(reportType: String = "year_week", month: String? = nil) async {
        await useCases.fetchStatistics(reportType: reportType, month: month)
    }

    func logStatisticsFilterTap() {
        useCases.logStatisticsFilterTap()
    }
    func logStatisticsLearnMore() {
        useCases.logStatisticsLearnMore()
    }

    func isChargeAssistAvailable() -> Bool {
        guard let vehicle = self.vehicle else { return false }
        return vehicle.isFeatureEnabled(.chargeAssist) || nativeLinkerContainer.isChargeAssistFeatureEnabled()
    }

    func navigateToFindStationsScreen() {
        useCases.navigateToFindStationsScreen()
    }

    func getWattTimeUrl() -> String {
        useCases.getWattTimeUrl()
    }

    func fetchIonnaConsentDetails() async {
        await useCases.fetchIonnaConsentDetails()
    }

    func fetchConsentDetails(for provider: CPOProvider) async {
        await useCases.fetchConsents(for: provider)
    }

    func acceptIonnaTerms(_ userAccepted: Bool) async {
        await useCases.acceptIonnaTerms(userAccepted)
    }

    func navigateToIonnaConsentScreen(termsAndConditions: String) {
        useCases.navigateToIonnaConsentScreen(termsAndConditions: termsAndConditions)
    }

    func showConsentScreen(termsAndConditions: String) {
        useCases.navigateToIonnaConsentScreen(termsAndConditions: termsAndConditions)
    }

    func getIonnaButtonTitle(acknowledged: Bool?) -> String {
        useCases.getIonnaButtonTitle(acknowledged: acknowledged)
    }
}
