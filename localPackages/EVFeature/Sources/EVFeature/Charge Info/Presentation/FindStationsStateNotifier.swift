// Copyright © 2024 Toyota. All rights reserved.

import Foundation
import Combine
import SwiftUI
import Utility

public class FindStationsStateNotifier: ObservableObject {
    @MainActor @Published var state = FindStationsState.initial

    private let useCases: FindStationsUseCase
    private var cancelState: AnyCancellable?

    private func setupState(_ newState: Published<FindStationsState>.Publisher.Output) async {
        await MainActor.run { [weak self] in
            self?.state = newState
        }
    }

    init(useCases: FindStationsUseCase) {
        self.useCases = useCases
        self.cancelState = useCases.state.sink { newState in
            Task {
                await self.setupState(newState)
            }
        }
    }

    func load() async {
        await useCases.load()
    }

    func navigateToRegisterPartnerScreen(partner: String) {
        useCases.navigateToRegisterPartnerScreen(partner: partner)
    }

    func getBrandName() -> String {
        useCases.getBrandName()
    }

    func dismissPopup() {
        useCases.dismissPopup()
    }

    func navigateToWallet(isSetupWallet: Bool) {
        useCases.navigateToWallet(isSetupWallet: isSetupWallet)
    }

    func showEvgoComplimentaryPopup(expiryDays: Int, walletSetup: Bool) {
        useCases.showEvgoComplimentaryPopup(expiryDays: expiryDays, walletSetup: walletSetup)
    }

    func search(lat: String?, lon: String?) {
        useCases.search(lat: lat, lon: lon)
    }

    func getStationsCount(level1: String?, level2: String?, dcFast: String?) -> String {
        useCases.getStationsCount(level1: level1, level2: level2, dcFast: dcFast)
    }

    func distanceBetweenLocations(lat: Double, lon: Double) -> String? {
        useCases.distanceBetweenLocations(lat: lat, lon: lon)
    }
    func fetchMapPinAnnotations() {
        useCases.fetchMapPinAnnotations()
    }

    func callEVStationButton(station: EvStation, screen: EVScreen) {
        useCases.callEVStationButton(station: station, screen: screen)
    }

    func getEVStationStatusTitle(station: EvStation) async {
        await useCases.getEVStationStatusTitle(station: station)
    }
    func getPartnersList() -> [String] {
        useCases.getPartnersList()
    }
    func handleFilters(selectedPartners: [String],
                       selectedPlugTypes: [String]) async {
        await useCases.handleFilters(selectedPartners: selectedPartners,
                                     selectedPlugTypes: selectedPlugTypes)
    }

    func toggleFavorites(placeId: String, isFavorite: Bool, isShowFavorites: Bool) {
        useCases.toggleFavorites(placeId: placeId, isFavorite: isFavorite, isShowFavorites: isShowFavorites)
    }

    func getDirections(latitude: Double, longitude: Double, destinationAddress: String?) {
        useCases.getDirections(latitude: latitude, longitude: longitude, destinationAddress: destinationAddress)
    }

    func sendToCar(address: String) async -> Bool {
        await useCases.sendToCar(address: address)
    }

    func oopenWebSite(url: String) {
        useCases.oopenWebSite(url: url)
    }

    func callStation(number: String) {
        useCases.callStation(number: number)
    }

    func focusMap(to location: LocationToCenter) {
        useCases.focusMap(to: location)
    }

    func nextPage() {
        useCases.nextPage()
    }

    /// You MUST reset the page count
    /// if filters change or location changes
    /// otherwise results get appended to last location or query.
    func resetPageCount() {
        useCases.resetPageCount()
    }

    func startChargingWithConnector(station: EvStation, evseUid: String, connectorId: String) {
        useCases.startChargingWithConnector(station: station, evseUid: evseUid, connectorId: connectorId)
    }

    func updateConnectorSelectionVisibility(show: Bool) {
        useCases.updateConnectorSelectionVisibility(show: show)
    }

    func removeAllPartnerFilter(partner: String?) {
        useCases.removeAllPartnerFilter(partner: partner)
    }
    func removeAllPlugtypeFilter(plugType: String?) {
        useCases.removeAllPlugtypeFilter(plugType: plugType)
    }
    func appendPartnerFilter(partner: String) {
        useCases.appendPartnerFilter(partner: partner)
    }
    func appendPlugtypeFilter(plugType: String) {
        useCases.appendPlugtypeFilter(plugType: plugType)
    }

    func isMyDestinationEnabled() -> Bool {
        useCases.isMyDestinationEnabled()
    }

    func fetchIonnaConsent() async {
        await useCases.fetchIonnaConsentDetails()
    }

    func navigateToIonnaConsent(termsAndConditions: String) {
        useCases.navigateToIonnaConsentScreen(termsAndConditions: termsAndConditions)
    }

    func shouldShowRegisterButton(for station: EvStation) -> Bool {
        useCases.shouldDisplayRegisterButton(for: station)
    }

    func shouldDisplayAdaptorDisclaimer(for evSource: String) -> Bool {
        useCases.shouldDisplayAdaptorDisclaimer(for: evSource)
    }
}
