// Copyright © 2025 Toyota. All rights reserved.
// swiftlint:disable line_length

import XCTest
import Combine
import UIKit
@testable import EVFeature
@testable import DashboardFeature
@testable import VehicleFeature
@testable import Analytics
@testable import Navigation
@testable import Utility
@testable import NetworkClients
@testable import NativeLinker

struct FindStationsRequestProviderMock: FindStationsRequestProvider {
    // swiftlint:disable function_parameter_count
    func getHydrogenStations(brand: String, fuelType: String, radius: String, zip: String, region: String, latitude: String, longitude: String) async -> Result<NearestFuelStationsResponse, OneapiError> {
        return .success(.init(results: NearestFuelStationsResults(totalResults: 0, ELEC: 0), fuelStations: []))
    }

    // swiftlint:disable function_parameter_count
    func getStationsList(brand: String, fuelType: String, zip: String, region: String, latitude: String, longitude: String, vin: String, requestBody: StationsQuery? = nil) async -> Result<FindStationsListResponse, OneapiError> {
        return .success(.init(totalNoOfRecords: 0, stations: []))
    }
}

struct AnnouncementsRequestProviderMock: AnnouncementsRequestProvider {
    func checkCleanAssistEligibility(vin: String, brand: String) async -> Result<LCFSEligibityPayload, OneapiError> {
        return .success(.init())
    }

    func fetchMarketingBanners(vin: String, location: String, region: VehicleResponse.Region, generation: String) async -> Result<MarketingBannerPayload, OneapiError> {
        return .success(.init(cards: []))
    }

    func fetchWifiReminder(vin: String, generation: String, attToken: String, xBrand: String) async -> Result<WifiReminderPayload, OneapiError> {
        return .success(.init(reminderCardDetails: .init()))
    }

    func fetchMarketingConsent(params: ConsentParams) async -> Result<MarketingConsentPayload, OneapiError> {
        return .success(.init(acknowledgedConsents: []))
    }

    func postAcceptConsent(generation: String, appBrand: String, body: CustomerConsentAcceptBody) async -> Result<AcceptConsentResponse, OneapiError> {
        return .success(.init(status: .init(messages: [])))
    }

    func wifiAcknowledgement(vin: String, generation: String, brand: String) async -> Result<Bool, OneapiError> {
        return .success(true)
    }

    func fetchEVSwap(vin: String) async -> Result<[EVSwapPayload], OneapiError> {
        return .success([])
    }

    func fetchEligibility(vin: String, brand: String) async -> Result<LCFSEligibityPayload, OneapiError> {
        return .success(LCFSEligibityPayload())
    }
}

struct WalletRequestProviderMock: WalletRequestProvider {
    func fetchEvWallet() async -> Result<WalletInfoResponse, OneapiError> {
        return .success(WalletInfoResponse(paymentMethods: []))
    }

    func fetchTransactions(last4: String?, expiry: String?) async -> Result<WalletTransactionsResponse, OneapiError> {
        return .success(WalletTransactionsResponse(transactions: []))
    }

    func fetchWalletConfig() async -> Result<WalletConfigResponse, OneapiError> {
        return .success(WalletConfigResponse(channelConfig: .none, images: .none))
    }

    func postWalletSetupIntent() async -> Result<WalletSetupIntentResponse, OneapiError> {
        return .success(WalletSetupIntentResponse(setupIntent: .none))
    }

    func deleteCard(paymentMethodId: String) async -> Result<String, OneapiError> {
        return .success("")
    }

    func postDefaultCard(paymentMethodId: String) async -> Result<DefaultCardResponse, OneapiError> {
        return .success(
            .init(
                paymentMethod: .init(
                    id: "",
                    isDefault: false,
                    type: "",
                    card: .none,
                    createdAt: Date.now,
                    updatedAt: Date.now,
                    deleted: false
                )
            )
        )
    }

    func postWalletAutoRecharge(body: Encodable?) async -> Result<DefaultCardResponse, OneapiError> {
        return .success(.init(paymentMethod: .init(
            id: "",
            isDefault: false,
            type: "",
            card: .none,
            createdAt: Date.now,
            updatedAt: Date.now,
            deleted: false
        )))
    }

    func postLoadFunds(body: Encodable?) async -> Result<WalletSetupIntentResponse, OneapiError> {
        return .success(.init(setupIntent: .none))
    }
}

struct PublicChargingRequestProviderMock: PublicChargingRequestProvider {
    func startCharging(make: String, vin: String, body: Encodable?) async -> Result<StartChargeResponse, OneapiError> {
        return .success(.init(startChargeResponse: .init(chargingId: "String", createdAt: "String", status: "String")))
    }

    func stopCharging(make: String, vin: String, region: String, body: Encodable?) async -> Result<ChargeSessionResponse, OneapiError> {
        return .success(.init(session: .none))
    }

    func chargeSession(make: String, vin: String) async -> Result<ChargeSessionResponse, OneapiError> {
        return .success(.init(session: .none))
    }

    func requestChargeCommand(generation: String, vin: String, brand: String) async -> Result<PostRealTimeStatusResponse, OneapiError> {
        return .success(.init(payload: .none, status: .none, timestamp: ""))
    }
}

class NavigationUseCasesMock: NavigationUseCases {
    func resetRoot(withViews views: [some NavigableView], animate: Bool) {}
    func navigateBack() {}
    func dismissToRoot() {}
    func dismissPresented() {}
    func pushPresentedViewController(viewController: UIViewController) {}
    func presentViewController(viewController: UIViewController) {}
    func rootViewController() -> UIViewController? { return nil }

    // NavigationUseCases protocol methods
    func pushView(_ view: some NavigableView, animate: Bool) {}
    func pushViewController(_ vc: NavigableViewController, animate: Bool) {}
    func pushViewControllerWithAnimation(_ vc: NavigableViewController, animate: Bool, animationType: PageAnimationType) {}

    func presentView(_ view: some NavigableView, presentationStyle: PresentationStyle, animate: Bool, detent: UISheetPresentationController.Detent) {}
    func presentViewController(_ vc: NavigableViewController, presentationStyle: PresentationStyle, animate: Bool, detent: UISheetPresentationController.Detent) {}

    @discardableResult
    func pushView(_ view: some NavigableView, from parentIdentifer: NavigableViewID, animate: Bool) -> Bool { return true }

    @discardableResult
    func pushViewController(_ vc: NavigableViewController, from parentIdentifer: NavigableViewID, animate: Bool) -> Bool { return true }

    func replaceLastView(with view: some NavigableView, animate: Bool) {}
    func replaceLastViewController(with vc: NavigableViewController, animate: Bool) {}

    @discardableResult
    func popView(_ identifier: NavigableViewID, animate: Bool) -> Bool { return true }

    @discardableResult
    func popToView(_ identifier: NavigableViewID, animate: Bool) -> Bool { return true }

    func popView(animate: Bool) {}
    func popToRoot(animate: Bool) {}

    func pushView(view: some NavigableView, fromRootView rootView: some NavigableView, animate: Bool) {}
    func pushViewController(_ vc: NavigableViewController, fromRootVC rootViewVC: NavigableViewController, animate: Bool) {}

    func resetRoot(withViews views: [any NavigableView], animate: Bool) {}
    func resetRoot(withViewControllers viewControllers: [NavigableViewController], animate: Bool) {}

    func pushToNativeNavigation(_ navigation: NativeNavigation) {}
    func isCurrentScreenOnlyOnBrightMode() -> Bool { return false }
}

class UserDefaultsUseCasesMock: UserDefaultsUseCases {

    @Published var statePublished = UserDefaultsState.initial()
    var state: Published<UserDefaultsState>.Publisher { $statePublished }

    @Published var ftueSkippedPublished = false
    var ftueSkipState: Published<Bool>.Publisher { $ftueSkippedPublished }

    @Published var unlinkedUserPublished = false
    var unlinkedUserState: Published<Bool>.Publisher { $unlinkedUserPublished }

    func setDarkMode(isDarkModeOn: Bool) {}
    func getDarkMode() -> Bool { return false }
    func setNotificationBadgeStatus(badgeStatus: Bool) {}
    func getNotificationBadgeStatus() -> Bool { return false }
    func getUserRegion() -> String { return "US" }
    func getUserLanguage() -> String { return "en" }
    func getFTUESkipped() -> Bool { return false }
    func setFTUESkipped(ftueCompleted: Bool) {}
    func getSubSnippetCloseDate(vin: String) -> Date? { return nil }
    func setSubSnippetCloseDate(date: Date, vin: String) {}
    func setEVSwapAnnouncementCount(count: Int) {}
    func getEVSwapAnnouncementCount() -> Int { return 0 }
    func getAnnouncementClosedDate(vin: String) -> Date? { return nil }
    func setAnnouncementClosedDate(date: Date, vin: String) {}
    func getAnnouncementCAEnroll(vin: String) -> Bool { return false }
    func setEVSwapExpiryDate(date: String) {}
    func setTfsSessionReceived(isTfsSessionReceived: Bool) {}
    func getTfsSessionReceived() -> Bool { return false }
    func setUnlinkedUser(vin: String, value: Bool) {}
    func getUnlinkedUser(vin: String) -> Bool { return false }
    func getEvgoExpiringSoonPopupCount() -> Int { return 0 }
    func getEvgoExpiredPopupCount() -> Int { return 0 }
    func incrementEvgoExpiringSoonPopupCount() {}
    func incrementEvgoExpiredPopupCount() {}
    func setStartClimateAlert(shown: Bool) {}
    func getStartClimateAlert() -> Bool { return false }
}

struct AccountUserProfileUseCasesMock: AccountUserProfileUseCases {

    func fetchLocationWithLatLon() async -> OAUserLocation? {
        return OAUserLocation(latitude: 32, longitude: 32)
    }
    func loadAccountUserProfile() async {}
    func changeEmail(_ email: String) async {}
    func changePassword(oldPassword: String, newPassword: String) async {}
    func navigateToChangeEmailScreen() {}
    func navigateToChangePasswordScreen() {}
    func loadAccountSettings() async {}
    func getProfileName() async -> String? { return "Test User" }
    func getProfileAddress(showLoading: Bool?) async -> String? { return "123 Test St" }
    func fetchUserPhoneNumber(showLoading: Bool?) async -> String? { return "************" }
    func logOutUser() async {}
    func getGUID() -> String? { return "test-guid" }
    func fetchATTToken() async -> String? { return "test-token" }
    func fetchLocation() async -> String? { return "test-location" }
    func updateVinList(vinList: String) {}
    func getEmail() -> String? { return "<EMAIL>" }
    func getLocale() -> String? { return "en_US" }
}

class ChargingUseCaseMock: ChargingUseCase {
    func startCharging(body: EVFeature.ChargeStartRequest) async {}

    func startHomeCharging() async {}

    func stopCharging(chargingId: String) async {}

    func fetchChargingSession(showLoading: Bool) async {}

    func popView() {}

    func buttonAction() {}

    func handleButtonText(_ session: EVFeature.EvChargeSession?) {}

    func navigateToDashboard() {}

    func contactSupport() {}

    var state: Published<ChargingState>.Publisher { $statePublished }
    @Published var statePublished = ChargingState(
        startChargeState: StartChargeState.initial,
        chargeSessionState: ChargeSessionState.initial,
        chargingStatus: ChargingStatus.chargingCompleted,
        enableStopChargingButton: true,
        showErrorOrFailed: true,
        showUnplugVehicle: true,
        fuelState: FuelState.loading,
        screenAction: ScreenChangeAction.initial,
        isPluggedInAndNotCharging: true
    )
}

struct OAUserLocationMock {
    var latitude: Double
    var longitude: Double

    init(latitude: Double, longitude: Double) {
        self.latitude = latitude
        self.longitude = longitude
    }
}
