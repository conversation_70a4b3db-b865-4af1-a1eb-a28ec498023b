// Copyright © 2025 Toyota. All rights reserved.

import XCTest
@testable import EVFeature

final class TeslaStateTests: XCTestCase {

    func testDefaultValuesAreFalse() {
        let state = TeslaState()
        XCTAssertFalse(state.isVinEligibleForTesla,
                       "By default, isVinEligibleForTesla should be false")
        XCTAssertFalse(state.hasRegisteredTesla,
                       "By default, hasRegisteredTesla should be false")
    }

    func testMutatingValuesIndependently() {
        var state = TeslaState()

        state.isVinEligibleForTesla = true
        state.hasRegisteredTesla    = true
        XCTAssertTrue(state.isVinEligibleForTesla,
                      "After setting, isVinEligibleForTesla should be true")
        XCTAssertTrue(state.hasRegisteredTesla,
                      "After setting, hasRegisteredTesla should be true")

        state.isVinEligibleForTesla = false
        XCTAssertFalse(state.isVinEligibleForTesla,
                       "After resetting, isVinEligibleForTesla should be false")
        XCTAssertTrue(state.hasRegisteredTesla,
                      "hasRegisteredTesla should remain true when only isVinEligibleForTesla changes")
    }
}
