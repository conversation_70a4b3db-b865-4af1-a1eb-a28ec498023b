// Copyright © 2025 Toyota. All rights reserved.
// swiftlint:disable line_length
// swiftlint:disable function_parameter_count
import XCTest
import Combine
import UIKit
@testable import EVFeature
@testable import DashboardFeature
@testable import VehicleFeature
@testable import Analytics
@testable import Navigation
@testable import Utility
@testable import NetworkClients
@testable import NativeLinker

class ElectricStatusUseCasesMock: ElectricStatusUseCases {
    var state: Published<ElectricStatusState>.Publisher { $statePublished }
    @Published var statePublished: ElectricStatusState = .notAvailable

    func fetchElectricStatus(silentRefresh: Bool?) async {}
    func postRealTimeChargingStatus() async -> String? { return nil }
    func fetchChargeStatus(appRequestNo: String) async {}
}

class TelemetryUseCasesMock: TelemetryUseCases {
    var state: Published<TelemetryState>.Publisher { $statePublished }
    @Published var statePublished: TelemetryState = .notAvailable

    func fetchTelemetry() async {}
}

class SelectedVehicleUseCasesMock: SelectedVehicleUseCases {
    var state: Published<Vehicle?>.Publisher { $statePublished }
    @Published var statePublished: Vehicle?

    func setSelectedVehicle(_ vehicle: Vehicle) {}
}

class AnalyticsUseCasesMock: AnalyticsUseCases {
    func logEvent(_ event: AnalyticsEvent) {}
    func logEvent(_ event: AnalyticsEvent, params: [String: Any]) {}
}

final class EvAPIRepoMock: EvAPIRepo {
    init() {
        super.init(
            provider: VehicleRequestProviderMock(),
            findProvider: FindStationsRequestProviderMock(),
            elegibilityProvider: AnnouncementsRequestProviderMock(),
            providerWallet: WalletRequestProviderMock(),
            chargingProvider: PublicChargingRequestProviderMock()
        )
    }
}

struct VehicleRequestProviderMock: VehicleRequestProvider {
    func makeDefaultVehicle(vin: String) async -> Result<DefaultVehicleResponse, OneapiError> {
        .success(DefaultVehicleResponse(payload: "String?", version: "String?", status: "String", message: "String", code: 200, errors: [""]))
    }

    func getTelemetry(vin: String, brand: VehicleResponse.Brand, generation: VehicleResponse.Generation) async -> Result<TelemetryResponse, OneapiError> {
        .success(TelemetryResponse(status: "String?", code: 200, message: "String?", errors: nil, payload: nil))
    }

    func getElectricStatus(vin: String, brand: String, generation: String) async -> Result<ElectricStatusResponse, OneapiError> {
        .success(ElectricStatusResponse(payload: nil, status: nil, timestamp: ""))
    }

    func getRealTimeStatus(vin: String, isEV: Bool, brand: String, generation: String, requestNo: String) async -> Result<ElectricStatusResponse, OneapiError> {
        .success(ElectricStatusResponse(payload: nil, status: nil, timestamp: nil))
    }

    func postRealTimeStatus(vin: String, brand: String, generation: String) async -> Result<PostRealTimeStatusResponse, OneapiError> {
        .success(PostRealTimeStatusResponse(payload: nil, status: nil, timestamp: nil))
    }

    func removeVehicle(vin: String, brand: VehicleResponse.Brand, generation: VehicleResponse.Generation) async -> Result<Bool, OneapiError> {
        .success(true)
    }

    func fetchVehicleSubscriptions(_ requestParams: VehicleSubscriptionsRequest) async -> Result<VehicleSubscriptionResponse, OneapiError> {
        .success(VehicleSubscriptionResponse(
                availableSubscriptions: [],
                trialSubscriptions: [],
                paidSubscriptions: [],
                isTrialEligible: nil,
                isAppUpdateRequired: nil,
                isPaidEnabled: nil,
                isBundlingEnabled: nil,
                isCPOEligible: nil
            )
        )
    }

    func getChargeSessionDetails(sessionId: String, partner: String) async -> Result<ChargeStatusResponse, OneapiError> {
        .success(.init(messages: nil, payload: nil))
    }

    func getChargeStatistics(vin: String, brand: String, generation: String, reportType: String, month: String?) async -> Result<ChargeStatsResponse, OneCdnError> {
        .success(ChargeStatsResponse(currentWeekReport: nil, monthlyReports: nil, lastCharge: nil))
    }

    func getChargeHistoryList(vin: String, brand: String, generation: String, chargingType: String, month: String?, startDate: String?, endDate: String?) async -> Result<ChargeHistoryResponse, OneCdnError> {
        .success(ChargeHistoryResponse(vin: nil, chargingSessions: nil))
    }

    func getEnrollmentCheck(vin: String, brand: String, email: String) async -> Result<EnrollmentCheckResponse, OneCdnError> {
        .success(EnrollmentCheckResponse())
    }

    func getMultidayScheduleRemoteControlStatus(vin: String, isEV: Bool, brand: String, generation: String, realTime: Bool, requestNo: String) async -> Result<MultidayScheduleResponse, OneapiError> {
        .success(.init())
    }

    func createOrModifyMultidaySchedule(vin: String, brand: String, generation: String, body: (any Encodable)?, method: MapMethod) async -> Result<PostRealTimeStatusResponse, OneapiError> {
        .success(PostRealTimeStatusResponse(payload: nil, status: nil, timestamp: nil))
    }

    func deleteMultidaySchedule(vin: String, brand: String, generation: String, settingId: Int) async -> Result<PostRealTimeStatusResponse, OneapiError> {
        .success(PostRealTimeStatusResponse(payload: nil, status: nil, timestamp: nil))
    }

    func ecoEnrollment(vin: String, brand: String, generation: String, enabled: Bool) async -> Bool {
        true
    }

    func getChargeEcoSchedule(vin: String, brand: String, generation: String) async -> Result<EcoSchedulesResponse, OneCdnError> {
        .success(EcoSchedulesResponse(
                message: "String",
                ecoEnrollmentDetails: EcoEnrollmentDetails(
                    vin: "String",
                    guid: "String",
                    watttimeBa: "String",
                    ecoStartTime: "String",
                    ecoEndTime: "String",
                    optedFromDate: "String",
                    optedToDate: "String?",
                    enabled: true,
                    settingId: "String"
                )
            )
        )
    }

    func createPHEVSchedule(vin: String, brand: String, generation: String, body: (any Encodable)?, method: MapMethod) async -> Result<PostRealTimeStatusResponse, OneapiError> {
        .success(PostRealTimeStatusResponse(payload: nil, status: nil, timestamp: "String?"))
    }

    func fetchPartnerEnrollmentTerms(brand: String, partner: String, language: String, make: String) async -> Result<LegalContentResponse, OneapiError> {
        .success(LegalContentResponse(
                messages: .init(),
                payload: LegalPayload(legalDetails: LegalDetails(partnerLegalID: "String", partner: "String", content: "String"))
            )
        )
    }

    func registerEvPartner(requestBody: EnrollEvPartnerRequest, vin: String, make: String) async -> Result<PostEnrollmentResponse, OneapiError> {
        .success(PostEnrollmentResponse(messages: nil, payload: PostEnrollmentPayload(accounts: nil)))
    }

    func fetchLCFSDashboard(vin: String, brand: String, locale: String) async -> Result<LcfsDashboardResponse, OneapiError> {
        .success(LcfsDashboardResponse(
                payload: LcfsPayload(
                    guid: "String",
                    vin: "String",
                    cleanElectricityChargedCurrentYear: 20,
                    co2eAvoidedCurrentYear: 20,
                    cleanElectricityChargedCurrentMonth: 20,
                    co2eAvoidedCurrentMonth: 20,
                    cleanChargingHistory: []
                )
            )
        )
    }

    func getRemoteControlStatus(vin: String, isEV: Bool, brand: String, generation: String, requestNo: String) async -> Result<MultidayScheduleResponse, OneapiError> {
        .success(MultidayScheduleResponse())
    }

    func getVehicleList() async -> Result<[VehicleResponse], OneapiError> {
        .success([])
    }

    func getHowToVideos(
        vin: String
    ) async -> Result<[HowToVideoResponse], OneapiError> {
        .success([])
    }
}
