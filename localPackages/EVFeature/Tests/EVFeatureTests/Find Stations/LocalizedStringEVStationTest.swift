// Copyright © 2025 Toyota. All rights reserved.

import XCTest
@testable import LocalizedStrings
@testable import EVFeature

final class LocalizedStringsTests: XCTestCase {

    override func setUp() {
        super.setUp()
    }

    override func tearDown() {
        super.tearDown()
    }

    func testAdaptorMayBeNeededWarning_LocalizationKey() {
        let expectedKey = "tesla.station.adaptor.warning"

        let warningString = Strings.EvTeslaStations.adaptorMayBeNeededWarning

        XCTAssertFalse(warningString.isEmpty, "The adaptor warning string should not be empty.")
    }
}
