// Copyright © 2025 Toyota. All rights reserved.
// swiftlint:disable line_length
import XCTest
import Combine
import UIKit
@testable import EVFeature
@testable import DashboardFeature
@testable import VehicleFeature
@testable import Analytics
@testable import Navigation
@testable import Utility
@testable import NetworkClients
@testable import NativeLinker

extension IonnaConsentDetails: Equatable {
    public static func == (lhs: IonnaConsentDetails, rhs: IonnaConsentDetails) -> Bool {
        switch (lhs, rhs) {
        case (.notAvailable, .notAvailable), (.loading, .loading):
            return true
        case (.success(let lhsAccepted, let lhsTerms), .success(let rhsAccepted, let rhsTerms)):
            return lhsAccepted == rhsAccepted && lhsTerms == rhsTerms
        default:
            return false
        }
    }
}

final class FindStationsLogicTests: XCTestCase {
    private var sut: FindStationsLogic!
    private var chargeInfoUseCasesMock: ChargeInfoMocks!
    private var teslaFlagMapperMock: TeslaFlagMapperMock!
    private var cancellables: Set<AnyCancellable>!

    // Create mocked dependencies
    private var electricStatusUseCasesMock = ElectricStatusUseCasesMock()
    private var telemetryUseCasesMock = TelemetryUseCasesMock()
    private var selectedVehicleUseCasesMock = SelectedVehicleUseCasesMock()
    private var analyticsUseCasesMock = AnalyticsUseCasesMock()
    private var chargeInfoAPIRepoMock = EvAPIRepoMock()
    private var navigationUseCasesMock = NavigationUseCasesMock()
    private var userDefaultsUseCasesMock = UserDefaultsUseCasesMock()
    private var accountUserProfileUseCasesMock = AccountUserProfileUseCasesMock()
    private var chargingUseCaseMock = ChargingUseCaseMock()

    override func setUp() {
        super.setUp()

        chargeInfoUseCasesMock = ChargeInfoMocks()
        teslaFlagMapperMock = TeslaFlagMapperMock()
        cancellables = Set<AnyCancellable>()

        // Initialize with custom cancellables
        // When - Create a new instance with the same cancellables set
        sut = FindStationsLogic(
            statePublished: FindStationsState.initial,
            electricStatusUseCases: ElectricStatusUseCasesMock(),
            telemetryUseCases: TelemetryUseCasesMock(),
            selectedVehicleUseCases: SelectedVehicleUseCasesMock(),
            analyticsUseCases: AnalyticsUseCasesMock(),
            chargeInfoAPIRepo: EvAPIRepoMock(),
            navigationUseCases: NavigationUseCasesMock(),
            userDefaultsUseCases: UserDefaultsUseCasesMock(),
            nativeLinkerLogic: AccountUserProfileUseCasesMock(),
            chargingState: nil,
            cancellables: cancellables,
            chargingUseCases: ChargingUseCaseMock(),
            chargeInfoUseCases: chargeInfoUseCasesMock,
            teslaFlagMapper: teslaFlagMapperMock
        )
    }

    override func tearDown() {
        sut = nil
        chargeInfoUseCasesMock = nil
        teslaFlagMapperMock = nil
        cancellables = nil
        super.tearDown()
    }

    // MARK: - fetchIonnaConsentDetails Tests
    func testFetchIonnaConsentDetailsHandlesLoadingState() async {
        let initialIonnaConsentDetails = IonnaConsentDetails.notAvailable
        // let loadingIonnaConsentDetails = IonnaConsentDetails.loading

        sut.statePublished.ionnaConsentDetails = initialIonnaConsentDetails
        await sut.fetchIonnaConsentDetails()
        XCTAssertEqual(sut.statePublished.ionnaConsentDetails, initialIonnaConsentDetails, "ionnaConsentDetails should be updated with loading state")
    }

    func testNavigateToIonnaConsentScreen() {
        let termsAndConditions = "Test Terms and Conditions"

        sut.navigateToIonnaConsentScreen(termsAndConditions: termsAndConditions)

        XCTAssertTrue(chargeInfoUseCasesMock.didNavigateToIonnaConsentScreen, "navigateToIonnaConsentScreen should be called on chargeInfoUseCases")
    }
}
