// Copyright © 2024 Toyota. All rights reserved.

import XCTest
import OneAppTest
import LocalizedStrings

@testable import EVFeature

final class FindStationsTests: XCTestCase {
    var logic: FindStationsMocks!

    override func setUp() {
        super.setUp()
        self.logic = FindStationsMocks()
    }

    override func tearDown() {
        super.tearDown()
        self.logic = nil
    }

    func testFindStationsState_DefaultValuesFromInitial() {
        let initialState = FindStationsState.initial

        XCTAssertEqual(initialState.selectedPartners, [], "Initial selectedPartners should be empty.")
        XCTAssertEqual(initialState.selectedPlugTypes, [], "Initial selectedPlugTypes should be empty.")
        XCTAssertFalse(initialState.teslaState.isVinEligibleForTesla, "Initial isVinEligibleForTesla should be false.")
        XCTAssertFalse(initialState.teslaState.hasRegisteredTesla, "Initial hasRegisteredTesla should be false.")
    }

    func testGetBrandName() {
        _ = logic.getBrandName()
        XCTAssertTrue(logic.didCallGetBrandName)
    }

    func testDismissPopup() {
        logic.dismissPopup()
        XCTAssertTrue(logic.didCallDismissPopup)
    }

    func testNavigateToWallet() {
        logic.navigateToWallet(isSetupWallet: false)
        XCTAssertTrue(logic.didCallNavigateToWallet)
    }

    func testShowEvgoComplimentaryPopup() {
        logic.showEvgoComplimentaryPopup(expiryDays: 5, walletSetup: true)
        XCTAssertTrue(logic.didCallShowEvgoComplimentaryPopup)
    }

    func testSearch() {
        logic.search(lat: "37.7749", lon: "-122.4194")
        XCTAssertTrue(logic.didCallSearch)
    }

    func testGetStationsCount() {
        _ = logic.getStationsCount(level1: "1", level2: "2", dcFast: "3")
        XCTAssertTrue(logic.didCallGetStationsCount)
    }

    func testDistanceBetweenLocations() {
        _ = logic.distanceBetweenLocations(lat: 37.774_9, lon: -122.419_4)
        XCTAssertTrue(logic.didCallDistanceBetweenLocations)
    }

    func testFetchMapPinAnnotations() {
        logic.fetchMapPinAnnotations()
        XCTAssertTrue(logic.didCallFetchMapPinAnnotations)
    }

    func testMapStationButtonAction() {
        logic.mapStationButtonAction(station: nil)
        XCTAssertTrue(logic.didCallMapStationButtonAction)
    }

    func testMapStationButtonTitle() {
        _ = logic.mapStationButtonTitle(station: nil)
        XCTAssertTrue(logic.didCallMapStationButtonTitle)
    }

    func testGetPartnersList() {
        _ = logic.getPartnersList()
        XCTAssertTrue(logic.didCallGetPartnersList)
    }

    func testHandleFilters() async {
        await logic.handleFilters(selectedPartners: [], selectedPlugTypes: [])
        XCTAssertTrue(logic.didCallHandleFilters)
    }

    func testHandleFilters_shouldPass_correctParnters() async {
        let expectedPartners = ["dummy partner"]
        await logic.handleFilters(selectedPartners: expectedPartners, selectedPlugTypes: [])
        XCTAssertEqual(logic.selectedPartnersPassed, expectedPartners)
    }

    func testHandleFilters_shouldPass_correctPlugTypes() async {
        let expectedPlugTypes = ["dummy plug type", "dummy plug type B", "dummy plug type C"]
        await logic.handleFilters(selectedPartners: [], selectedPlugTypes: expectedPlugTypes)
        XCTAssertEqual(logic.selectedPlugTypesPassed, expectedPlugTypes)
    }

    func testShowFavoritesList() {
        logic.showFavoritesList()
        XCTAssertTrue(logic.didCallShowFavoritesList)
    }

    func testIsFavorite() {
        _ = logic.isFavorite()
        XCTAssertTrue(logic.didCallIsFavorite)
    }

    func testToggleFavorites() {
        logic.toggleFavorites(placeId: "placeId", isFavorite: false, isShowFavorites: false)
        XCTAssertTrue(logic.didCallToggleFavorites)
    }

    func testGetDirections() {
        logic.getDirections(
            latitude: 33.169_600,
            longitude: -96.519_130,
            destinationAddress: "6565 headquarters drive"
        )
        XCTAssertTrue(logic.didCallGetDirections)
    }

    func testSendToCar() async {
        _ = await logic.sendToCar(address: "placeId")
        XCTAssertTrue(logic.didCallSendToCar)
    }

    func testOpenWebSite() {
        logic.oopenWebSite(url: "https://toyota.com")
        XCTAssertTrue(logic.didCallOpenWebSite)
    }

    func testMapToHydrogenStations() {
        logic.mapToHydrogenStations()
        XCTAssertTrue(logic.didCallMapToHydrogenStations)
    }

    func testCallEVStationButton() {
        let station = EvStation(evEvses: [], evIsPartner: false, evEvSource: "", isFavourite: false)
        let screen = EVScreen.none
        logic.callEVStationButton(station: station, screen: screen)
        XCTAssertTrue(logic.didCallEVStationButton)
    }

    func testGetPlugAvailabilityArray() {
        let detailsJ1772 = EvConnectorDetails(evActive: 1, evTotal: 4)
        let detailsCCS = EvConnectorDetails(evActive: 2, evTotal: 4)
        let detailsChademo = EvConnectorDetails(evActive: 3, evTotal: 4)
        let detailsNACS = EvConnectorDetails(evActive: 4, evTotal: 4)

        let connectorSum = EvConnectorSum(
            evCcs1: detailsCCS,
            evChademo: detailsChademo,
            evJ1772: detailsJ1772,
            evNacs: detailsNACS
        )

        let result = connectorSum.getPlugAvailabilityArray()

        // Assert that each array contains the expected display name and string
        XCTAssertTrue(result.contains([PlugType.level2.displayName, detailsJ1772.toString() ?? ""]))
        XCTAssertTrue(result.contains([PlugType.dcFast.displayName, detailsCCS.toString() ?? ""]))
        XCTAssertTrue(result.contains([FindStationsConstants.chademoUppercase, detailsChademo.toString() ?? ""]))
        XCTAssertTrue(result.contains([PlugType.nacs.displayName, detailsNACS.toString() ?? ""]))
    }

    func testGetEVStationStatusTitle() async {
        let station = EvStation(evEvses: [], evIsPartner: false, evEvSource: "", isFavourite: false)
        await logic.getEVStationStatusTitle(station: station)
        XCTAssertTrue(logic.didGetEVStationStatusTitle)
    }

    func testSendToCarWithAddress() async {
        let result = await logic.sendToCar(placeId: "placeId", address: "123 Main St")
        XCTAssertTrue(logic.didSendToCar)
        XCTAssertTrue(result)
    }

    func testRemoveAllPartnerFilter() {
        logic.removeAllPartnerFilter(partner: "partner")
        XCTAssertTrue(logic.didRemoveAllPartnerFilter)
    }

    func testRemoveAllPlugtypeFilter() {
        logic.removeAllPlugtypeFilter(plugType: "plugType")
        XCTAssertTrue(logic.didRemoveAllPlugTypeFilter)
    }

    func testAppendPartnerFilter() {
        logic.appendPartnerFilter(partner: "partner")
        XCTAssertTrue(logic.didAppendPartnerFilter)
    }

    func testAppendPlugtypeFilter() {
        logic.appendPlugtypeFilter(plugType: "plugType")
        XCTAssertTrue(logic.didAppendPlugTypeFilter)
    }

    func testUpdateConnectorSelectionVisibility() {
        logic.updateConnectorSelectionVisibility(show: true)
        XCTAssertTrue(logic.didUpdateConnectorSelectionVisibility)
    }

    func testIsMyDestinationEnabled() {
        let result = logic.isMyDestinationEnabled()
        XCTAssertTrue(logic.didMyDestinationEnabled)
        XCTAssertTrue(result)
    }

    func testFetchIonnaDetails() async {
        await logic.fetchIonnaConsentDetails()
        XCTAssertTrue(logic.didCallFetchIonnaConsent)
    }

    func testNavigateToIonnaConsent() {
        _ = logic.navigateToIonnaConsentScreen(termsAndConditions: "")
        XCTAssertTrue(logic.didCallNavigateToIonnaConsent)
    }

    func testShouldDisplayTeslaRegisterButton() {
        _ = logic.shouldDisplayRegisterButton(
            for: EvStation(evEvses: [], evIsPartner: false, evEvSource: "", isFavourite: false)
        )
        XCTAssertTrue(logic.didCallShouldDisplayTeslaRegisterButton)
    }

    func testShouldDisplayAdaptorDisclaimer() {
        _ = logic.shouldDisplayAdaptorDisclaimer(for: "")
        XCTAssertTrue(logic.didCallShouldDisplayAdaptorDisclaimer)
    }

    final class LocalizedStringsTests: XCTestCase {

        override func setUp() {
            super.setUp()
        }

        override func tearDown() {
            super.tearDown()
        }

        func testAdaptorMayBeNeededWarning_LocalizationKey() {
            let expectedKey = "tesla.station.adaptor.warning"

            let warningString = Strings.EvTeslaStations.adaptorMayBeNeededWarning

            XCTAssertFalse(warningString.isEmpty, "The adaptor warning string should not be empty.")
        }
    }
}
