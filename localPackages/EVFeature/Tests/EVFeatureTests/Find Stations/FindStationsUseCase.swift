// Copyright © 2024 Toyota. All rights reserved.

import XCTest
import Combine
import SwiftUI
@testable import EVFeature

class MockFindStationsUseCase: FindStationsUseCase {

    @Published var internalState = FindStationsState.initial
    var fetchIonnaConsentDetailsCalled = false
    var navigateToIonnaConsentScreenCalled = false
    var navigateToIonnaConsentScreenTerms: String?
    var ionnaConsentDetailsResult: EVFeature.IonnaConsentDetails?
    var ionnaConsentDetailsError: Error?

    // Conform to protocol by providing `state` as the publisher
    var state: Published<FindStationsState>.Publisher {
        $internalState
    }

    func fetchIonnaConsentDetails() async {
        fetchIonnaConsentDetailsCalled = true
        if let error = ionnaConsentDetailsError {
            internalState.error = error
        } else {
            internalState.ionnaConsentDetails = ionnaConsentDetailsResult
        }
    }

    func navigateToIonnaConsentScreen(termsAndConditions: String) {
        navigateToIonnaConsentScreenCalled = true
        navigateToIonnaConsentScreenTerms = termsAndConditions
        internalState.stationDetailButtonState = .success("", .ionnaTermsAndConditions(termsAndConditions))
    }

    func isEVGoExpired(_ res: EVFeature.EVPartnerStatus) -> Bool? {
        return nil
    }

    func isEVGoExpiringSoon(_ res: EVFeature.EVPartnerStatus) -> Bool? {
        return nil
    }

    func shouldDisplayRegisterButton(for station: EVFeature.EvStation) -> Bool {
        station.evEvSource.contains("tesla") ? true : false
    }

    func shouldDisplayAdaptorDisclaimer(for evSource: String) -> Bool {
        evSource.contains("tesla") ? true : false
    }

    // Implement other required methods
    func load() async {}
    func navigateToRegisterPartnerScreen(partner: String) {}
    func getBrandName() -> String { "" }
    func dismissPopup() {}
    func navigateToWallet(isSetupWallet: Bool) {}
    func showEvgoComplimentaryPopup(expiryDays: Int, walletSetup: Bool) {}
    func search(lat: String?, lon: String?) {}
    func getStationsCount(level1: String?, level2: String?, dcFast: String?) -> String { "" }
    func distanceBetweenLocations(lat: Double, lon: Double) -> String? { nil }
    func fetchMapPinAnnotations() {}
    func callEVStationButton(station: EvStation, screen: EVScreen) {}
    func getEVStationStatusTitle(station: EvStation) async {}
    func getPartnersList() -> [String] { [] }
    func handleFilters(selectedPartners: [String], selectedPlugTypes: [String]) async {}
    func toggleFavorites(placeId: String, isFavorite: Bool, isShowFavorites: Bool) {}
    func getDirections(latitude: Double, longitude: Double, destinationAddress: String?) {}
    func sendToCar(address: String) async -> Bool { false }
    func oopenWebSite(url: String) {}
    func callStation(number: String) {}
    func focusMap(to: LocationToCenter) {}
    func nextPage() {}
    func resetPageCount() {}
    func startChargingWithConnector(station: EvStation, evseUid: String, connectorId: String) {}
    func updateConnectorSelectionVisibility(show: Bool) {}
    func removeAllPartnerFilter(partner: String?) {}
    func removeAllPlugtypeFilter(plugType: String?) {}
    func appendPartnerFilter(partner: String) {}
    func appendPlugtypeFilter(plugType: String) {}
    func isMyDestinationEnabled() -> Bool { false }
}

struct EVPartnerStatus {
    let partnerName: String
    let status: String
    let enrollmentDate: String
    let expiryDate: String
}

class FindStationsStateNotifierTests: XCTestCase {
    var notifier: FindStationsStateNotifier!
    var mockUseCase: MockFindStationsUseCase!
    var cancellables: Set<AnyCancellable>!

    override func setUp() {
        super.setUp()
        mockUseCase = MockFindStationsUseCase()
        notifier = FindStationsStateNotifier(useCases: mockUseCase)
        cancellables = []
    }

    override func tearDown() {
        notifier = nil
        mockUseCase = nil
        cancellables = nil
        super.tearDown()
    }

    func testFetchIonnaConsent_Success() async {
        let expectedDetails = EVFeature.IonnaConsentDetails.success(true, "") // Use actual initializer from EVFeature
        mockUseCase.ionnaConsentDetailsResult = expectedDetails
        let expectation = XCTestExpectation(description: "State updates with ionnaConsentDetails")

        notifier.$state
            .dropFirst() // Skip initial state
            .sink { state in
                if state.ionnaConsentDetails != nil {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)

        await notifier.fetchIonnaConsent()

        await fulfillment(of: [expectation], timeout: 1.0)
        XCTAssertTrue(mockUseCase.fetchIonnaConsentDetailsCalled, "fetchIonnaConsentDetails should be called")
    }

    func testFetchIonnaConsent_Failure() async {
        let expectedError = NSError(domain: "Test", code: -1, userInfo: nil)
        mockUseCase.ionnaConsentDetailsError = expectedError
        let expectation = XCTestExpectation(description: "State updates with error")

        notifier.$state
            .dropFirst()
            .sink { state in
                if state.error != nil {
                    expectation.fulfill()
                }
            }
            .store(in: &cancellables)

        await notifier.fetchIonnaConsent()

        await fulfillment(of: [expectation], timeout: 1.0)
        XCTAssertTrue(mockUseCase.fetchIonnaConsentDetailsCalled, "fetchIonnaConsentDetails should be called")

    }

    @MainActor func testNavigateToIonnaConsent() {
        let terms = "Sample Terms and Conditions"
        let expectation = XCTestExpectation(description: "State updates with navigation")

        notifier.$state
            .dropFirst()
            .sink { state in
                if case .success(_, let screen) = state.stationDetailButtonState,
                   case .ionnaTermsAndConditions(let receivedTerms) = screen {
                    if receivedTerms == terms {
                        expectation.fulfill()
                    }
                }
            }
            .store(in: &cancellables)

        notifier.navigateToIonnaConsent(termsAndConditions: terms)

        wait(for: [expectation], timeout: 1.0)
        XCTAssertTrue(mockUseCase.navigateToIonnaConsentScreenCalled, "navigateToIonnaConsentScreen should be called")
        XCTAssertEqual(mockUseCase.navigateToIonnaConsentScreenTerms, terms, "Terms should match")
        if case .success(_, let screen) = notifier.state.stationDetailButtonState {
            XCTAssertEqual(screen, .ionnaTermsAndConditions(terms),
                           "stationDetailButtonState should reflect navigation")
        } else {
            XCTFail("stationDetailButtonState should be success with ionnaTermsAndConditions")
        }
    }

    func testShouldDisplayAdaptorWarning_ForTesla() {
        let result = notifier.shouldDisplayAdaptorDisclaimer(for: "tesla")
        XCTAssertTrue(result)
    }

    func testShouldDisplayAdaptorWarning_NotForTesla() {
        let result = notifier.shouldDisplayAdaptorDisclaimer(for: "dummy station")
        XCTAssertFalse(result)
    }

    func testShouldDisplayRegisterButton_ForTesla() {
        let result = notifier.shouldShowRegisterButton(
            for: EvStation(evEvses: [], evIsPartner: false, evEvSource: "tesla", isFavourite: false)
        )
        XCTAssertTrue(result)
    }

    func testShouldDisplayRegisterButton_NotForTesla() {
        let result = notifier.shouldShowRegisterButton(
            for: EvStation(evEvses: [], evIsPartner: false, evEvSource: "dummy station", isFavourite: false)
        )
        XCTAssertFalse(result)
    }
}
