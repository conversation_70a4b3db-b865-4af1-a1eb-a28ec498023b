// Copyright © 2024 Toyota. All rights reserved.

import Foundation
@testable import DashboardFeature
@testable import EVFeature
import Combine
import SwiftUI

internal class ChargeInfoMocks: ChargeInfoUseCases {
    @Published var statePublished = ChargeInfoState.initial
    var state: Published<ChargeInfoState>.Publisher { $statePublished }
    var fetchedConsents: [CPOProvider] = []
    var acceptedConsents: [(provider: CPOProvider, accepted: Bool)] = []
    var shownConsentScreens: [(provider: CPOProvider, terms: String)] = []
    var didCallLoad = false
    var didUpdateFuelState = false
    var didUpdateEletricFuelState = false
    var didUpdatePHEVState = false
    var didUpdateOdometerState = false
    var didCallObserveVehicle = false
    var didCallNavigateToChargeHistory = false
    var didCallNavigateToChargeSchedule = false
    var didCallFetchChargeHistory = false
    var didCallFetchStatistics = false
    var didCallFetchEnrollmentCheck = false
    var didCallIsEVGoExpired = false
    var didCallIsEVGoExpiringSoon = false
    var didCallProgressColor = false
    var didCallNavigateToStatistics = false
    var didCallNavigateToCleanAssistDetailPage = false
    var didCallFetchCleanAssistDetails = false
    var didCallAcceptCleanAssistTerms = false
    var didCallNavigateToRegisterPartnerScreen = false
    var didCallNavigateToManualSchedule = false
    var didCallNavigateToChargeAssistSchedule = false
    var didCallFetchLCFSDashboard = false
    var didCallGetBrandName = false
    var didCallDismissPopup = false
    var didCallNavigateToWallet = false
    var didCallShowEvgoComplimentaryPopup = false
    var didCallGetLastUpdatedDate = false
    var didCallNavigateToCreateScheduleScreen = false
    var didNavToCreateModScheduleScreen = false
    var didLogStatisticsFilte = false
    var didLogStatisticsLearnMore = false
    var didNavigateToFindStationsScreen = false
    var didGetWattTimeUrl = false
    var didFetchIonnaConsentDetails = false
    var didAcceptIonnaTerms = false
    var didNavigateToIonnaConsentScreen = false
    var didGetIonnaButtonTitle = false
    func load(polling: Bool = false) async {
        didCallLoad = true
        await fetchStatistics()
        await fetchChargeHistory()
        await fetchEnrollmentCheck()
        await fetchCleanAssistElegibility()
        await fetchPublicChargingSettingsElegibility()

        statePublished.isEvTesla = true
        statePublished.teslaConsentDetails = .success(false, "tcn consent terms and conditions")
        statePublished.isEvIonna = true
        statePublished.ionnaConsentDetails = .success(false, "ionna consent terms and conditions")
    }

    func fetchStatistics(reportType: String = "year_week", month: String? = nil) async {
        didCallFetchStatistics = true
        let currentWeekReport = EVCurrentWeekReport(chargingCount: "40",
                                                    leafCount: "5",
                                                    totalChargeKwhr: 10_000)
        let chargeClasification = EVChargeClasification.nonEcho
        let watttimeDetails = EVWatttimeDetails(chargeClasification: chargeClasification,
                                                region: "en-US",
                                                co2Value: "10",
                                                healthDollarValue: "200")
        let lastCharge = EVLastCharge(chargeLocationType: "Public",
                                      startTime: "2024-07-09T19:43:57Z",
                                      endTime: "2024-07-10T00:15:59Z",
                                      socBeforeCharging: "57%",
                                      socAfterCharging: "63%",
                                      totalChargeKwhr: "5.328",
                                      latitude: "33.146935",
                                      longitude: "-96.88672",
                                      watttimeDetails: watttimeDetails,
                                      address: "6565 headquarters drive, Plano TX 75024",
                                      cdrDetails: nil)
        let stats = ChargeStatistics(currentWeekReport: currentWeekReport,
                                     monthlyReports: [],
                                     lastCharge: lastCharge,
                                     barChartData: ChargeStatisticsBarData())
        statePublished.statisticsState = .success(stats)
    }

    func fetchEnrollmentCheck() async {
        didCallFetchEnrollmentCheck = true
        let messages = EVMessages(responseCode: 200, description: "Success")
        let partnerEnrollment = EVPartnerEnrollment(guid: "123",
                                                    wallet: "Found",
                                                    partnerStatus: [])
        let payload = EVEnrollmentPayload(showAssociatedBanners: "skip",
                                          partnerEnrollment: partnerEnrollment)
        let state = EVEnrollmentCheck(messages: messages,
                                      payload: payload)
        statePublished.enrollmentState = .success(state)
    }

    func fetchCleanAssistElegibility() async {
        statePublished
            .cleanAssistEnrollmentState = CleanAssistEnrollmentState(
                lcfsOptIn: "In",
                lcfsEligible: true)
    }

    func fetchPublicChargingSettingsElegibility() async {
        statePublished.showChargingSettings = true
    }

    func navigateToChargeHistory() {
        didCallNavigateToChargeHistory = true
    }

    func navigateToChargeSchedule() {
        didCallNavigateToChargeSchedule = true
    }

    func fetchChargeHistory(startDate: String? = nil, endDate: String? = nil, chargingType: String = "") async {
        didCallFetchChargeHistory = true
        statePublished.historyState = .loadinng
        let chargeClasification = EVChargeClasification.nonEcho
        let watttimeDetails = EVWatttimeDetails(chargeClasification: chargeClasification,
                                                region: "en-US",
                                                co2Value: "10",
                                                healthDollarValue: "200")
        let lastCharge = EVLastCharge(chargeLocationType: "Public",
                                      startTime: "2024-07-09T19:43:57Z",
                                      endTime: "2024-07-10T00:15:59Z",
                                      socBeforeCharging: "57%",
                                      socAfterCharging: "63%",
                                      totalChargeKwhr: "5.328",
                                      latitude: "33.146935",
                                      longitude: "-96.88672",
                                      watttimeDetails: watttimeDetails,
                                      address: "6565 headquarters drive, Plano TX 75024",
                                      cdrDetails: nil)

        let response = ChargeHistory(vin: "**********",
                                     chargingSessions: [
                                        lastCharge
                                     ])
        statePublished.historyState = .success(response)
    }

    func isEVGoExpired(_ res: EVFeature.EVPartnerStatus) -> Bool? {
        didCallIsEVGoExpired = true
        return nil
    }

    func isEVGoExpiringSoon(_ res: EVFeature.EVPartnerStatus) -> Bool? {
        didCallIsEVGoExpiringSoon = true
        return nil
    }

    func progressColor(_ percent: Int, isPHEV: Bool) -> Color {
        didCallProgressColor = true
        return Color.clear // Assuming Color.clear is a placeholder for the actual implementation
    }

    func navigateToStatistics() {
        didCallNavigateToStatistics = true
    }

    func navigateToCleanAssistDetailPage() {
        didCallNavigateToCleanAssistDetailPage = true
    }

    func navigateToManualSchedule() {
        didCallNavigateToManualSchedule = true
    }

    func navigateToChargeAssistScheduleScreen() {
        didCallNavigateToChargeAssistSchedule = true
    }

    func fetchCleanAssistDetails() async {
        didCallFetchCleanAssistDetails = true
    }

    func acceptCleanAssistTerms(_ userAccepted: Bool) async {
        didCallAcceptCleanAssistTerms = true
    }

    func navigateToRegisterPartnerScreen(partner: String) {
        didCallNavigateToRegisterPartnerScreen = true
    }

    func fetchLCFSDashboard() async {
        didCallFetchLCFSDashboard = true
    }

    func getBrandName() -> String {
        didCallGetBrandName = true
        return ""
    }

    func dismissPopup() {
        didCallDismissPopup = true
    }

    func navigateToWallet(isSetupWallet: Bool) {
        didCallNavigateToWallet = true
    }

    func showEvgoComplimentaryPopup(expiryDays: Int, walletSetup: Bool) {
        didCallShowEvgoComplimentaryPopup = true
    }

    func getLastUpdatedDate() -> String? {
        didCallGetLastUpdatedDate = true
        return nil
    }

    func navigateToCreateScheduleScreen(isDeparture: Bool) {
        didCallNavigateToCreateScheduleScreen = true
    }

    func navigateToCreateModifyScheduleScreen() {
        didNavToCreateModScheduleScreen = true
    }

    func logStatisticsFilterTap() {
        didLogStatisticsFilte = true
    }

    func logStatisticsLearnMore() {
        didLogStatisticsLearnMore = true
    }

    func navigateToFindStationsScreen() {
        didNavigateToFindStationsScreen = true
    }

    func getWattTimeUrl() -> String {
        didGetWattTimeUrl = true
        return ""
    }

    func fetchIonnaConsentDetails() async {
        didFetchIonnaConsentDetails = true
        await fetchConsents(for: .ionna)
    }

    func acceptIonnaTerms(_ userAccepted: Bool) async {
        didAcceptIonnaTerms = true
        await acceptConsentTerms(userAccepted, for: .ionna)
    }

    func navigateToIonnaConsentScreen(termsAndConditions: String) {
        didNavigateToIonnaConsentScreen = true
        showConsentScreen(termsAndConditions: termsAndConditions, for: .ionna)
    }

    func getIonnaButtonTitle(acknowledged: Bool?) -> String {
        didGetIonnaButtonTitle = true
        return ""
    }

    func fetchConsents(for provider: CPOProvider) async {
        fetchedConsents.append(provider)

        switch provider {
        case .tcn:
            statePublished.teslaConsentDetails = .success(false, "tcn consent terms and conditions")
        case .ionna:
            statePublished.ionnaConsentDetails = .success(false, "ionna consent terms and conditions")
        }
    }

    func acceptConsentTerms(_ userAccepted: Bool, for provider: CPOProvider) async {
        acceptedConsents.append((provider: provider, accepted: userAccepted))

        switch provider {
        case .tcn:
            statePublished.teslaConsentDetails = .success(userAccepted, "Tesla consent terms and conditions")
        case .ionna:
            statePublished.ionnaConsentDetails = .success(userAccepted, "Ionna consent terms and conditions")
        }
    }

    func showConsentScreen(termsAndConditions: String, for provider: CPOProvider = .tcn) {
        shownConsentScreens.append((provider: provider, terms: termsAndConditions))
    }
}
