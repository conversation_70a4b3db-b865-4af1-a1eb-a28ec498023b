// Copyright © 2024 Toyota. All rights reserved.

extension ChargeInfoMocks {
    func observeVehicle() {
        didCallObserveVehicle = true
    }

    func updateFuelState() {
        didUpdateFuelState = true
    }

    func updateElectricFuelState() {
        didUpdateEletricFuelState = true
    }

    func updatePHEVState() {
        didUpdatePHEVState = true
    }

    func updateOdometerState() {
        didUpdateOdometerState = true
    }
}
