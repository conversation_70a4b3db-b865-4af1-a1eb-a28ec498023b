// Copyright © 2024 Toyota. All rights reserved.

import Foundation
@testable import DashboardFeature
@testable import EVFeature
import Combine

class FindStationsMocks: FindStationsUseCase {
    @Published var statePublished = FindStationsState.initial
    var state: Published<FindStationsState>.Publisher { $statePublished }

    var didCallLoad = false
    var didCallIsEVGoExpired = false
    var didCallIsEVGoExpiringSoon = false
    var didCallNavigateToRegisterPartnerScreen = false
    var didCallGetBrandName = false
    var didCallDismissPopup = false
    var didCallNavigateToWallet = false
    var didCallShowEvgoComplimentaryPopup = false
    var didCallSearch = false
    var didCallGetStationsCount = false
    var didCallDistanceBetweenLocations = false
    var didCallFetchMapPinAnnotations = false
    var didCallIsPartner = false
    var didCallMapStationButtonAction = false
    var didCallMapStationButtonTitle = false
    var didCallGetPartnersList = false
    var didCallHandleFilters = false
    var didCallShowFavoritesList = false
    var didCallIsFavorite = false
    var didCallToggleFavorites = false
    var didCallGetDirections = false
    var didCallSendToCar = false
    var didCallOpenWebSite = false
    var didCallDealer = false
    var didCenterMap = false
    var didCallNextPage = false
    var didCallResetPageCount = false
    var didCallMapToHydrogenStations = false
    var didCallEVStationButton = false
    var didGetEVStationStatusTitle = false
    var didSendToCar = false
    var didStartChargingWithConnector = false
    var didRemoveAllPartnerFilter = false
    var didRemoveAllPlugTypeFilter = false
    var didRemoveAllFilters = false
    var didAppendPartnerFilter = false
    var didAppendPlugTypeFilter = false
    var didUpdateConnectorSelectionVisibility = false
    var didMyDestinationEnabled = false
    var didCallFetchIonnaConsent = false
    var didCallNavigateToIonnaConsent = false
    var didCallShouldDisplayAdaptorDisclaimer = false
    var didCallShouldDisplayTeslaRegisterButton = false
    var selectedPartnersPassed: [String] = []
    var selectedPlugTypesPassed: [String] = []

    func load() async {
        didCallLoad = true
    }

    func isEVGoExpired(_ res: EVFeature.EVPartnerStatus) -> Bool? {
        didCallIsEVGoExpired = true
        return false
    }

    func isEVGoExpiringSoon(_ res: EVFeature.EVPartnerStatus) -> Bool? {
        didCallIsEVGoExpiringSoon = true
        return false
    }

    func navigateToRegisterPartnerScreen(partner: String) {
        didCallNavigateToRegisterPartnerScreen = true
    }

    func getBrandName() -> String {
        didCallGetBrandName = true
        return ""
    }

    func dismissPopup() {
        didCallDismissPopup = true
    }

    func navigateToWallet(isSetupWallet: Bool) {
        didCallNavigateToWallet = true
    }

    func showEvgoComplimentaryPopup(expiryDays: Int, walletSetup: Bool) {
        didCallShowEvgoComplimentaryPopup = true
    }

    func search(lat: String?, lon: String?) {
        didCallSearch = true
    }

    func getStationsCount(level1: String?, level2: String?, dcFast: String?) -> String {
        didCallGetStationsCount = true
        return ""
    }

    func distanceBetweenLocations(lat: Double, lon: Double) -> String? {
        didCallDistanceBetweenLocations = true
        return nil
    }

    func fetchMapPinAnnotations() {
        didCallFetchMapPinAnnotations = true
    }

    func mapStationButtonAction(station: EVFeature.EvStation?) {
        didCallMapStationButtonAction = true
    }

    func mapStationButtonTitle(station: EVFeature.EvStation?) -> String? {
        didCallMapStationButtonTitle = true
        return nil
    }

    func getPartnersList() -> [String] {
        didCallGetPartnersList = true
        return []
    }

    func handleFilters(selectedPartners: [String], selectedPlugTypes: [String]) async {
        didCallHandleFilters = true
        selectedPartnersPassed = selectedPartners
        selectedPlugTypesPassed = selectedPlugTypes
    }

    func showFavoritesList() {
        didCallShowFavoritesList = true
    }

    func isFavorite() -> [String: Bool] {
        didCallIsFavorite = true
        return [:]
    }

    func toggleFavorites(placeId: String, isFavorite: Bool, isShowFavorites: Bool) {
        didCallToggleFavorites = true
    }

    func getDirections(latitude: Double, longitude: Double, destinationAddress: String?) {
        didCallGetDirections = true
    }

    func sendToCar(address: String) async -> Bool {
        didCallSendToCar = true
        return false
    }

    func oopenWebSite(url: String) {
        didCallOpenWebSite = true
    }
    func callStation(number: String) {
        didCallDealer = true
    }

    func focusMap(to location: EVFeature.LocationToCenter) {
        didCenterMap = true
    }

    func nextPage() {
        didCallNextPage = true
    }

    func resetPageCount() {
        didCallResetPageCount = true
    }

    func mapToHydrogenStations() {
        didCallMapToHydrogenStations = true
    }

    func callEVStationButton(station: EVFeature.EvStation, screen: EVFeature.EVScreen) {
        didCallEVStationButton = true
    }

    func getEVStationStatusTitle(station: EVFeature.EvStation) async {
        didGetEVStationStatusTitle = true
    }

    func sendToCar(placeId: String, address: String) async -> Bool {
        didSendToCar = true
        return true
    }

    func startChargingWithConnector(station: EVFeature.EvStation, evseUid: String, connectorId: String) {
        didStartChargingWithConnector = true
    }

    func removeAllPartnerFilter(partner: String?) {
        didRemoveAllPartnerFilter = true
    }

    func removeAllPlugtypeFilter(plugType: String?) {
        didRemoveAllPlugTypeFilter = true
    }

    func appendPlugtypeFilter(plugType: String) {
        didAppendPlugTypeFilter = true
    }

    func appendPartnerFilter(partner: String) {
        didAppendPartnerFilter = true
    }

    func removeAllFilters() {
        didRemoveAllFilters = true
    }

    func updateConnectorSelectionVisibility(show: Bool) {
        didUpdateConnectorSelectionVisibility = true
    }

    func isMyDestinationEnabled() -> Bool {
        didMyDestinationEnabled = true
        return true
    }

    func fetchIonnaConsentDetails() async {
        didCallFetchIonnaConsent = true
    }

    func navigateToIonnaConsentScreen(termsAndConditions: String) {
        didCallNavigateToIonnaConsent = true
    }

    func shouldDisplayRegisterButton(for station: EvStation) -> Bool {
        didCallShouldDisplayTeslaRegisterButton = true
        return true
    }

    func shouldDisplayAdaptorDisclaimer(for evSource: String) -> Bool {
        didCallShouldDisplayAdaptorDisclaimer = true
        return true
    }
}
