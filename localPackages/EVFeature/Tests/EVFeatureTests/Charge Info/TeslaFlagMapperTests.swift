// Copyright © 2025 Toyota. All rights reserved.

import XCTest

@testable import EVFeature

final class TeslaFlagMapperTests: XCTestCase {
    private let sut = TeslaFlagMapper()

  func test_defaultState_isNotEligible_notRegistered() {
    let cis = ChargeInfoState.initial
    let (eligible, registered) = sut.map(cis)
    XCTAssertFalse(eligible, "By default, cis.isEvTesla is false")
    XCTAssertFalse(registered, "By default, cis.teslaConsentDetails isn't .success(true)")
  }

  func test_nilInput_returnsNotEligible_notRegistered() {
    let (eligible, registered) = sut.map(nil)
    XCTAssertFalse(eligible, "nil state should be treated as not eligible")
    XCTAssertFalse(registered, "nil state should be treated as not registered")
  }

  func test_eligible_butConsentNil_notRegistered() {
    var cis = ChargeInfoState.initial
    cis.isEvTesla = true
    cis.teslaConsentDetails = .success(nil, "any")
    let (eligible, registered) = sut.map(cis)
    XCTAssertTrue(eligible, "cis.isEvTesla=true means eligible")
    XCTAssertFalse(registered, "consent nil should map to false")
  }

  func test_eligible_andConsentTrue_registered() {
    var cis = ChargeInfoState.initial
    cis.isEvTesla = true
    cis.teslaConsentDetails = .success(true, "token")
    let (eligible, registered) = sut.map(cis)
    XCTAssertTrue(eligible)
    XCTAssertTrue(registered)
  }

  func test_notEligible_butConsentTrue_registered() {
    var cis = ChargeInfoState.initial
    cis.isEvTesla = false
    cis.teslaConsentDetails = .success(true, "token")
    let (eligible, registered) = sut.map(cis)
    XCTAssertFalse(eligible, "Even with consentSuccess, isEvTesla=false means not eligible")
    XCTAssertTrue(registered, "ConsentSuccess true still maps to registered=true")
  }

  func test_eligible_andConsentFalse_notRegistered() {
    var cis = ChargeInfoState.initial
    cis.isEvTesla = true
    cis.teslaConsentDetails = .success(false, "token")
    let (eligible, registered) = sut.map(cis)
    XCTAssertTrue(eligible, "cis.isEvTesla=true means eligible")
    XCTAssertFalse(registered, "consent false should map to not registered")
  }

  func test_eligible_andConsentLoading_notRegistered() {
    var cis = ChargeInfoState.initial
    cis.isEvTesla = true
    cis.teslaConsentDetails = .loading
    let (eligible, registered) = sut.map(cis)
    XCTAssertTrue(eligible)
    XCTAssertFalse(registered, "loading consent should map to not registered")
  }

  func test_eligible_andConsentNotAvailable_notRegistered() {
    var cis = ChargeInfoState.initial
    cis.isEvTesla = true
    cis.teslaConsentDetails = .notAvailable
    let (eligible, registered) = sut.map(cis)
    XCTAssertTrue(eligible)
    XCTAssertFalse(registered, "notAvailable consent should map to not registered")
  }
}
