// Copyright © 2025 Toyota. All rights reserved.

import XCTest

@testable import EVFeature

final class TeslaFlagMappingTests: XCTestCase {
    var mapperMock: TeslaFlagMapperMock!

    override func setUp() {
        super.setUp()
        self.mapperMock = TeslaFlagMapperMock()
    }

    override func tearDown() {
        super.tearDown()
        self.mapperMock = nil
    }

    func testMap() {
        mapperMock.map(nil)
        XCTAssertTrue(mapperMock.didCallMap)
    }

    func testChargeInfoStatePassed() {
        let expectedCis = ChargeInfoState(
            odometerState: .loading,
            fuelState: .loading,
            statisticsState: .loadinng,
            historyState: .loadinng,
            enrollmentState: .loadinng,
            cleanAssistDetails: .loading,
            ionnaConsentDetails: .loading,
            teslaConsentDetails: .loading,
            cleanAssistLCFS: .loading,
            showSetTimerOnVehicleToast: false,
            isPHEV: false,
            showEvGo: false,
            showWallet: false,
            isPublicChargingControlAllowed: false,
            isEvIonna: false,
            isEvTesla: false,
            isWallet: false
        )
        mapperMock.map(expectedCis)
        XCTAssertTrue(mapperMock.didCallMap)
        XCTAssertEqual(mapperMock.chargeInfoStatePassed?.isPHEV, expectedCis.isPHEV)
        XCTAssertEqual(mapperMock.chargeInfoStatePassed?.showEvGo, expectedCis.showEvGo)
        XCTAssertEqual(mapperMock.chargeInfoStatePassed?.showWallet, expectedCis.showWallet)
        XCTAssertEqual(
            mapperMock.chargeInfoStatePassed?.isPublicChargingControlAllowed,
            expectedCis.isPublicChargingControlAllowed
        )
        XCTAssertEqual(mapperMock.chargeInfoStatePassed?.isEvIonna, expectedCis.isEvIonna)
        XCTAssertEqual(mapperMock.chargeInfoStatePassed?.isEvTesla, expectedCis.isEvTesla)
        XCTAssertEqual(mapperMock.chargeInfoStatePassed?.isWallet, expectedCis.isWallet)
    }

    func testMap_shouldReturnCorrectValues() {
        let expectedResult = (true, false)
        mapperMock.tupleToReturn = expectedResult
        mapperMock.map(nil)
    }
}
