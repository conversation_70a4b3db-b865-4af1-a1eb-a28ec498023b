// Copyright © 2025 Toyota. All rights reserved.

import XCTest
import OneAppTest

@testable import EVFeature

final class CPOConsentTests: XCTestCase {
    var mockChargeInfo: ChargeInfoMocks!

    let allProviders: [CPOProvider] = [.tcn, .ionna]

    override func setUp() {
        super.setUp()
        self.mockChargeInfo = ChargeInfoMocks()
    }

    override func tearDown() {
        super.tearDown()
        self.mockChargeInfo = nil
    }

    func testGetConsentId() {
        for provider in allProviders {
            let consentId = provider.consentCode
            XCTAssertFalse(consentId.isEmpty, "Consent ID for \(provider.rawValue) should not be empty")
            XCTAssertEqual(consentId, expectedConsentCode(for: provider),
                          "Consent ID for \(provider.rawValue) should match expected value")
        }
    }

    private func expectedConsentCode(for provider: CPOProvider) -> String {
        switch provider {
        case .tcn, .ionna:
            return "A20"
        }
    }

    func testFetchConsentDetails() async {
        for provider in allProviders {
            mockChargeInfo.fetchedConsents = []
            await mockChargeInfo.fetchConsents(for: provider)
            XCTAssertTrue(mockChargeInfo.fetchedConsents.contains(provider),
                          "fetchedConsents should contain \(provider.rawValue)")
            verifyConsentDetails(provider)
        }
    }

    func testAcceptConsentTerms() async {
        for provider in allProviders {
            mockChargeInfo.acceptedConsents = []
            await mockChargeInfo.acceptConsentTerms(true, for: provider)
            XCTAssertTrue(mockChargeInfo.acceptedConsents.contains {
                $0.provider == provider && $0.accepted == true
            }, "acceptedConsents should contain accepted consent for \(provider.rawValue)")
            verifyConsentAccepted(provider)
        }
    }

    func testRejectConsentTerms() async {
        for provider in allProviders {
            mockChargeInfo.acceptedConsents = []
            await mockChargeInfo.acceptConsentTerms(false, for: provider)
            XCTAssertTrue(mockChargeInfo.acceptedConsents.contains {
                $0.provider == provider && $0.accepted == false
            }, "acceptedConsents should contain rejected consent for \(provider.rawValue)")
            verifyConsentRejected(provider)
        }
    }

    func testShowConsentScreen() {
        for provider in allProviders {
            mockChargeInfo.shownConsentScreens = []
            let termsText = "\(provider.rawValue) consent terms"
            mockChargeInfo.showConsentScreen(termsAndConditions: termsText, for: provider)
            XCTAssertTrue(mockChargeInfo.shownConsentScreens.contains {
                $0.provider == provider && $0.terms == termsText
            }, "shownConsentScreens should contain entry for \(provider.rawValue)")
        }
    }

    func testInitialStateHasProvidersDisabled() {
        let initialState = ChargeInfoState.initial
        XCTAssertFalse(initialState.isEvTesla, "Tesla feature should be disabled in initial state")
        XCTAssertFalse(initialState.isEvIonna, "Ionna feature should be disabled in initial state")
    }

    func testProviderFlagsCanBeEnabled() {
        var state = ChargeInfoState.initial
        state.isEvTesla = true
        XCTAssertTrue(state.isEvTesla, "Tesla feature flag should be enabled after setting to true")
        state.isEvIonna = true
        XCTAssertTrue(state.isEvIonna, "Ionna feature flag should be enabled after setting to true")
    }

    func testLoadInitializesProviderStates() async {
        await mockChargeInfo.load()
        XCTAssertTrue(mockChargeInfo.statePublished.isEvTesla,
                    "Tesla feature flag should be enabled after load")
        verifyConsentDetails(.tcn)
        XCTAssertTrue(mockChargeInfo.statePublished.isEvIonna,
                    "Ionna feature flag should be enabled after load")
        verifyConsentDetails(.ionna)
    }

    func testProviderStateIndependence() {
        var state = ChargeInfoState.initial
        state.teslaConsentDetails = .success(true, "Tesla terms")
        state.ionnaConsentDetails = .success(false, "Ionna terms")
        if case .success(let teslaAccepted, let teslaTerms) = state.teslaConsentDetails,
           case .success(let ionnaAccepted, let ionnaTerms) = state.ionnaConsentDetails {
            XCTAssertTrue(teslaAccepted ?? false, "Tesla consent should be accepted")
            XCTAssertFalse(ionnaAccepted ?? true, "Ionna consent should be rejected")
            XCTAssertEqual(teslaTerms, "Tesla terms")
            XCTAssertEqual(ionnaTerms, "Ionna terms")
        } else {
            XCTFail("Expected success states for both Tesla and Ionna")
        }
    }

    func testIonnaSpecificMethods() async {
        await mockChargeInfo.fetchIonnaConsentDetails()
        XCTAssertTrue(mockChargeInfo.didFetchIonnaConsentDetails)
        XCTAssertTrue(mockChargeInfo.fetchedConsents.contains(.ionna))
        await mockChargeInfo.acceptIonnaTerms(true)
        XCTAssertTrue(mockChargeInfo.didAcceptIonnaTerms)
        XCTAssertTrue(mockChargeInfo.acceptedConsents.contains { $0.provider == .ionna && $0.accepted == true })
        mockChargeInfo.navigateToIonnaConsentScreen(termsAndConditions: "Ionna terms")
        XCTAssertTrue(mockChargeInfo.didNavigateToIonnaConsentScreen)
        XCTAssertTrue(mockChargeInfo.shownConsentScreens.contains { $0.provider == .ionna })
    }

    private func verifyConsentDetails(_ provider: CPOProvider) {
        switch provider {
        case .tcn:
            switch mockChargeInfo.statePublished.teslaConsentDetails {
            case .notAvailable, .loading:
                XCTFail("\(provider.rawValue) consent details should be available")
            case .success(let isAccepted, let termsAndConditions):
                XCTAssertFalse(isAccepted ?? true, "\(provider.rawValue) consent should not be accepted by default")
                XCTAssertEqual(termsAndConditions?.lowercased(), "\(provider.rawValue) consent terms and conditions")
            }
        case .ionna:
            switch mockChargeInfo.statePublished.ionnaConsentDetails {
            case .notAvailable, .loading:
                XCTFail("\(provider.rawValue) consent details should be available")
            case .success(let isAccepted, let termsAndConditions):
                XCTAssertFalse(isAccepted ?? true, "\(provider.rawValue) consent should not be accepted by default")
                XCTAssertEqual(termsAndConditions?.lowercased(), "\(provider.rawValue) consent terms and conditions")
            }
        }
    }

    private func verifyConsentAccepted(_ provider: CPOProvider) {
        switch provider {
        case .tcn:
            switch mockChargeInfo.statePublished.teslaConsentDetails {
            case .notAvailable, .loading:
                XCTFail("\(provider.rawValue) consent details should be available")
            case .success(let isAccepted, _):
                XCTAssertTrue(isAccepted ?? false, "\(provider.rawValue) consent should be accepted")
            }
        case .ionna:
            switch mockChargeInfo.statePublished.ionnaConsentDetails {
            case .notAvailable, .loading:
                XCTFail("\(provider.rawValue) consent details should be available")
            case .success(let isAccepted, _):
                XCTAssertTrue(isAccepted ?? false, "\(provider.rawValue) consent should be accepted")
            }
        }
    }

    private func verifyConsentRejected(_ provider: CPOProvider) {
        switch provider {
        case .tcn:
            switch mockChargeInfo.statePublished.teslaConsentDetails {
            case .notAvailable, .loading:
                XCTFail("\(provider.rawValue) consent details should be available")
            case .success(let isAccepted, _):
                XCTAssertFalse(isAccepted ?? true, "\(provider.rawValue) consent should be rejected")
            }
        case .ionna:
            switch mockChargeInfo.statePublished.ionnaConsentDetails {
            case .notAvailable, .loading:
                XCTFail("\(provider.rawValue) consent details should be available")
            case .success(let isAccepted, _):
                XCTAssertFalse(isAccepted ?? true, "\(provider.rawValue) consent should be rejected")
            }
        }
    }
}
