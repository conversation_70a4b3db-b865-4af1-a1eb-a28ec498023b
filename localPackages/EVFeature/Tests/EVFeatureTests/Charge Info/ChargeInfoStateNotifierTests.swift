// Copyright © 2025 Toyota. All rights reserved.

import XCTest
import OneAppTest
import NetworkClients
@testable import EVFeature

final class ChargeInfoStateNotifierTests: XCTestCase {
    var logic: ChargeInfoMocks!

    override func setUp() {
        super.setUp()
        logic = ChargeInfoMocks()
    }

    override func tearDown() {
        super.tearDown()
        logic = nil
    }

    func testStatePublisherExists() {
        let cancellable = logic.state.sink { state in
            XCTAssertNotNil(state)
        }
        cancellable.cancel()
    }

    func testShowSetTimerOnVehicleToastToggle() {
        let initialValue = logic.statePublished.showSetTimerOnVehicleToast
        XCTAssertFalse(initialValue)
        XCTAssertTrue(true)
    }
}
