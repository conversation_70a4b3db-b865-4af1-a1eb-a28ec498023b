// Copyright © 2025 Toyota. All rights reserved.

import Foundation
@testable import EVFeature

class TeslaFlagMapperMock: TeslaFlagMapping {
    private(set) var didCallMap = false
    private(set) var chargeInfoStatePassed: ChargeInfoState?
    var tupleToReturn: (<PERSON><PERSON>, <PERSON>ol) = (false, false)

    func map(_ cis: ChargeInfoState?) -> (Bool, Bool) {
        didCallMap = true
        chargeInfoStatePassed = cis
        return tupleToReturn
    }

    func reset() {
        didCallMap = false
        chargeInfoStatePassed = nil
    }
}
