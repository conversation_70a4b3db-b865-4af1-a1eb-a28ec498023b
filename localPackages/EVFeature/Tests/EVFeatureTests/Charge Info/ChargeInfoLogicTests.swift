// Copyright © 2024 Toyota. All rights reserved.

import XCTest
import OneAppTest

@testable import EVFeature

final class ChargeInfoLogicTests: XCTestCase {
    var logic: ChargeInfoMocks!

    override func setUp() {
        super.setUp()
        self.logic = ChargeInfoMocks()
    }

    override func tearDown() {
        super.tearDown()
        self.logic = nil
    }

    func testNavigateToManualSchedule() {
        logic.navigateToManualSchedule()
        XCTAssertTrue(logic.didCallNavigateToManualSchedule)
    }

    func testNavigateToChargeAssistSchedule() {
        logic.navigateToChargeAssistScheduleScreen()
        XCTAssertTrue(logic.didCallNavigateToChargeAssistSchedule)
    }

    func testNavigateToCreateModifyScheduleScreen() {
        logic.navigateToCreateModifyScheduleScreen()
        XCTAssertTrue(logic.didNavToCreateModScheduleScreen)
    }

    func testLogStatisticsFilterTap() {
        logic.logStatisticsFilterTap()
        XCTAssertTrue(logic.didLogStatisticsFilte)
    }

    func testLogStatisticsLearnMore() {
        logic.logStatisticsLearnMore()
        XCTAssertTrue(logic.didLogStatisticsLearnMore)
    }

    func testFetchEnrollmentCheck() async {
        await logic.fetchEnrollmentCheck()
        XCTAssertTrue(logic.didCallFetchEnrollmentCheck)
    }

    func testUpdateFuelState() {
        logic.updateFuelState()
        XCTAssertTrue(logic.didUpdateFuelState)
    }

    func testUpdateElectricFuelState() {
        logic.updateElectricFuelState()
        XCTAssertTrue(logic.didUpdateEletricFuelState)
    }

    func testUpdatePHEVState() {
        logic.updatePHEVState()
        XCTAssertTrue(logic.didUpdatePHEVState)
    }

    func testUpdateOdometerState() {
        logic.updateOdometerState()
        XCTAssertTrue(logic.didUpdateOdometerState)
    }
}
