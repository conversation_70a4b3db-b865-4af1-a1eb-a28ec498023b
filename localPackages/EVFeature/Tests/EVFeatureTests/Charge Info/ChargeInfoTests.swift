// Copyright © 2024 Toyota. All rights reserved.

import XCTest
import OneAppTest

@testable import EVFeature

final class ChargeInfoTests: XCTestCase {
    var logic: ChargeInfoMocks!

    override func setUp() {
        super.setUp()
        self.logic = ChargeInfoMocks()
    }

    override func tearDown() {
        super.tearDown()
        self.logic = nil
    }

    func testStatistics() async {
        await logic.load()
        XCTAssertTrue(logic.didCallLoad)
        switch logic.statePublished.statisticsState {
        case .loadinng, .notAvailable:
            break
        case .success(let stats):
            if let lastCharge = stats.lastCharge {
                XCTAssertEqual(lastCharge.chargeLocationType, "Public")
                XCTAssertEqual(lastCharge.endTime, "2024-07-10T00:15:59Z")
                XCTAssertEqual(lastCharge.startTime, "2024-07-09T19:43:57Z")
                XCTAssertEqual(lastCharge.socAfterCharging, "63%")
                XCTAssertEqual(lastCharge.socBeforeCharging, "57%")
                XCTAssertEqual(lastCharge.totalChargeKwhr, "5.328")
                XCTAssertEqual(lastCharge.address, "6565 headquarters drive, Plano TX 75024")
                XCTAssertEqual(lastCharge.latitude, "33.146935")
                XCTAssertEqual(lastCharge.longitude, "-96.88672")

                if let watttimeDetails = lastCharge.watttimeDetails {
                    XCTAssertEqual(watttimeDetails.chargeClasification, EVChargeClasification.nonEcho)
                    XCTAssertEqual(watttimeDetails.co2Value, "10")
                    XCTAssertEqual(watttimeDetails.healthDollarValue, "200")
                    XCTAssertEqual(watttimeDetails.region, "en-US")
                }
            }

            if let currentWeekReport = stats.currentWeekReport {
                XCTAssertEqual(currentWeekReport.leafCount, "5")
                XCTAssertEqual(currentWeekReport.chargingCount, "40")
                XCTAssertEqual(currentWeekReport.totalChargeKwhr, 10_000)
            }
        }
    }

    func testChargeHistory() async {
        await logic.load()
        XCTAssertTrue(logic.didCallLoad)
        switch logic.statePublished.historyState {
        case .loadinng, .notAvailable:
            return
        case .success(let history):
            XCTAssertEqual(history.vin, "**********")
            if let lastCharge = history.chargingSessions?.first {
                XCTAssertEqual(lastCharge.chargeLocationType, "Public")
                XCTAssertEqual(lastCharge.endTime, "2024-07-10T00:15:59Z")
                XCTAssertEqual(lastCharge.startTime, "2024-07-09T19:43:57Z")
                XCTAssertEqual(lastCharge.socAfterCharging, "63%")
                XCTAssertEqual(lastCharge.socBeforeCharging, "57%")
                XCTAssertEqual(lastCharge.totalChargeKwhr, "5.328")
                XCTAssertEqual(lastCharge.address, "6565 headquarters drive, Plano TX 75024")
                XCTAssertEqual(lastCharge.latitude, "33.146935")
                XCTAssertEqual(lastCharge.longitude, "-96.88672")

            }
        }
    }

    func testEnrollmentCheck() async {
        await logic.load()
        XCTAssertTrue(logic.didCallLoad)
        switch logic.statePublished.enrollmentState {
        case .loadinng, .notAvailable:
            return
        case .success(let enrollmentCheck):
            XCTAssertEqual(enrollmentCheck.messages?.responseCode, 200)
            XCTAssertEqual(enrollmentCheck.messages?.description, "Success")
            XCTAssertEqual(enrollmentCheck.payload?.showAssociatedBanners, "skip")
            XCTAssertEqual(enrollmentCheck.payload?.partnerEnrollment?.wallet, "Found")
            XCTAssertEqual(enrollmentCheck.payload?.partnerEnrollment?.guid, "123")
            if let evgo = enrollmentCheck.payload?.partnerEnrollment?.partnerStatus?.first(where: {
                $0.partnerName == "EVgo"
            }) {
                XCTAssertEqual(evgo.status, "Found")
                XCTAssertEqual(evgo.enrollmentDate, "20240510")
                XCTAssertEqual(evgo.expiryDate, "20250510")
            }
            if let chargePoint = enrollmentCheck.payload?.partnerEnrollment?.partnerStatus?.first(where: {
                $0.partnerName == "ChargePoint"
            }) {
                XCTAssertEqual(chargePoint.status, "Found")
                XCTAssertEqual(chargePoint.enrollmentDate, "20240505")
                XCTAssertEqual(chargePoint.expiryDate, "")
            }
        }
    }

    func testCleanAssistElegibility() async {
        await logic.load()
        XCTAssertTrue(logic.didCallLoad)
        if let lcfsOptIn = logic.statePublished.cleanAssistEnrollmentState?.lcfsOptIn {
            XCTAssertEqual(lcfsOptIn, "In")
        } else {
            assertionFailure()
        }
        if let lcfsEligible = logic.statePublished.cleanAssistEnrollmentState?.lcfsEligible {
            XCTAssertTrue(lcfsEligible)
        } else {
            assertionFailure()
        }
    }

    func testPublicChargingSettingsElegibility() async {
        await logic.load()
        XCTAssertTrue(logic.didCallLoad)
        XCTAssertTrue((logic.statePublished.showChargingSettings != nil))
    }

    func testGetWattTimeUrl() {
        _ = logic.getWattTimeUrl()
        XCTAssertTrue(logic.didGetWattTimeUrl)
    }

    func testFetchIonnaConsentDetails() async {
        await logic.fetchIonnaConsentDetails()
        XCTAssertTrue(logic.didFetchIonnaConsentDetails)
    }

    func testAcceptIonnaTerms() async {
        await logic.acceptIonnaTerms(true)
        XCTAssertTrue(logic.didAcceptIonnaTerms)
    }

    func testNavigateToIonnaConsentScreen() {
        logic.navigateToIonnaConsentScreen(termsAndConditions: "Sample Terms and Conditions")
        XCTAssertTrue(logic.didNavigateToIonnaConsentScreen)
    }

    func testGetIonnaButtonTitle() {
        _ = logic.getIonnaButtonTitle(acknowledged: true)
        XCTAssertTrue(logic.didGetIonnaButtonTitle)
    }
}
