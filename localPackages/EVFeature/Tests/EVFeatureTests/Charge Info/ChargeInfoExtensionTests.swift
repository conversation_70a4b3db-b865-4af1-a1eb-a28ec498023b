// Copyright © 2025 Toyota. All rights reserved.

import XCTest
import OneAppTest

@testable import EVFeature

final class ChargeInfoExtensionTests: XCTestCase {
    var logic: ChargeInfoMocks!

    override func setUp() {
        super.setUp()
        logic = ChargeInfoMocks()
    }

    override func tearDown() {
        super.tearDown()
        logic = nil
    }

    func testObserveVehicle() {
        logic.observeVehicle()
        XCTAssertTrue(logic.didCallObserveVehicle)
    }

    func testLoad() async {
        await logic.load(polling: false)
        XCTAssertTrue(logic.didCallLoad)
    }

    func testFetchStatistics() async {
        await logic.fetchStatistics()
        XCTAssertTrue(logic.didCallFetchStatistics)
    }

    func testFetchChargeHistory() async {
        await logic.fetchChargeHistory()
        XCTAssertTrue(logic.didCallFetchChargeHistory)
    }

    func testAcceptCleanAssistTerms_UserAccepted() async {
        await logic.acceptCleanAssistTerms(true)
        XCTAssertTrue(logic.didCallAcceptCleanAssistTerms)
    }

    func testAcceptCleanAssistTerms_UserDeclined() async {
        await logic.acceptCleanAssistTerms(false)
        XCTAssertTrue(logic.didCallAcceptCleanAssistTerms)
    }

    func testNavigateToRegisterPartnerScreen() {
        logic.navigateToRegisterPartnerScreen(partner: "evgo")
        XCTAssertTrue(logic.didCallNavigateToRegisterPartnerScreen)
    }

    func testFetchLCFSDashboard() async {
        await logic.fetchLCFSDashboard()
        XCTAssertTrue(logic.didCallFetchLCFSDashboard)
    }

    func testGetBrandName() {
        _ = logic.getBrandName()
        XCTAssertTrue(logic.didCallGetBrandName)
    }

    func testDismissPopup() {
        logic.dismissPopup()
        XCTAssertTrue(logic.didCallDismissPopup)
    }

    func testNavigateToWallet() {
        logic.navigateToWallet(isSetupWallet: true)
        XCTAssertTrue(logic.didCallNavigateToWallet)
    }

    // MARK: - Test showEvgoComplimentaryPopup

    func testShowEvgoComplimentaryPopup() {
        logic.showEvgoComplimentaryPopup(expiryDays: 15, walletSetup: true)
        XCTAssertTrue(logic.didCallShowEvgoComplimentaryPopup)
    }

    // MARK: - Test getLastUpdatedDate

    func testGetLastUpdatedDate() {
        _ = logic.getLastUpdatedDate()
        XCTAssertTrue(logic.didCallGetLastUpdatedDate)
    }

    func testNavigateToCreateScheduleScreen() {
        logic.navigateToCreateScheduleScreen(isDeparture: true)
        XCTAssertTrue(logic.didCallNavigateToCreateScheduleScreen)
    }

    func testNavigateToCreateModifyScheduleScreen() {
        logic.navigateToCreateModifyScheduleScreen()
        XCTAssertTrue(logic.didNavToCreateModScheduleScreen)
    }

    func testFetchCleanAssistDetails() async {
        await logic.fetchCleanAssistDetails()
        XCTAssertTrue(logic.didCallFetchCleanAssistDetails)
    }

    func testFetchIonnaConsentDetails() async {
        await logic.fetchIonnaConsentDetails()

        XCTAssertTrue(logic.didFetchIonnaConsentDetails)
    }

    // MARK: - Test acceptIonnaTerms

    func testAcceptIonnaTerms() async {
        await logic.acceptIonnaTerms(true)

        XCTAssertTrue(logic.didAcceptIonnaTerms)
    }

    // MARK: - Test navigateToIonnaConsentScreen

    func testNavigateToIonnaConsentScreen() {
        logic.navigateToIonnaConsentScreen(termsAndConditions: "Sample Terms")

        XCTAssertTrue(logic.didNavigateToIonnaConsentScreen)
    }

    func testGetIonnaButtonTitle() {
        _ = logic.getIonnaButtonTitle(acknowledged: true)

        XCTAssertTrue(logic.didGetIonnaButtonTitle)
    }

    func testLogStatisticsFilterTap() {
        logic.logStatisticsFilterTap()

        XCTAssertTrue(logic.didLogStatisticsFilte)
    }

    func testLogStatisticsLearnMore() {
        logic.logStatisticsLearnMore()

        XCTAssertTrue(logic.didLogStatisticsLearnMore)
    }

    func testGetWattTimeUrl() {
        _ = logic.getWattTimeUrl()

        XCTAssertTrue(logic.didGetWattTimeUrl)
    }
}
