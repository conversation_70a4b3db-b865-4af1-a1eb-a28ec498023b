// Copyright © 2024 Toyota. All rights reserved.

import Foundation

public extension Strings {
    // swiftlint:disable line_length
    enum ChargeInfo {
        public static var estimateRangeText: String {
            String(localized: "ev.estimateRangeText",
                   defaultValue: "est. range without A/C",
                   bundle: bundle,
                   comment: "estimate Range Text")
        }
        public static var noScheduleChargingText: String {
            String(localized: "ev.noScheduleChargingText",
                   defaultValue: "No scheduled charging",
                   bundle: bundle,
                   comment: "no Schedule Charging Text")
        }
        public static var scheduleEcoChargingText: String {
            String(localized: "ev.scheduleEcoChargingText",
                   defaultValue: "Manage vehicle’s ECO charging modes and set specific charging times.",
                   bundle: bundle,
                   comment: "schedule Eco Charging Text")
        }
        public static var chargeSettingsText: String {
            String(localized: "ev.chargeSettingsText",
                   defaultValue: "Charging Station Settings",
                   bundle: bundle,
                   comment: "charge Settings Text")
        }
        public static var gasRange: String {
            String(localized: "ev.gasRange",
                   defaultValue: "HV range",
                   bundle: bundle,
                   comment: "HV range")
        }
        public static var evRange: String {
            String(localized: "ev.evRange",
                   defaultValue: "EV range",
                   bundle: bundle,
                   comment: "EV range")
        }
        public static var ecoCharge: String {
            String(localized: "ev.ecoCharge",
                   defaultValue: "ECO Charge",
                   bundle: bundle,
                   comment: "ECO Charge title")
        }
        public static var whatIsEcoCharge: String {
            String(localized: "ev.whatIsEcoCharge",
                   defaultValue: "What is ECO Charge?",
                   bundle: bundle,
                   comment: "What is ECO Charge?")
        }
        public static var chargePointRegistered: String {
            String(localized: "ev.chargePointRegistered",
                   defaultValue: "Registered",
                   bundle: bundle,
                   comment: "Registered text")
        }
        public static var learnMore: String {
            String(localized: "ev.learnMore",
                   defaultValue: "Learn More",
                   bundle: bundle,
                   comment: "learn more button text")
        }
        public static var endTime: String {
            String(localized: "ev.endTime",
                   defaultValue: "End Time",
                   bundle: bundle,
                   comment: "End Time Text")
        }
        public static var offPeakHours: String {
            String(localized: "ev.offPeakHours",
                   defaultValue: "Off-Peak Hours",
                   bundle: bundle,
                   comment: "Off Peak Hours Text")
        }
        public static var offpeakSchedule: String {
            String(localized: "ev.offpeakSchedule",
                   defaultValue: "Off-Peak Schedule",
                   bundle: bundle,
                   comment: "Off Peak Schedule Text")
        }
        public static var ecoCharging: String {
            String(localized: "ev.ecoCharging",
                   defaultValue: "ECO Charging",
                   bundle: bundle,
                   comment: "ECO Charging Text")
        }

        public static var daysOfTheWeek: String {
            String(localized: "ev.daysOfTheWeek",
                   defaultValue: "Days of Week",
                   bundle: bundle,
                   comment: "Days of Week text")
        }
        public static var noSavedHomeAddressHeadingText: String {
            String(localized: "ev.noSavedHomeAddressHeadingText",
                   defaultValue: "No saved home address",
                   bundle: bundle,
                   comment: "No saved home address Heading Text")
        }
        public static var noSavedHomeAddressSubHeadingText: String {
            String(
                localized: "ev.noSavedHomeAddressSubHeadingText",
                defaultValue: "We could not determine if this was charged at home or in the public. Please go to user profile and enter your home address.",
                bundle: bundle,
                comment: "No saved home address SubHeading Text"
            )
        }
        public static var evmcDepartureTimeInfoText: String {
            String(localized: "ev.evmcDepartureTimeInfoText",
                   defaultValue: "Réglez l'heure à laquelle vous souhaitez que votre véhicule arrête de se recharger",
                   bundle: bundle,
                   comment: "evmc Departure Time Info Text")
        }
        public static var offpeakDescription: String {
            String(localized: "ev.offpeakDescription",
                   defaultValue: "Off-peak vehicle charging between ",
                   bundle: bundle,
                   comment: "off-peak Description")
        }
        public static var and: String {
            String(localized: "ev.and",
                   defaultValue: "and",
                   bundle: bundle,
                   comment: "and text")
        }
        public static var findOutMore: String {
            String(localized: "ev.findOutMore",
                   defaultValue: "Find out more",
                   bundle: bundle,
                   comment: "Find out more text")
        }
        public static var noSchedulesTitle: String {
            String(localized: "ev.noSchedulesTitle",
                   defaultValue: "No charging schedules yet",
                   bundle: bundle,
                   comment: "no Schedules Title")
        }
        public static var noCreatedSchedules: String {
            String(localized: "ev.noCreatedSchedules",
                   defaultValue: "You have no created schedules.",
                   bundle: bundle,
                   comment: "No created schedules description")
        }
        public static var noSchedulesSubtitle: String {
            String(localized: "ev.noSchedulesSubtitle",
                   defaultValue: "Set charging schedules and control when you charge your vehicle.",
                   bundle: bundle,
                   comment: "no Schedules Subtitle")
        }
        public static var phevNoSchedulesSubtitle: String {
            String(localized: "ev.phevNoSchedulesSubtitle",
                   defaultValue: "To use ECO charging or in-app schedule charging, you need to set the schedule in the vehicle head unit first. The schedule will sync with the app after the set up.",
                   bundle: bundle,
                   comment: "no Schedules Subtitle for PHEV")
        }
        public static var onText: String {
            String(localized: "ev.onText",
                   defaultValue: "On",
                   bundle: bundle,
                   comment: "On text")
        }
        public static var offText: String {
            String(localized: "ev.offText",
                   defaultValue: "Off",
                   bundle: bundle,
                   comment: "Off text")
        }
        public static var evmcChargeSchedulePageTitleText: String {
            String(localized: "ev.evmcChargeSchedulePageTitleText",
                   defaultValue: "Charge Schedule",
                   bundle: bundle,
                   comment: "Charge ScheduleTile text")
        }
        public static var evmcNewSchedulePageTitleText: String {
            String(localized: "ev.evmcNewSchedulePageTitleText",
                   defaultValue: "New Schedule",
                   bundle: bundle,
                   comment: "New ScheduleTile text")
        }
        public static var publicText: String {
            String(localized: "ev.publicText",
                   defaultValue: "Public",
                   bundle: bundle,
                   comment: "Public text")
        }
        public static var homeText: String {
            String(localized: "ev.home",
                   defaultValue: "Home”",
                   bundle: bundle,
                   comment: "Home text")
        }
        public static var chargeInfo: String {
            String(localized: "ev.chargeInfo",
                   defaultValue: "Charge Info",
                   bundle: bundle,
                   comment: "Charge Info text")
        }
        public static var evgo: String {
            String(localized: "ev.evgo", defaultValue: "EVgo", bundle: bundle, comment: "EVgo text")
        }
        public static var ionna: String {
            String(localized: "ev.ionna", defaultValue: "IONNA", bundle: bundle, comment: "Ionna text")
        }
        public static var tesla: String {
            String(localized: "ev.tesla", defaultValue: "Tesla", bundle: bundle, comment: "Tesla text")
        }
        public static var chargePoint: String {
            String(localized: "ev.chargePoint", defaultValue: "ChargePoint",
                   bundle: bundle, comment: "chargepoint text")
        }
        public static var termsAndPrivacy: String {
            String(localized: "ev.termsAndPrivacy",
                   defaultValue: "Terms & Privacy",
                   bundle: bundle,
                   comment: "terms And Privacy")
        }

        public static var offPeakChargingTimes: String {
            String(localized: "ev.offPeakChargingTimes",
            defaultValue: "Off-peak vehicle charging between %0 and %1.",
            bundle: bundle,
            comment: "Off-peak Charging times")
        }

        public static var ecoEarnedLeafText: String {
            String(localized: "ev.ecoEarnedLeafText",
                   defaultValue: "Earned Leaf",
                   bundle: bundle,
                   comment: "ECO Earned Leaf Text")
        }
        public static var ecoNotEligibleText: String {
            String(localized: "ev.ecoNotEligibleText",
                   defaultValue: "Not eligible",
                   bundle: bundle,
                   comment: "ECO Not Eligible Text")
        }
        public static var ecoPendingText: String {
            String(localized: "ev.ecoPendingText",
                   defaultValue: "Pending",
                   bundle: bundle,
                   comment: "ECO Pending Text")
        }
        public static var wattTimeText: String {
            String(localized: "ev.wattTimeText",
                   defaultValue: "WattTime",
                   bundle: bundle,
                   comment: "WattTime Text")
        }
    }

    // MARK: - Environmental Impact
    enum ChargeLocationInfo {
        public static var unKnownLocationHeadingText: String {
            String(localized: "ev.unKnownLocationHeadingText",
                   defaultValue: "Unknown location",
                   bundle: bundle,
                   comment: "unKnown Location Heading Text")
        }
        public static var unKnownLocationSubHeadingText: String {
            String(localized: "ev.unKnownLocationSubHeadingText",
                   defaultValue: "Please go to user profile and enter your home address.",
                   bundle: bundle,
                   comment: "unKnown Location SubHeading Text")
        }
        public static var unKnownLocationSubHeading1Text: String {
            String(localized: "ev.unKnownLocationSubHeading1Text",
                   defaultValue: "Once added, we will be able to determine the location of your future charge sessions as home or public.",
                   bundle: bundle,
                   comment: "unKnown Location SubHeading 1 Text")
        }
        public static var accountSettings: String {
            String(localized: "ev.accountSettings",
                   defaultValue: "Go to Account Settings",
                   bundle: bundle,
                   comment: "Account Setting Text Text")
        }
        public static var closeText: String {
            String(localized: "ev.close",
                   defaultValue: "Close",
                   bundle: bundle,
                   comment: "Close Text")
        }
    }

    // MARK: - Environmental Impact
    enum EnvironmentalImpact {
        public static var estimatedCO2EmissionsSaved: String {
            String(localized: "ev.EstimatedCO2EmissionsSaved",
                   defaultValue: "Estimated CO2 emissions saved",
                   bundle: bundle,
                   comment: "Estimated CO2 emissions saved text")
        }
        public static var co2Avoided: String {
            String(localized: "ev.co2Avoided",
                   defaultValue: "By using ECO Charge, you’ve avoided an estimated %0 lbs of CO2 from being emitted by power plants, which is equivalent to %1 urban tree seedlings being grown for 10 years.",
                   bundle: bundle,
                   comment: "co2 avoided text")
        }
        public static var treesEquivalence: String {
            String(localized: "ev.TreesEquivalence",
                   defaultValue: "Trees Equivalence",
                   bundle: bundle,
                   comment: "Trees Equivalence text")
        }
        public static var healthImpactReduction: String {
            String(localized: "ev.healthImpactReduction",
                   defaultValue: "Potential Health Impact Reduction",
                   bundle: bundle,
                   comment: "health Impact Reduction text")
        }
    }

    // MARK: - Eco Charging Achievements
    enum EcoChargingAchievements {
        public static var earnedThroughECOCharging: String {
            String(localized: "ev.earnedthroughECOCharging",
                   defaultValue: "%0 of %1 leaves earned through ECO Charging.",
                   bundle: bundle,
                   comment: "earned through ECO Charging text")
        }

        public static var cumulativeChargingBehavior: String {
            String(localized: "ev.cumulativeChargingBehavior",
                   defaultValue: "Based on your cumulative charging behavior this month, charging your vehicle at cleaner times is estimated to be %0% better for local health outcomes in comparison to previous month’s behavior.",
                   bundle: bundle,
                   comment: "cumulative charging behavior text")
        }
        public static var ecoChargingAchievementText: String {
            String(localized: "ev.ecoChargingAchievementText",
                   defaultValue: "You’ve achieved %0 out of 7 possible leaves this week.",
                   bundle: bundle,
                   comment: "eco Charging Achievement Text")
        }
        public static var monthlyChallenge: String {
            String(localized: "ev.MonthlyChallenge",
                   defaultValue: "Monthly challenge",
                   bundle: bundle,
                   comment: "Monthly challenge text")
        }
    }
}
