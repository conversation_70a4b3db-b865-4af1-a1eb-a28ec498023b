/* Biometry_faceid_failed_enter_passcode */
"Biometric.Biometry_faceid_failed_enter_passcode" = "Face ID is locked because of too many failed attempts. Enter passcode to unlock.";

/* Biometry_faceid_failed_reason */
"Biometric.Biometry_faceid_failed_reason" = "Face ID does not recognize your face. Please try again.";

/* Biometry_faceid_not_enrolled */
"Biometric.Biometry_faceid_not_enrolled" = "There is no face enrolled in the device. Please go to <PERSON>ce Settings and enroll your face.";

/* Biometry_login_to_OneApp */
"Biometric.Biometry_login_to_OneApp" = "Please authenticate to proceed";

/* Biometry_not_available_note */
"Biometric.Biometry_not_available_note" = "Biometric authentication is not available for this device.";

/* Biometry_set_passcode_to_use_faceid */
"Biometric.Biometry_set_passcode_to_use_faceid" = "Please set device passcode to use Face ID for authentication.";

/* Biometry_set_passcode_to_use_touchid */
"Biometric.Biometry_set_passcode_to_use_touchid" = "Please set device passcode to use Touch ID for authentication.";

/* Biometry_touchid_failed_enter_passcode */
"Biometric.Biometry_touchid_failed_enter_passcode" = "Touch ID is locked because of too many failed attempts. Enter passcode to unlock.";

/* Biometry_touchid_failed_reason */
"Biometric.Biometry_touchid_failed_reason" = "Touch ID does not recognize your fingerprint. Please try again.";

/* Biometry_touchid_not_enrolled */
"Biometric.Biometry_touchid_not_enrolled" = "There are no fingerprints enrolled in the device. Please go to Device Settings and enroll your fingerprint.";

/* DKPopup CtaHeaderLabel */
"DKPopup.ConnectVehicleCtaHeaderLabel" = "Manage Key";

/* DKPopup CtaTitleLabel */
"DKPopup.ConnectVehicleCtaTitleLabel" = "Connect to Vehicle";

/* DKPopup Description */
"DKPopup.ConnectVehicleDescription" = "Use your mobile device as a key to start the engine and lock/unlock your vehicle. You can share a Digital Key with others and manage their access.";

/* DKPopup Image Description */
"DKPopup.ConnectVehicleImageDescription" = "Start, lock, unlock your vehicle and share a key with others.";

/* DKPopup Image Header */
"DKPopup.ConnectVehicleImageHeader" = "Your phone is your key";

/* DKPopup Title */
"DKPopup.ConnectVehicleTitle" = "Digital key";

/* DKPopup CtaTitleLabel */
"DKPopup.DKErrorNotDownloadedCtaTitleLabel" = "Try Again";

/* DKPopup Description */
"DKPopup.DKErrorNotDownloadedDescription" = "Confirm if your mobile device has sufficient battery charge, an internet or cellular connection and Bluetooth is on. If you continue to see this message, please try again later.";

/* DKPopup Title */
"DKPopup.DKErrorNotDownloadedTitle" = "Digital Key not downloaded";

/* DKPopup Description */
"DKPopup.DKNoInternetDescription" = "There’s no internet connection. Please check your connection and try again.";

/* DKPopup Shared TitleDKPopup Shared Title */
"DKPopup.DKSharedInviteTitle" = "Shared a Digital Key";

/* DKPopup Resend Invite Error TitleDKPopup Error Title */
"DKPopup.DKPersonaResendFailedTitle" = "";

/* DKPopup Resend Invite Error DescriptionDKPopup Error Description */
"DKPopup.DKPersonaResendFailedDescription" = "This Shared Key was already accepted. Remove the shared user and resend Shared Key.";

/* DKPopup Resend Invite Error DescriptionDKPopup Error Description */
"DKPopup.DKPersonaResendFailedPairedDescription" = "Guest has Active digital key paired with vehicle.";

/* DKPopup CtaHeaderLabel */
"DKPopup.DownloadGuestKeyCtaHeaderLabel" = "Download a Guest Key";

/* DKPopup Description */
"DKPopup.DownloadGuestKeyDescription" = "Digital Key for this vehicle has already been set up on another device, do you want to transfer that key to this device?";

/* DKPopup CtaTitleLabel */
"DKPopup.DownloadGuestKeySubTitleLabel" = "Connect to Vehicle";

/* DKPopup Title */
"DKPopup.DownloadGuestKeyTitle" = "Transfer Master Digital Key";

/* DKPopup CtaTitleLabel */
"DKPopup.TurnOnBluetoothCtaTitleLabel" = "Try Again";

/* DKPopup Description */
"DKPopup.TurnOnBluetoothDescription" = "Bluetooth required to use Digital Key.\nEnable Bluetooth and try again.";

/* DKPopup Title */
"DKPopup.TurnOnBluetoothTitle" = "Turn on Bluetooth®";

/* DigitalKey Cancel */
"DigitalKey.Cancel" = "Cancel";

/* DigitalKey DigitalKeyTitle */
"DigitalKey.DigitalKeyTitle" = "Digital Key";

/* DigitalKey Download */
"DigitalKey.Download" = "Download";

/* DigitalKey DownloadDigitalKey */
"DigitalKey.DownloadDigitalKey" = "Download Digital Key";

/* DigitalKey DownloadForAnotherVehicle */
"DigitalKey.DownloadForAnotherVehicle" = "Digital Key is currently downloading to another vehicle.";

/* DigitalKey Downloading... */
"DigitalKey.Downloading" = "Downloading...";

/* Digital Key Loading... */
"DigitalKey.Loading" = "Loading...";

/* DigitalKey DigitalKeyManage */
"DigitalKey.Manage" = "Manage";

/* DigitalKey ManageAndShare */
"DigitalKey.ManageAndShare" = "Manage and Share";

/* DigitalKey NotLongNow */
"DigitalKey.NotLongNow" = "Not long now...";

/* DigitalKey SharedWith */
"DigitalKey.SharedWith" = "Shared with %@";

/* DigitalKey Syncing */
"DigitalKey.Syncing" = "Syncing...";

/* DigitalKey TryAgainLater */
"DigitalKey.TryAgainLater" = "Please try again later";

/* parts and accessories title */
"Shop.parts.and.accessories" = "Parts & Accessories";

/* Shop SXM trial end */
"Shop.sxm.details.expired.title" = "Expired";

/* Shop SXM RadioShop SXM Radio */
"Shop.sxm.details.radio.title" = "Radio";

/* Shop SXM Status */
"Shop.sxm.details.status.title" = "Status";

/* Shop SXM trial end */
"Shop.sxm.details.trial.end.title" = "Trial End";

/* Shop Manage sub */
"Subscription.manage.subscriptions" = "Manage Subscriptions";

/* Shop Subscriptions title */
"Subscription.subscriptions" = "Subscriptions";

/* no packages found */
"Subscriptions.no.packages.found" = "You have no packages for this vehicle";

/* no subscription text */
"Subscriptions.no.service.found" = "No Services Found";

/* no subscription lexus subtitle text */
"Subscriptions.noSubscriptions.lexus" = "To subscribe, push the SOS button to speak with an agent. Alternatively, you can call Lexus Guest Service for support at 800-290-5506.";

/* no subscription common subtitle text */
"Subscriptions.noSubscriptions.subtitle.text" = "You have no subscriptions for your %@, would you like to add one?";

/* no subscription toyota subtitle text */
"Subscriptions.noSubscriptions.toyota" = "To subscribe, push the SOS button to speak with an agent. Alternatively, you can call Toyota Customer Service for support at 800-290-4431.";

/* Announcement subtitle */
"account.announcements.subtitle" = "Stay up to date on important announcements";

/* Announcements */
"account.announcements.title" = "Announcements";

/* Account */
"account.button.title" = "Account";

/* Dark Mode */
"account.darkMode.title" = "Dark Mode";

/* Account Inbox */
"account.inbox.title" = "Inbox";

/* Notifications subtitle */
"account.notifications.subtitle" = "See notifications for your vehicle and account";

/* Notifications */
"account.notifications.title" = "Notifications";

/* Take a Tour subtitle */
"account.takeATour.subtitle" = "Explore the app to see what’s new";

/* Take a Tour */
"account.takeATour.title" = "Take a Tour";

/* enroll now */
"announcement.enrollNow" = "Enroll Now";

/* lexus reserve */
"announcement.evSwap" = "Lexus Reserve";

/* learn more */
"announcement.learnMore" = "Learn More";

/* Manage sub */
"announcement.manage.subscription" = "Manage Subscription";

/* marketing consent button */
"announcement.marketing.consent.button" = "Learn More";

/* of text */
"announcement.of.text" = "of";

/* planning long trip */
"announcement.planning.longer.trip" = "Planning a longer trip?";

/* Announcement popup title */
"announcement.popup.title" = "Announcements";

/* reserve days */
"announcement.reserveDays" = "Lexus Reserve days added to your account";

/* subscription sutbtitle */
"announcement.subscription.subtitle" = "Your service expires in %@ days on %@";

/* lexus reserve take advanatage */
"announcement.take.advantage" = "Take advantage of the Lexus Reserve Days on your account";

/* no active announcements */
"announcementcenter.noActiveAnnouncements" = "There are no active announcements for this vehicle.";

/* Delivery Title*/
"common.delivery" = "Delivery";

/* PickUp Title*/
"common.pickUp" = "Pick up";

/* City Tile */
"common.city" = "City";

/* State Text */
"common.state" = "State";

/* Zip Code Text */
"common.zip" = "Zip Code";

/* Country Text*/
"common.country" = "Country";

/* Hours Text*/
"common.hours" = "hours";

/* Minute Text*/
"common.min" = "min";

/* Second Text*/
"common.sec" = "sec";

/* Less than a second ago Text*/
"common.lessThanSec" = "Less than a second ago.";

/* Date & Time text */
"calendar.dateAndTime" = "Date & Time";

/* Sunday */
"calendar.sunday" = "Sunday";

/* Monday */
"calendar.monday" = "Monday";

/* Tuesday */
"calendar.tuesday" = "Tuesday";

/* Wednesday */
"calendar.wednesday" = "Wednesday";

/* Thursday */
"calendar.thursday" = "Thursday";

/* Friday */
"calendar.friday" = "Friday";

/* Saturday */
"calendar.saturday" = "Saturday";

/* Sunday Short */
"calendar.sundayShort" = "Sun";

/* Monday Short */
"calendar.mondayShort" = "Mon";

/* Tuesday Short */
"calendar.tuesdayShort" = "Tue";

/* Wednesday Short */
"calendar.wednesdayShort" = "Wed";

/* Thursday Short */
"calendar.thursdayShort" = "Thu";

/* Friday Short */
"calendar.fridayShort" = "Fri";

/* Saturday Short */
"calendar.saturdayShort" = "Sat";

/* January */
"calendar.january" = "January";

/* February */
"calendar.february" = "February";

/* March */
"calendar.march" = "March";

/* April */
"calendar.april" = "April";

/* May */
"calendar.may" = "May";

/* June */
"calendar.june" = "June";

/* July */
"calendar.july" = "July";

/* August */
"calendar.august" = "August";

/* September */
"calendar.september" = "September";

/* October */
"calendar.october" = "October";

/* November */
"calendar.november" = "November";

/* December */
"calendar.december" = "December";

/* January Short */
"calendar.januaryShort" = "Jan";

/* February Short */
"calendar.februaryShort" = "Feb";

/* March Short */
"calendar.marchShort" = "Mar";

/* April Short */
"calendar.aprilShort" = "Apr";

/* May Short */
"calendar.mayShort" = "May";

/* June Short */
"calendar.juneShort" = "Jun";

/* July Short */
"calendar.julyShort" = "Jul";

/* August Short */
"calendar.augustShort" = "Aug";

/* September Short */
"calendar.septemberShort" = "Sep";

/* October Short */
"calendar.octoberShort" = "Oct";

/* November Short */
"calendar.novemberShort" = "Nov";

/* December Short */
"calendar.decemberShort" = "Dec";

/* Clean Assist */
"clean.assist" = "Clean Assist";

/* clean assist lexus subtitle */
"clean.assist.lexus.subtitle" = "Take control with the Clean Assist program allows you to charge your vehicle with 100% renewable electricity credits. The program helps minimize the overall environmental impact of your vehicle resulting from your charging activities through Renewable Energy Certificates (RECs). The best part? After you enroll, there is no additional work on your end";

/* clean assist subtitle */
"clean.assist.subtitle" = "Match your charging with 100% renewable energy.";

/* clean assist title2 */
"clean.assist.title2" = "Clean Assist Details";

/* clean assist toyota subtitle */
"clean.assist.toyota.subtitle" = "This Clean Assist program allows Toyota to match the amount of electricity you use to charge your vehicle with 100% renewable electricity credits. Not only will this program help minimize the overall environmental impact of your vehicle resulting from your charging activities, but it also gives Toyota an opportunity to further promote electric vehicles with the use of these credits. The best part&#63; After you enroll, there is no additional work on your end";

/* Climate Title */
"climate.title" = "Climate";

/* Climate Heated Seat Off */
"climate.off" = "off";

/* Climate Heated Seat Cool */
"climate.cool" = "cool";

/* Climate Heated Seat Heat */
"climate.heat" = "heat";

/* Climate Airflow Heading */
"climate.airFlow" = "Air Flow";

/* Climate Fanspeed Heading */
"climate.fanSpeed" = "Fan Speed";

/* Climate Steering Wheel Heading */
"climate.steeringWheel" = "Steering Wheel";

/* Climate Heating Heading */
"climate.heating" = "Heating";

/* Climate Defrost Heading */
"climate.defrost" = "Defrost";

/* Climate Defrost Front */
"climate.front" = "Front";

/* Climate Defrost Back */
"climate.back" = "Back";

/* Climate Air Circulation Heading */
"climate.airCirculation" = "Air Circulation";

/* Climate Air Circulation Inside */
"climate.inside" = "Inside";

/* Climate Air Circulation Outside */
"climate.outside" = "Outside";

/* Custom Climate Settings */
"climate.customTitle" = "Custom Climate Settings";

/* Custom Climate Subtitle */
"climate.customSubTitle" = "When these settings are enabled, changes will take effect the next time you remote start your car.";

/* Save Settings Button */
"climate.saveSettings" = "Save Settings";

/* Toast success message for settings */
"climate.settingsSuccess" = "Your climate settings have been saved";

/* Toast failure message for settings */
"climate.settingsFailure" = "Error - changes cannot be performed.";

/* Climate Tab Settings Title */
"climate.settingsTitle" = "Settings";

/* Climate Tab Settings Title */
"climate.scheduleTitle" = "Schedule";

/* Start Climate Button */
"climate.startClimate" = "Start Climate";

/* Stop Climate Button */
"climate.stopClimate" = "Turn Off Climate";

/* Caution Heading */
"climate.caution" = "Caution";

/* Start Alert Sub Title */
"climate.startAlertSubTitle" = "・ Please check the vehicle surroundings and make sure it is in a safe area before using Remote Climate.\n・ Do not initiate a Remote Climate Start if occupants or animals are inside the vehicle. Even when the system is in use, internal temperatures may still reach a high or low level due to features such as the automatic shut-off. Occupants and pets left inside the vehicle may suffer heatstroke, dehydration or hypothermia which could result in serious injury or death.\n・ Depending on surrounding environment such as vehicle conditions and outside temperature, Remote Climate Activation may not operate or take a long time to operate.\n Refer to your owner’s manual for more information.";

/* Alert Confirmation */
"climate.dontAskMeAgain" = "Don't ask me again";

/* Cancel Button */
"climate.cancel" = "Cancel";

/* Apply Button */
"climate.apply" = "Apply";

/* PreSeventeen Alert Sub Title */
"climate.preSeventeenSubTitle" = "In order for remote start function to activate please ensure HVAC is set to “LO” inside vehicle";

/* Last In-Car Setting */
"climate.lastInCarSetting" = "Last In-Car Setting";

/* Data not available text */
"climate.dataNotAvailable" = "Vehicle data is not available. If this vehicle was recently added, please try again later.";

/* Check Vehicle Toast */
"climate.checkVehicle" = "The climate system could not be started. Please check the vehicle communication status.";

/* Server Error Toast */
"climate.serverError" = "Server error occurred.";

/* Time Out Toast */
"climate.timedOut" = "Request timed out.";

/* Stop Alert Sub Title */
"climate.stopAlertSubTitle" = "Are you sure you want to turn off the climate system?";

/* Turn Off Button */
"climate.turnOff" = "Turn Off";

/* Keep On Button */
"climate.keepOn" = "Keep On";

/* Ends In */
"climate.endsIn" = "Ends in";

/* Empty Title */
"climate.scheduleEmptyTitle" = "Please tap the '+' button below to set the day and start time you want your vehicle to begin heating or cooling";

/* Schedule Text */
"climate.schedule" = "Schedule";

/* Climate Schedule Text */
"climate.climateSchedule" = "Climate Schedule";

/* Climate Schedule Start Time */
"climate.scheduleTime" = "When you want climate settings to start";

/* CSchedule Start Time */
"climate.startTime" = "Start Time";

/* Schedule Start Date */
"climate.date" = "Date";

/* Schedule Days */
"climate.scheduleDay" = "Day of Week";

/* Select Hint */
"climate.select" = "Select";

/* Toast - Past Time */
"climate.pastTimeToast" = "Remote climate cannot be enabled for a past date.";

/* Remove Schedule - Title */
"climate.removeScheduleTitle" = "Remove Schedule";

/* Remove Schedule - Sub Title */
"climate.removeScheduleSubTitle" = "Are you sure you want to remove this climate schedule?";

/* Remove - Button */
"climate.remove" = "Remove";

/* Button OK */
"common.Ok" = "OK";

/* Common AcceptCommon Accept */
"common.accept" = "Accept";

/* Common AcceptCommon Accepted */
"common.accepted" = "Accepted";

/* Navigation button */
"common.add" = "Add";

/* At string */
"common.at" = "at";

/* Button Continue */
"common.continue" = "Continue";

/* Button Retry */
"common.retry" = "Retry";

/* Common DeclineCommon Decline */
"common.decline" = "Decline";

/* Common DeclinedCommon Declined */
"common.declined" = "Declined";

/* Common Agree */
"common.agree" = "Agree";

/* Navigation Button */
"common.edit" = "Edit";

/* Button Exit */
"common.exit" = "Exit";

/* Feature is not supported for this vehicle. */
"common.featureNotSupported" = "The selected feature is not supported for this vehicle.";

/* Last updated string */
"common.lastVehicleStatusUpdate" = "Last updated: ";

/* No Image found */
"common.noImageFound" = "No image found";

/* Today string */
"common.today" = "Today";

/* Yesterday string */
"common.yesterday" = "Yesterday";

/* Back string */
"common.back" = "Back";

/* Go Back string */
"common.goBack" = "Go Back";
/* On string*/
"common.on" = "On";

/* Off string*/
"common.off" = "Off";

/* Error string */
"common.error" = "Error";

/* Total String */
"common.total" = "Total";

/* Services Price text */
"common.servicesPrices" = "Los precios de los servicios son estimados y pueden variar según la ubicación del distribuidor.  Es posible que algunos precios no estén disponibles en la aplicación; comuníquese con su distribuidor para obtener más detalles";

/* Current Location text */
"common.currentLocation" = "Current Location";

/* Not Now text */
"common.notNow" = "Not Now";
/* Sunday */
"common.sunday" = "Sunday";

/* Monday */
"common.monday" = "Monday";

/* Tuesday */
"common.tuesday" = "Tuesday";

/* Wednesday */
"common.wednesday" = "Wednesday";

/* Thursday */
"common.thursday" = "Thursday";

/* Friday */
"common.friday" = "Friday";

/* Saturday */
"common.saturday" = "Saturday";

/* Sunday Short Two Word */
"common.sundayShortTwo" = "Su";

/* Monday Short Two Word */
"common.mondayShortTwo" = "Mo";

/* Tuesday Short Two Word */
"common.tuesdayShortTwo" = "Tu";

/* Wednesday Short Two Word */
"common.wednesdayShortTwo" = "We";

/* Thursday Short Two Word */
"common.thursdayShortTwo" = "Th";

/* Friday Short Two Word */
"common.fridayShortTwo" = "Fr";

/* Saturday Short Two Word */
"common.saturdayShortTwo" = "Sa";

/* Add a Vehicle buttonAdd a Vehicle button */
"dashboard.addavehicle" = "Add a Vehicle";

/* Find your favorite vehicle to rent LabelFind your favorite vehicle to rent Label */
"dashboard.findfavouritevehicletorent" = "Find your favorite vehicle to rent";

/* Make a Reservation buttonMake a Reservation button */
"dashboard.makeareservation" = "Make a Reservation";

/* Rent a Toyota LabelRent a Toyota Label */
"dashboard.rentaToyota" = "Rent a Toyota";

/* Rentals headlineRentals headline */
"dashboard.rentals" = "Rentals";

/* See Upcoming labelSee Upcoming label */
"dashboard.seeupcoming" = "See upcoming";

/* Driving limits Title */
"gd.drivingLimits" = "Driving Limits";

/* Save Driving limits */
"gd.save" = "Save";

/* Stay connected LabelStay connected Label */
"dashboard.stayconnected" = "Stay connected";

/* Upcoming Reservation labelUpcoming Reservation label */
"dashboard.upcomingreservation" = "Upcoming Reservation";

/* wherever you go Labelwherever you go Label */
"dashboard.whereveryougo" = "wherever you go";

/* Driving Limit - Miles Screen Title */
"dashboard.miles" = "Miles";

/* Miles Screen - Reset Time Title */
"dashboard.resetTime" = "Reset Time";

/* Miles Screen - Reset Time Description */
"dashboard.resetMaxMilesDescription" = "This will also reset the Max Miles";

/* Miles Screen - Max Miles Title */
"dashboard.maxMiles" = "Max Miles";

/* Miles Screen - Distance Unit */
"dashboard.distanceUnit" = "mi";

/* Miles Screen - Mileage */
"dashboard.mileage" = "Mileage";

/* Driving Limit - Time Title */
"dashboard.time" = "Time";

/* Time Screen - Reset Time Description */
"dashboard.resetMaxTimeDescription" = "This will also reset the Max Time";

/* Time Screen - Max Time */
"dashboard.maxTime" = "Max Time";

/* Time Screen -  Time Unit */
"dashboard.timeUnit" = "Hrs";

/* Dealers Label */
"find.dealers" = "Dealers";

/* Destinations Label */
"find.destinations" = "Destinations";

/* Drive Pulse & Trips Label */
"find.drivePulseandTrips" = "Drive Pulse & Trips";

/* Drive pulse and recent trips Label */
"find.drivePulseandrecentTrips" = "Drive pulse and recent trips";

/* Drive pulse and Trips Driver Score Header Label */
"drivePulse.driverScoreHeaderText" = "Driver score";

/* Drive pulse and Trips Opt Out Header Label */
"drivePulse.optOutHeaderText" = "Opt Out";

/* Drive pulse and Trips Header Label */
"drivePulse.tripsHeaderText" = "Trips";

/* Drive pulse and Trips Details Header Label */
"drivePulse.tripsDetailsHeaderText" = "Trip Details";

/* Drive pulse and Trips Opt Out Body Label */
"drivePulse.optOutBodyText" = "I want to opt out of Drive Pulse, including tracking of my recent trips and driving behavior data\n\nI do not want %0 to use my location (latitude and longitude at a particular point in time) and driving data (including vehicle acceleration, speed, braking, steering) to calculate my Drive Pulse score nor track recent trips.\n\nDrive Pulse score and recent trips will no longer be tracked or calculated for the selected VIN.\n\nI understand that opting out of Drive Pulse does not stop my vehicle from sending location and driving data to {brand} or opt me out of other Connected Services. We do not share this data with any third parties without your consent.";

/* Drive pulse and Trips Opt In Body Label */
"drivePulse.optInBodyText" = "I want %0 to use my location (latitude and longitude at a particular point in time) and driving data (including vehicle acceleration, speed, braking, steering) to calculate my Drive Pulse score and track recent trips.\n\nDrive Pulse score and recent trips will be calculated and tracked for the selected VIN.\n\nDrive Pulse score, driving behavior, and trip information are for your information only and is not shared with any third party for its own purposes without your express consent.";

/* Drive pulse and Trips Opt In No Trips Available Label */
"drivePulse.noTripsAvailableText" = "No Trips Available";

/* Drive pulse and Trips Opt Out Confirm Button */
"drivePulse.textConfirm" = "Confirm";

/* Drive pulse and Trips Opt In Button */
"drivePulse.optInText" = "Opt In";

/* Drive pulse and Trips Clear Trips Button Label */
"drivePulse.clearTripsText" = "Clear Trips";

/* Drive pulse and Trips Cancel Button Label */
"drivePulse.cancelText" = "Cancel";

/* Drive pulse and Trips Drive Pulse & Trips Header Text Label */
"drivePulse.drivePulseTipsHeaderText" = "Drive Pulse & Trips";

/* Drive pulse and Trips Drive Pulse & Trips Body Text Label */
"drivePulse.drivePulseTipsBodyText" = "Drive Pulse uses the sensor data from the connected vehicle to calculate a Drive Pulse score for each trip. This sensor data includes odometer reading, acceleration and speed. The driving performance for every trip is mapped to a score on a scale of 0-100. \nThe key metrics for a trip score calculation are acceleration behaviour, braking behaviour and cornering behavior. Along with the Drive Pulse score of a particular trip, an aggregated overall Drive Pulse score based on all the past trips, for the last 30 days, is calculated to provide you a better representation of your driving behaviour.";

/* Drive pulse and Trips Drive Pulse & Trips Behavior Header Text Label */
"drivePulse.drivingBehaviorHeaderText" = "Does %0 share this driving behavior data?";

/* Drive pulse and Trips Drive Pulse & Trips Behavior Body Text Label */
"drivePulse.drivingBehaviorBodyText" = " %0 does not share this information with any third party for its own purposes without your express consent.";

/* Drive pulse and Trips Drive Pulse & Trips Opt Out Header Text Label */
"drivePulse.drivePulseOptOutHeaderText" = "Can I opt out of Drive Pulse?";

/* Drive pulse and Trips Drive Pulse & Trips Opt Out Body Text Label */
"drivePulse.drivePulseOptOutBodyText" = "Yes. To opt out of Drive Pulse and recent trips, select “Drive Pulse” from the %0 App Dashboard, then click “Learn More” to view the Driver Pulse details. Select “Opt Out” at the top right to view the opt out confirmation and select “Confirm”.";

/* Drive pulse and Trips Label */
"drivePulse.drivePulseText" = "Drive Pulse";

/* Drive pulse and Trips Harsh Cornering Label */
"drivePulse.harshCorneringText" = "Harsh\nCornering";

/* Drive pulse and Trips Fast Acceleration Label */
"drivePulse.fastAccelerationText" = "Fast\nAccelerations";

/* Drive pulse and Trips Harsh Braking Label */
"drivePulse.harshBrakingText" = "Harsh\nBraking";

/* Drive pulse and Trips Clear Text Header Label */
"drivePulse.clearTripsHeaderText" = "Clear Trips";

/* Drive pulse and Trips Clear Text Body Label */
"drivePulse.clearTripsBodyText" = "Are you sure you want clear the trips from your account?";

/* Drive pulse and Trips See FAQs Label */
"drivePulse.seeFAQsText" = "See FAQs";

/* Drive pulse and Trips No Score Text */
"drivePulse.noScoreText" = "No Score";

/* Drive pulse and Trips Fair Score Text */
"drivePulse.fairScoreText" = "Fair";

/* Drive pulse and Trips Good Score Text */
"drivePulse.goodScoreText" = "Good";

/* Drive pulse and Trips Excellent Score Text */
"drivePulse.excellentScoreText" = "Excellent";

/* Drive pulse and Trips Remove Trips Success Text */
"drivePulse.removeTripsSuccessText" = "Trips are removed successfully";

/* Drive pulse and Trips Trip A Text */
"drivePulse.tripAText" = "Trip A";

/* Drive pulse and Trips Trip B Text */
"drivePulse.tripBText" = "Trip B";

/* Drive pulse and Trips Minutes Text */
"drivePulse.minutesText" = "mins";

/* Lexus Reserve Label */
"find.evSwap" = "Lexus Reserve";

/* Favorites, recent, and sent to car Label */
"find.favoritesrecent" = "Favorites, recent, and sent to car";

/* find dealer Label */
"find.findDealer" = "Find a dealer";

/* Find your favorite vehicle to rent Label */
"find.findfavouritevehicletorent" = "Find your favorite vehicle to rent";

/* km in Label */
"find.km" = "mi";

/* Last Parked Location Label */
"find.lastParkedLocation" = "Last Parked Location";

/* Lexus Reserve Label */
"find.lexusReserve" = "Lexus Reserve";

/* Recharge in Label */
"find.rechargein" = "Find nearby stations to recharge";

/* Rent a toyota Label */
"find.rentaToyota" = "Rent a Toyota";

/* Rentals Label */
"find.rentals" = "Rentals";

/* Stations Label */
"find.stations" = "Stations";

/* Drive Pulse Sunset Banner Title */
"find.drivePulseBannerTitle" = "Important Update";

/* Drive Pulse Sunset Banner No Longer Text */
"find.drivePulseBannerNoLongerText" = "As of April 23, 2025, Drive Pulse & Trips will no longer be available.";

/* Drive Pulse Sunset Banner Detail Text */
"find.drivePulseBannerDetailText" = "You can continue accessing your Connected Services in the %0 app through your capable smartphone or smartwatch.";

/* FTUE Carousel Sub Title */
"ftue.carouselSubTitle" = "Check out our fresh new look designed to give you an even better experience. Features may vary depending on vehicle or location.";

/* FTUE Carousel Title */
"ftue.carouselTitle" = "We just got an upgrade";

/* FTUE Done CTA */
"ftue.doneCta" = "Done";

/* FTUE Health Count */
"ftue.healthCount" = "4 of 5";

/* FTUE Health SubTitle */
"ftue.healthSubTitle" = "Check your vehicle’s maintenance, alerts, and more.";

/* FTUE Health Title */
"ftue.healthTitle" = "Health";

/* FTUE Info Count */
"ftue.infoCount" = "5 of 5";

/* FTUE Info SubTitle */
"ftue.infoSubTitle" = "Review your vehicle’s manuals and warranties, subscriptions, and more.";

/* FTUE Info Title */
"ftue.infoTitle" = "Vehicle Info";

/* FTUE Next CTA */
"ftue.nextCta" = "Next";

/* FTUE Remote Count */
"ftue.remoteCount" = "2 of 5";

/* FTUE Remote SubTitle */
"ftue.remoteSubTitle" = "Start, stop, lock and unlock your vehicle remotely. Plus give guest access to your vehicle.";

/* FTUE Remote Title */
"ftue.remoteTitle" = "Remote Connect";

/* Revist SubTitle */
"ftue.revistSubTitle" = "Enjoy the new app!";

/* Revist Title */
"ftue.revistTitle" = "Revisit the tutorial from your account, anytime.";

/* FTUE Carousel See CTA */
"ftue.seeCta" = "See What’s New";

/* FTUE Carousel Skip CTA */
"ftue.skipCta" = "Skip For Now";

/* FTUE Skip Heading */
"ftue.skipTitle" = "Tap on the account icon to explore the Take a Tour option.";

/* FTUE Status Count */
"ftue.statusCount" = "3 of 5";

/* FTUE Status SubTitle */
"ftue.statusSubTitle" = "Instantly check on the status of important alerts.";

/* FTUE Status Title */
"ftue.statusTitle" = "Vehicle Status";

/* FTUE Switcher Count */
"ftue.switcherCount" = "1 of 5";

/* FTUE Switcher SubTitle */
"ftue.switcherSubTitle" = "Tap your vehicle name to switch.";

/* FTUE Switcher Title */
"ftue.switcherTitle" = "A new way to add or switch vehicles";

/* Whats New CTA */
"ftue.whatsNewCta" = "Start the Tour";

/* Whats New Title */
"ftue.whatsNewTitle" = "See what’s new";

/* Charge Now buttonCharge Now buttonCharge Now button */
"fuel.chargeNow" = "Charge Now";

/* charging stringcharging stringcharging string */
"fuel.charging" = "Charging";

/* charging session */
"fuel.chargingSession" = "Charging Session";

/* Charging details */
"fuel.chargingDetails" = "Charging details";

/* Day description */
"fuel.dayUntilFull" = "day";

/* distance to empty string */
"fuel.distanceToEmpty" = "Distance to empty";

/* Fuel est. */
"fuel.estimation" = "est.";

/* Find Stations buttonFind Stations buttonFind Stations button */
"fuel.findStations" = "Find Stations";

/* Hrs description */
"fuel.hoursUntilFull" = "hrs";

/* Curfew Screen -  Start Time Title */
"gd.startTimeUnit" = "Start Time";

/* Curfew Screen -  End Time Title */
"gd.endTimeUnit" = "End Time";

/* Curfew Screen -  Days of Week Title */
"gd.daysUnit" = "Days of Week";

/* Area Screen -  Guest Driver Search Bar Placeholder Title */
"gd.areaSearchPlaceholderText" = "Search Name, City, or Zip Code";

/* Min description */
"fuel.minUntilFull" = "min";

/* percent charged stringpercent charged stringpercent charged string */
"fuel.percentCharged" = "%0% charged";

/* range string */
"fuel.range" = "Range";

/* Unplug button */
"fuel.unplug" = "Unplug";

/* Fuel description */
"fuel.untilFull" = "until full";

/* * until fully charged */
"fuel.untillFullyCharged" = "until fully charged";

/* Guest Driver Heading */
"gd.guestDriver" = "Guest Drivers";

/* Guest Driver Sub Heading */
"gd.guestDriverSubTitle" = "Share remote and track speed limits";

/* Used to check profile activated as guestFour */
"gd.guestFour" = "Guest 4";

/* Remote Share Heading */
"gd.remoteShareSubTitle" = "Share your vehicle's remote";

/* Secondary User Text */
"gd.secondaryUser" = "You’re a secondary driver for this vehicle.";

/* Used to check profile activated as valet */
"gd.valet" = "Valet";

/* Guest Text */
"gd.guest" = "Guest";

/* Driving Limits Title */
"gd.drivingLimitsTitle" = "Driving Limits";

/* Driving Limits Sub Title */
"gd.drivingLimitsSubTitle" = "You’ll get notified when your guest driver goes over the driving limits you set.";

/* Driving Limits Cta */
"gd.drivingLimitCta" = "Define Driving Limits";

/* Everyday */
"gd.everyday" = "Everyday";

/* Label or Textfield title */
"login.email" = "Email";

/* Manage Profile and preferences */
"manageProfilePreference.title" = "Manage your Profile & Settings";

/* Service History Card Description */
"noncv.appointmentsDescription" = "Upcoming and past service";

/* Service History Card Description */
"noncv.appointmentsHeading" = "Appointments";

/* Maintanance Schedule Card Description When Having Values */
"noncv.maintananceScheduleComingUp" = "Coming up in";

/* Maintanance Schedule Card Description */
"noncv.maintananceScheduleDescription" = "No schedules found";

/* Maintanance Schedule Card Description */
"noncv.maintananceScheduleHeading" = "Maintenance Schedule";

/* Preferred Dealer Card Description */
"noncv.preferredDealerDescription" = "No preferred dealer selected";

/* Preferred Dealer Card Heading */
"noncv.preferredDealerHeading" = "Preferred Dealer";

/* Service History Card Description */
"noncv.serviceHistoryDescription" = "No service history found";

/* Service History Card Description */
"noncv.serviceHistoryHeading" = "Service History";

/* Service History Card Description When Having Values */
"noncv.serviceHistoryLastAppointment" = "Last appointment";

/* Odometer N/A text */
"odometer.notApplicable" = "N/A";

/* Odometer text */
"odometer.odometer" = "Odometer";

/* Access Acount Card Text */
"pay.accessAccountCardText" = "Click below to access your %0\n Financial Services account.";

/* Access Account CardTitle */
"pay.accessAccountTitle" = "Access Account";

/* Account label */
"pay.account" = "Account";

/* Account closed */
"pay.accountClosed" = "The financial account associated with the selected vehicle is closed.";

/* Account Linking Card Text */
"pay.accountLinkCardText" = "Link your account to make a payment on\n your vehicle from the %0 app.";

/* Account Linking Flow Card Title */
"pay.accountLinkFlowTitle" = "Account Linking Required";

/* Account Locked */
"pay.accountLocked" = "Account Locked";

/* Account not found */
"pay.accountNotFound" = "We are unable to locate this account.";

/* Account Unverified */
"pay.accountUnverified" = "Account Unverified";

/* Make Payment All caught up! */
"pay.allCaughtUp" = "All caught up!";

/* And Text */
"pay.and" = "and";

/* Cancel button */
"pay.cancel" = "Cancel";

/* Continue Button */
"pay.continueBtn" = "Continue";

/* Accept Button */
"pay.iAccept" = "I Accept";

/* Create Account */
"pay.createAccount" = "to create an account.";

/* Disclosure title text */
"pay.disclouse" = "Disclosure";

/* Make Payment Due On */
"pay.dueOn" = "Due On";

/* Electronic Communications and Agreement */
"pay.electronicCommunicationAgreement" = "Consent to Electronic Communications and Agreement";

/* Financial Com */
"pay.financialCom" = " %0financial.com";

/* Including text */
"pay.includingThe" = "including the";

/* Disclosure New User latest Agreement Text */
"pay.newUserLatestDisclosureAgreement" = "By selecting the “I Accept” button, you direct and authorize %0 Motor North America to disclose your Vehicle Identification Number to %0 Financial Services, for the purpose of linking your accounts on this app. Select the “Cancel” button if you do not wish to do so. By selecting the “I Accept” button, you acknowledge that you have read and agreed to the";

/* Disclosure Existing User latest Agreement Text */
"pay.existingUserLatestDisclosureAgreement" = "By selecting “I Accept” below, I acknowledge that I have read and agreed to the";

/* Learn More Button */
"pay.learnMore" = "Learn More";

/* Learn More Card body text */
"pay.learnMoreCompareRateText" = "Apply for credit, compare rates from\n top insurance carriers, or make payments\n for a vehicle you own or lease.";

/* Learn More Flow Card Title */
"pay.learnMoreFlowTitle" = "Flexible Financing Options";

/* Learn More Card body text */
"pay.learnMoreMakePaymentText" = "make payments for a vehicle you own or lease.";

/* Link Account Button */
"pay.linkAccount" = "Link Account";

/* Make Payment Button */
"pay.makePayment" = "Make a Payment";

/* Manage Payments Button */
"pay.managePayments" = "Manage Payments";

/* Online Policies & Agreements */
"pay.onlinePoliciesAgreement" = "Online Policies & Agreements";

/* online registration */
"pay.onlineRegistration" = "online registration";

/* online self service */
"pay.onlineSelfService" = "online self-service";

/* Make Payment Past Due */
"pay.pastDue" = "Past Due";

/* Please visit */
"pay.pleaseVisit" = "Please visit";

/* Privacy Policy Text */
"pay.privacyPolicy" = "Privacy Policy";

/* Verify your Account */
"pay.registeredUser" = "If you're a %0 Financial Services Registered user, please visit";

/* Make Payment Scheduled */
"pay.scheduled" = "Scheduled";

/* Make Payment Scheduled for Text */
"pay.scheduledFor" = "Scheduled for";

/* Server Error */
"pay.serverError" = "It looks like there was an error on our end.";

/* tap To Refresh */
"pay.tapToRefresh" = "Tap to refresh";

/* TFS card title */
"pay.tfsCardTitle" = "%0 Financial Services";

/* tryAgain */
"pay.tryAgain" = "Please try again soon.";

/* Unlock Account Content */
"pay.unlockAccountContent" = "to unlock your account. If you're not a %0 Financial Services Registered user, please visit";

/* Verify your Account */
"pay.verifyAccount" = "to complete registration and verify your account";

/* Wallet card title */
"pay.wallet" = "Wallet";

/* Wallet card Set up Text */
"pay.walletSetup" = "Set Up";

/* Wallet Card Default Title */
"pay.walletCardDefaultTitle" = "Default Card";

/* Wallet Card Four Digits */
"pay.walletCardFourDigits" = "•••• ";

/* Wallet Manage Payment for Vehicle Subscriptions */
"pay.walletManagePayment" = "Manage payment\n methods for your vehicle’s\n subscriptions.";

/* TFS card Tfs Consent Text */
"pay.tfsConsentText" = "There are new TFS terms and conditions that need to be consented to before you can access the TFS feature.";

/* Action Button on submittingAction Button on submitting */
"register.register" = "Register";

/* Remote Button Text - Info */
"remote.Info" = "Info";

/* Text for heading */
"remote.advancedRemote" = "Advanced Remote";

/* AutoFix Popup Destructive Button */
"remote.autoFixDestructive" = "Cancel";

/* AutoFix Popup Message */
"remote.autoFixMessage" = "We were unable to start the engine because all of the vehicle’s doors are not locked. Would you like to lock your doors and resume your engine start request?";

/* AutoFix Popup Primary Button */
"remote.autoFixPrimary" = "Try Again";

/* AutoFix Popup Title */
"remote.autoFixTitle" = "Unable To Process Engine Start Command";

/* Remote Button Text - Buzzer */
"remote.buzzer" = "Buzzer";

/* Buzzer on */
"remote.buzzerOn" = "Buzzer on";

/* Remote Button Text - Climate */
"remote.climate" = "Climate";

/* Remote Button Text While Connecting */
"remote.connecting" = "Connecting";

/* Feature not supported */
"remote.featureNotSupported" = "Feature not supported";

/* Guest Driver Heading */
"remote.guestDriver" = "Guest Drivers";

/* Guest Driver Sub Heading */
"remote.guestDriverSubTitle" = "Share remote and track speed limits";

/* Remote Button Text - Hazard */
"remote.hazards" = "Hazards";

/* Hazards on */
"remote.hazardsOn" = "Hazards on";

/* Remote Button Text - Horn */
"remote.horn" = "Horn";

/* Horn on */
"remote.hornOn" = "Horn on";

/* Remote Button Text - Info */
"remote.info" = "Info";

/* Remote Button Text - Lights */
"remote.lights" = "Light";

/* Lights on */
"remote.lightsOn" = "Lights on";

/* Remote Button Text - Lock */
"remote.lock" = "Lock";

/* Remote Button Text - Lock Trunk */
"remote.lockTrunk" = "Lock Trunk";

/* Remote Button Text While Contacting - Lock */
"remote.locking" = "Locking";

/* Remote Button Text - Park */
"remote.park" = "Park";

/* Toast for short press */
"remote.pressAndHold" = "Press and hold";

/* Remote Button Text - Remote */
"remote.remote" = "Remote";

/* Remote Share Heading */
"remote.remoteShareSubTitle" = "Share your vehicle's remote";

/* Remote Command failure message */
"remote.requestFailed" = "Oops, looks like something went wrong. Please try again in a few minutes";

/* Remote Button Text While Sending */
"remote.sending" = "Sending";

/* Remote Button Text - Start */
"remote.start" = "Start";

/* Toast for start command already execuiting */
"remote.startInProgress" = "Remote Start already in progress";

/* Starting */
"remote.starting" = "Starting";

/* Remote Button Text - Stop */
"remote.stop" = "Stop";

/* Stopping */
"remote.stopping" = "Stopping";

/* Tailgate Locking */
"remote.tailgateLocking" = "Tailgate Locking";

/* Tailgate Unlocking */
"remote.tailgateUnlocking" = "Tailgate Unlocking";

/* Trunk Locking */
"remote.trunkLocking" = "Trunk Locking";

/* Trunk Unlocking */
"remote.trunkUnlocking" = "Trunk Unlocking";

/* Vehicle is unable to perform this command */
"remote.unableToPerformCommand" = "Vehicle is unable to perform this command";

/* Remote Button Text - Unlock */
"remote.unlock" = "Unlock";

/* Remote Button Text - Unlock Trunk */
"remote.unlockTrunk" = "Unlock Trunk";

/* Remote Button Text While Contacting - UnLock */
"remote.unlocking" = "Unlocking";

/* Activate - Remote State Button TextActivate - Remote State Button Text */
"remotestate.activate" = "Activate";

/* Activate Remote - Remote State BodyActivate Remote - Remote State Body */
"remotestate.activateRemoteBody" = "To use remote features like start, stop, lock, and unlock, we need to verify you as the owner of the vehicle.";

/* Body text for activate remote ng86 */
"remotestate.activateRemoteBodyNG86" = "In order to activate remote functionality, we need to verify vehicle ownership. To authorize your Remote Connect service, press the SOS button in your vehicle.";

/* Activate Remote - Remote State TitleActivate Remote - Remote State Title */
"remotestate.activateRemoteTitle" = "Activate Remote";

/* Activation Error - Remote State BodyActivation Error - Remote State Body */
"remotestate.activationErrorBody" = "Your activation request could not be processed. Please contact us for further assistance.";

/* Activation Error - Remote State Button TextActivation Error - Remote State Button Text */
"remotestate.activationErrorBt" = "Contact Support";

/* Activation Error - Remote State TitleActivation Error - Remote State Title */
"remotestate.activationErrorTitle" = "Remote Activation Error";

/* Activation Pending - Remote State BodyActivation Pending - Remote State Body */
"remotestate.activationPendingBody" = "Your remote services are being activated. The activation process may take up to 24 hours.";

/* Activation Pending - Remote State Button TextActivation Pending - Remote State Button Text */
"remotestate.activationPendingBt" = "Refresh Status";

/* Activation Pending - Remote State TitleActivation Pending - Remote State Title */
"remotestate.activationPendingTitle" = "Remote Activation Pending";

/* Button text for auth required LMEX */
"remotestate.authRequiredLmexBt" = "Enter Authorization Code";

/* Notification Disabled - Remote State BodyNotification Disabled - Remote State Body */
"remotestate.notificationDisabledBody" = "To remotely start, stop, lock, and unlock your vehicle, you need to turn on push notifications to this device.";

/* Notification Disabled - Remote State Button TextNotification Disabled - Remote State Button Text */
"remotestate.notificationDisabledBt" = "Turn On";

/* Notification Disabled - Remote State TitleNotification Disabled - Remote State Title */
"remotestate.notificationDisabledTitle" = "Notification Disabled";

/* Stolen vehicle sheet title */
"remotestate.reactivateRemoteHeading" = "Reactivate Remote Connect?";

/* Stolen vehicle sheet sub title */
"remotestate.reactivateRemoteSubHeading" = "By selecting ‘Confirm’ you acknowledge your vehicle is in your possession. You will not be able to proceed with Remote Activation without it.";

/* Body text for remote shared */
"remotestate.remoteSharedBody" = "To regain control of your remote and share it with someone else, remove the driver.";

/* Button text for remote shared */
"remotestate.remoteSharedBt" = "Remove Driver";

/* Title text for remote shared */
"remotestate.remoteSharedTitle" = "Your guest has remote access";

/* Remote Share sheet Button */
"remotestate.remove" = "Remove";

/* Remote sheet Share Title */
"remotestate.removeDriver" = "Remove Driver";

/* Remote Share sheet Sub Title */
"remotestate.removeDriverNotes" = "Remove Driver to regain control of your remote and share it with someone else.";

/* Renew Subscription - Remote State Button TextRenew Subscription - Remote State Button Text */
"remotestate.renew" = "Renew Subscription";

/* Subscription Cancelled - Remote State TitleSubscription Cancelled - Remote State Title */
"remotestate.subCancelledTitle" = "Subscription Cancelled";

/* Subscription Expired - Remote State Title TextSubscription Expired - Remote State Title Text */
"remotestate.subExpiredTitle" = "Remote Subscription Expired";

/* Subscription Cancelled/Expired - Remote State BodySubscription Cancelled/Expired - Remote State Body */
"remotestate.subscriptionBody" = "Renew your subscription now to restore your remote services.";

/* Stolen vehicle sheet secondary btn */
"remotestate.textCancel" = "Cancel";

/* Stolen vehicle sheet primary btn */
"remotestate.textConfirm" = "Confirm";

/* Unable to Activate - Remote State BodyUnable to Activate - Remote State Body */
"remotestate.unableToActivateBody" = "There was a problem processing your request. Please try activating remote services again.";

/* Unable to Activate - Remote State TitleUnable to Activate - Remote State Title */
"remotestate.unableToActivateTitle" = "Unable to Activate Remote";

/* Body text for stolen vehicle */
"remotestate.vehicleStolenBody" = "Certain Remote Connect features are now unavailable. When your vehicle is recovered, click ‘Reactivate’ to restore your services and features. For assistance, please contact us.";

/* Button text for stolen vehicle */
"remotestate.vehicleStolenBt" = "Contact Us";

/* Secondary Button text for stolen vehicle */
"remotestate.vehicleStolenSecBt" = "Reactivate";

/* Title text for stolen vehicle */
"remotestate.vehicleStolenTitle" = "Vehicle Reported Stolen";

/* LMEX Auth Sheet Title */
"remotestate.verifyOwnership" = "Verify Ownership";

/* LMEX Auth Sheet Sub Title */
"remotestate.verifyOwnershipDescription" = "When your vehicle is in an area with good cellular coverage, press the SOS button and follow the prompts for \"Remote Activation\".";

/* See FAQs button textSee FAQs button text */
"service.SeeFAQs" = "See FAQs";

/* Call Roadside button textCall Roadside button text */
"service.callRoadside" = "Call %0 Roadside";

/* Roadside card content textRoadside card content text */
"service.connectWithRoadside" = "Connect with %0 Roadside Assistance to get answers to your questions.";

/* SubTitleSubTitle */
"service.getHelpOnTheRoad" = "Get help on the road";

/* buttonbutton */
"service.makeAnAppointment" = "Make an Appointment";

/* TitleTitle */
"service.roadsideAssistance" = "Roadside Assistance";

/* TitleTitle */
"service.serviceAppointments" = "Service Appointments";

/*Date & Time Title*/
"service.datetime" = "Date & Time";

/* Avaiable Times Title*/
"service.AvailableTimes" = "Available Times";

/* Custom Service Title */
"service.customService" = "Custom Service";

/* Service Prices Text */
"common.servicesPrices" = "Service prices are estimated and may vary depending on dealership location. Some prices may not be available in-app, contact your dealer for more details.";

/* Shop Announcements Title */
"shop.announcements.description" = "View %@ announcements";

/* Shop Announcements Title */
"shop.announcements.title" = "%@ Announcements";

/* Shop issurance title */
"shop.insurance.offers" = "Insurance Offers";

/* Shop parts title */
"shop.parts.and.accessories" = "Parts and Accessories";

/* Shop parts subtitle */
"shop.shop.genuine.parts" = "Shop Genuine Parts";

/* Shop SXM title */
"shop.siriusXM" = "SiriusXM";

/* Shop SXM Active status */
"shop.siriusXM.active.status" = "Account status active";

/* Shop SXM InActive status */
"shop.siriusXM.inActive.status" = "Account status inactive";

/* Shop issurance subtitle */
"shop.view.insurance.offer" = "View your personalized offers";

/* Sign out */
"signOut.button.title" = "Sign Out";

/* Sign out cancel */
"signout.cancel" = "Cancel";

/* Sign out message question */
"signout.message.question" = "Are you sure you want to Sign Out?";

/* Sign out message */
"signout.message.text" = "Sign in will require you to re-enter your username and password.";

/* closeclose */
"status.close" = "Close";

/* closedclosed */
"status.closed" = "Closed";

/* status constant Driver Side */
"status.constant.Driver.Side" = "Driver Side";

/* status constant Passenger Side */
"status.constant.Passenger.Side" = "Passenger Side";

/* status constant Rear Door */
"status.constant.Rear.Door" = "Rear Door";

/* status constant Rear Window */
"status.constant.Rear.Window" = "Rear Window";

/* status constant door */
"status.constant.door" = "door";

/* status constant hatch */
"status.constant.hatch" = "hatch";

/* status constant hood */
"status.constant.hood" = "hood";

/* status constant moonroof */
"status.constant.moonroof" = "moonroof";

/* status constant open */
"status.constant.open" = "open";

/* status constant trunk */
"status.constant.trunk" = "trunk";

/* status constant unlocked */
"status.constant.unlocked" = "unlocked";

/* status constant window */
"status.constant.window" = "window";

/* doordoor */
"status.door" = "Door";

/* doorsdoors */
"status.doors" = "Doors";

/* getting status */
"status.getting.status.text" = "Getting Status";

/* goodgood */
"status.good" = "Good";

/* hatchhatch */
"status.hatch" = "Hatch";

/* hoodhood */
"status.hood" = "Hood";

/* LockLock */
"status.lock" = "Lock";

/* lockedlocked */
"status.locked" = "Locked";

/* lowlow */
"status.low" = "low";

/* Moon roofMoon roof */
"status.moonroof" = "Moonroof";

/* Moon roof openMoon roof open */
"status.moonroof.open" = "open + moonroof";

/* openopen */
"status.open" = "open";

/* request in progress */
"status.request.inProgress" = "Request In Progress";

/* tailgate */
"status.tailgate" = "Tailgate";

/* tailgate text */
"status.tailgate.text" = "Tailgate";

/* Tire PressureTire Pressure */
"status.tirePressure" = "Tire Pressure";

/* Tire Status UpdateTire Status Update */
"status.tireStatusUpdate" = "Tire Status Updated: ";

/* Vehicle StatusVehicle Status */
"status.title" = "Status";

/* trunktrunk */
"status.trunk" = "Trunk";

/* UnitUnit */
"status.unit" = "psi";

/* unlockedunlocked */
"status.unlocked" = "Unlocked";

/* vehicle information updatedvehicle information updated */
"status.vehicleInformation.Updated" = "Vehicle Information Updated:";

/* windowwindow */
"status.window" = "Window";

/* windowswindows */
"status.windows" = "Windows";

/* status disclaimer title*/
"status.disclaimer.title" = "Check Rear Window";

/* status disclaimer subtitle*/
"status.disclaimer.subtitle" = "Status of rear window manually opened or closed does not display in the app.";

/* status disclaimer cta*/
"status.disclaimer.cta" = "Dismiss";

/* Active TextActive Text */
"subscription.active" = "Active";

/* Add Service Button TextAdd Service Button Text */
"subscription.addServices" = "Add Service";

/* Add Service Record Title */
"service.addServiceRecord" = "Add Service Record";

/* Pick Up only instructions text */
"service.puoInstructions" = "The dealer will contact you regarding pick only details. Locations will need to be with in 15 miles of the delaership.";

/* Confirm button text */
"service.confirmBtn" = "Confirm";

/* Change Transportation Button text */
"service.changeTransportation" = "Change Transportation";

/* Add Service Date Text */
"service.serviceDate" = "Service Date";

/* Add Odometer Text */
"odometer.odometer" = "Odometer";

/* Add Odometer Text */
"common.odometer" = "Odometer";


/* Auto Renew Off TextAuto Renew Off Text */
"subscription.autorenewOff" = "Auto Renew Off";

/* Auto Renew On TextAuto Renew On Text */
"subscription.autorenewOn" = "Auto Renew On";

/* Disclamier Description TextDisclamier Description Text */
"subscription.content" = "Services are dependent upon connection to a compatible wireless network, provided by a third-party wireless service provider.";

/* Disclamier Description Toyota text */
"subscription.content02" = "Content Toyota";

/* Disclamier Description Lexus TextDisclamier Description Lexus Text */
"subscription.content02Lexus" = "Lexus is not responsible for cellular network discontinuance and will not provide compensation for reduced service availability.";

/* Disclamier Description Toyota TextDisclamier Description Toyota Text */
"subscription.content02Toyota" = "Toyota is not responsible for cellular network discontinuance and will not provide compensation for reduced service availability.";

/* Disclamier Title TextDisclamier Title Text */
"subscription.disclamiertitle" = "Disclaimer";

/* Add Service Button Text */
"subscription.enableAllTrials" = "Enable All Trials";

/* Exit Button Text */
"subscription.exit" = "Exit";

/* Paid Services TextPaid Services Text */
"subscription.servicePaidPackage" = "Paid Services";

/* Trial Services TextTrial Services Text */
"subscription.serviceTrialPackage" = "Trial Services";

/* Snippet Title */
"subscription.subscriptionExpiring" = "subscription expiring";

/* Subscriptions Title Text */
"subscription.subscriptionTitle" = "Subscriptions";

/* Snippet Title */
"subscription.subscriptionsExpiring" = "subscriptions expiring";

/* Trial Available TextTrial Available Text */
"subscription.trialAvailable" = "Trial Available";

/* HealthHealth */
"tabWidget.health" = "Health";

/* Remote TabRemote Tab */
"tabWidget.remote" = "Remote";

/* StatusStatus */
"tabWidget.status" = "Status";

/* Find Tabbar button */
"tabbar.find" = "Find";

/* Key Fob Text */
"tabbar.keyfob" = "Key Fob";

/* Pay Tabbar button */
"tabbar.pay" = "Pay";

/* Safety Recalls Text */
"tabbar.safetyrecalls" = "Safety Recalls";

/* Tabbar button */
"tabbar.service" = "Service";

/* Service Campaigns Text */
"tabbar.servicecampaigns" = "Service Campaigns";

/* Shop Tabbar button */
"tabbar.shop" = "Shop";

/* Vehicle Alerts Text */
"tabbar.vehiclealerts" = "Vehicle Alerts";

/* Vehicle Health Report text */
"tabbar.vehiclehealthreport" = "Vehicle Health Report";

/* Cancel button */
"vehicleSwitcher.cancel" = "Cancel";

/* Copy */
"vehicleSwitcher.copy" = "Copy";

/* Default button */
"vehicleSwitcher.defaultText" = "Default";

/* Disclaimer Text */
"vehicleSwitcher.disclaimerText" = "Image shown for illustration purposes only. Vehicle may not be as shown.";

/* Make Default button */
"vehicleSwitcher.makeDefault" = "Make Default";

/* Remove button */
"vehicleSwitcher.remove" = "Remove";

/* Remove confirmation */
"vehicleSwitcher.removeConfirmation" = "Are you sure you want to remove this vehicle from your account?";

/* Remove Digital Key */
"vehicleSwitcher.removeDigitalKeyMessage" = "You and any shared keyholders will no longer be able to access this vehicle with a digital key.";

/* Remove Digital Key */
"vehicleSwitcher.removeDigitalKeyTitle" = "Are you sure you want to delete your Digital key?";

/* Remove Vehicle */
"vehicleSwitcher.removeVehicle" = "Remove Vehicle";

/* Remove Vehicle Subscriptions */
"vehicleSwitcher.removeVehicleAllSubscriptions" = "All services you subscribed to and subscription information for this vehicle will be removed.";

/* Remove Vehicle Confirmation when active padi subscription */
"vehicleSwitcher.removeVehicleConfirmation" = "A confirmation will be sent to the email account address for this account.";

/* All subscriptions cancel info */
"vehicleSwitcher.removeVehicleSubscriptions" = "Selecting ‘Remove Vehicle’ will cancel all subscriptions for the selected vehicle.";

/* Select button */
"vehicleSwitcher.select" = "Select";

/* Vehicle disclaimer */
"vehicleSwitcher.vehicleDisclaimer" = "Vehicle may not be as shown.";

/* Vehicle Switch title */
"vehicleSwitcher.vehicles" = "Vehicles";

/* VIN */
"vehicleSwitcher.vin" = "VIN";

/* active alert text */
"vehiclehealth.activealert" = "active vehicle alert";

/* active alerts text */
"vehiclehealth.activealerts" = "active vehicle alerts";

/* active service campaign text */
"vehiclehealth.activeservicecampaign" = "active service campaign";

/* active service campaigns text */
"vehiclehealth.activeservicecampaigns" = "active service campaigns";

/* Call dealer text */
"vehiclehealth.callDealer" = "Call Dealer";

/* Remedy tile header text */
"vehiclehealth.remedy" = "Remedy";

/* Overview tile header text */
"vehiclehealth.overview" = "Overview";

/* Description tile header text */
"vehiclehealth.description" = "Description";

/* Dealer id tile sub text */
"vehiclehealth.dealerId" = "Dealer ID";

/* Nhtsa id tile sub text */
"vehiclehealth.nhtsaId" = "NHTSA ID";

/* Remedy Status tile sub header text */
"vehiclehealth.remedyStatus" = "Remedy Status: ";

/* Engine oil good text */
"vehiclehealth.engineoilgood" = "Engine oil good";

/* Engine oil low text */
"vehiclehealth.engineoillow" = "Engine oil low";

/* good text */
"vehiclehealth.good" = "Good";

/* Key fob good text */
"vehiclehealth.keyfobgood" = "Key fob battery level is good.";

/* Key fob low text */
"vehiclehealth.keyfoblow" = "Key fob battery is low, please replace soon.";

/* low text */
"vehiclehealth.low" = "Low";

/* No Safety Recalls text */
"vehiclehealth.noSafetyRecalls" = "No safety recalls";

/* No service campaigns text */
"vehiclehealth.noservicecampaigns" = "No service campaigns";

/* safety recall text */
"vehiclehealth.safetyRecall" = "active safety recall";

/* safety recalls text */
"vehiclehealth.safetyRecalls" = "active safety recalls";

/* See report text */
"vehiclehealth.seereport" = "See report";

/* Vehicle alert text */
"vehiclehealth.vehicleAlertGood" = "No vehicle alerts";

/* App Suite Text */
"vehicleinfo.appsuite" = "App Suite";

/* Ask Siri to help you with your vehicle Text */
"vehicleinfo.asksiri" = "Ask Siri to help you with your vehicle";

/* Connected services for your vehicle Text */
"vehicleinfo.connectedservicesforyourvehicle" = "Connected services for your vehicle";

/* Connected Services Support Text */
"vehicleinfo.connectedservicessupport" = "Connected Services Support";

/* Connect to in-vehicle apps Text */
"vehicleinfo.connecttoinvehicleapps" = "Connect to in-vehicle apps";

/* VIN Copied Text */
"vehicleinfo.copied" = "Copied";

/* Customer Action Complete Text */
"vehicleinfo.customeractioncomplete" = "Customer Action Complete";

/* Dynamic Navi Text */
"vehicleinfo.dynamicnavi" = "Dynamic Navi";

/* Enter a nickname Text */
"vehicleinfo.enteranickname" = "(Enter a nickname)";

/* Get support Text */
"vehicleinfo.getsupport" = "Get support";

/* glovebox Text */
"vehicleinfo.glovebox" = "Glovebox";

/* New update available Text */
"vehicleinfo.newupdateavailable" = "New update available";

/* Remove Vehicle Button */
"vehicleinfo.removevehicle" = "Remove Vehicle";

/* Save Button */
"vehicleinfo.save" = "Save";

/* Siri Shortcuts Text */
"vehicleinfo.sirishortcuts" = "Siri Shortcuts";

/* Unable to update vehicle software Text */
"vehicleinfo.softwareerrornotification" = "Unable to update vehicle software";

/* Software update available Text */
"vehicleinfo.softwareupdateavailable" = "Software Update Available";

/* Up to date Text */
"vehicleinfo.softwareupdatecomplete" = "Up to date";

/* Software update initiated Text */
"vehicleinfo.softwareupdateprogress" = "Software Update Initialized";

/* Specs Text */
"vehicleinfo.specs" = "Specs";

/* Capabilities Text */
"vehicleinfo.vehiclecapabilities" = "Vehicle Capabilities";

/* Specs, manuals Text */
"vehicleinfo.specsmanuals" = "Specs, manuals";

/* Specs, manuals, dashboard lights Text */
"vehicleinfo.specsmanualsdashboardlights" = "Specs, manuals, dashboard lights…";

/* Subscriptions Text */
"vehicleinfo.subscriptions" = "Subscriptions";

/* Update Nickname Text */
"vehicleinfo.updatenickname" = "Update Nickname";

/* Update your renewal date Text */
"vehicleinfo.updateyourrenewaldate" = "Update your renewal date";

/* Vehicle Identification Number Text */
"vehicleinfo.vehicleidentificationnumber" = "Vehicle Identification Number";

/* Vehicle Software Text */
"vehicleinfo.vehiclesoftware" = "Vehicle Software";

/* Update Initialized Text */
"vehicleinfo.updateInitialized" = "Update Initialized";

/* Glovebox header Text */
"glovebox.glovebox" = "Glovebox";

/* specsAndCapabilities tile text */
"glovebox.specsAndCapabilities" = "Specs & Capabilities";

/* manualsAndWarranties tile text */
"glovebox.manualsAndWarranties" = "Manuals & Warranties";

/* howToVideos tile text */
"glovebox.howToVideos" = "How-To \nVideos";

/* dashboardLights tile text */
"glovebox.dashboardLights" = "Dashboard Lights";

/* toyotaForFamilies tile text */
"glovebox.toyotaForFamilies" = "Toyota for Families";

/* proXSeats tile text */
"glovebox.proXSeats" = "IsoDynamic Performance Seats";

/* seeFaq tile text */
"glovebox.seeFaq" = "See FAQs";

/* noResults text */
"glovebox.noResults" = "No Results Found";

/* search text */
"glovebox.search" = "Search";

/* noManualsAndWarranties text */
"glovebox.noManualsAndWarranties" = "No Manuals & Warranties";

/* noCapabilities text */
"glovebox.noCapabilities" = "No Capabilities";

/* specifications text */
"glovebox.specifications" = "Specifications";

/* capabilities text */
"glovebox.capabilities" = "Capabilities";

/* noSpecifications text */
"glovebox.noSpecifications" = "No Specifications";

/* vehicleInfo text */
"glovebox.vehicleInfo" = "Vehicle Info";

/* noDataFound text */
"glovebox.noDataFound" = "No data found";
/* Charge history title */
"ev.history" = "History";

/*
 Charge History tile description */
"ev.historyDescription" = "View your vehicle’s charging record";

/*
 Clean assist description */
"ev.howCAworks" = "How does Clean Assist work?";

/*
 Start */
"ev.start" = "Start";

/*
 Start Time */
"ev.startTime" = "Start Time";

/*
 startTime Sub Heading */
"ev.startTimeSubHeading" = "When you want vehicle to start charging";

/* end Time Sub Heading text */
"ev.endTimeSubHeading" = "When you want vehicle recharged by";

/*
 same Start End Time */
"ev.sameStartEndTime" = "Start and end time cannot be the same";

/*
 wattTime StartTime SubHeading */
"ev.wtStartTimeSubHeading" = "Set the time when you want your vehicle to start charging";

/*
 wattTime EndTime Sub Heading */
"ev.wtEndTimeSubHeading" = "Set the time when you want your vehicle to be recharged by";

/*
 statistics Info */
"ev.statisticsInfo" = "Energy use statistics will be available after your first complete month of driving.";

/*
 manual Schedule Heading */
"ev.manualScheduleHeading" = "Manual Charging Schedule";

/*
 estimate Range Text */
"ev.estimateRangeText" = "est. range without A/C";

/*
 no Schedule Charging Text */
"ev.noScheduleChargingText" = "No scheduled charging";

/*
 schedule Eco Charging Text */
"ev.scheduleEcoChargingText" = "Manage vehicle’s ECO charging modes and set specific charging times.";

/*
 charge Settings Text */
"ev.chargeSettingsText" = "Charging Station Settings";

/*
 evgo Free One Year Text */
"ev.evgoFreeOneYearText" = "1 Year Complimentary!";

/*
 HV range */
"ev.gasRange" = "HV range";

/*
 EV Range */
"ev.evRange" = "EV range";

/*
 ECO Charge title*/
"ev.ecoCharge" = "ECO Charge";

/*
 ECO Earned Leaf Text*/
"ev.ecoEarnedLeafText" = "Earned Leaf";

/*
 ECO Not Eligible Text*/
"ev.ecoNotEligibleText" = "Not eligible";

/*
 ECO Pending Text*/
"ev.ecoPendingText" = "Pending";

/*
 ECO Watt Time Text*/
"ev.wattTimeText" = "WattTime";

/*
 what Is Eco Charge */
"ev.whatIsEcoCharge" = "What is ECO Charge?";

/* Registered text */
"ev.chargePointRegistered" = "Registered";

/* Statistics text */
"ev.statistics" = "Statistics";

/* learn more button text */
"ev.learnMore" = "Learn More";

/* End Time Text */
"ev.endTime" = "End Time";

/* Off Peak Hours Text */
"ev.offPeakHours" = "Off-Peak Hours";

/* Off Peak Schedule Text */
"ev.offpeakSchedule" = "Off-Peak Schedule";

/** unKnown Location Heading Text */
"ev.unKnownLocationHeadingText" = "Unknown location";

/** unKnown Location SubHeading Text */
"ev.unKnownLocationSubHeadingText" = "Please go to user profile and enter your home address.";

/* Account Setting Text */
"ev.accountSettings" = "Go to Account Settings";

/* Close Text */
"ev.close" = "Close";

/** unKnown Location SubHeading 1 Text */
"ev.unKnownLocationSubHeading1Text" = "Once added, we will be able to determine the location of your future charge sessions as home or public.";

/* ECO Charging Text */
"ev.ecoCharging" = "ECO Charging";

/* eco Charging Achievement Text */
"ev.ecoChargingAchievementText" = "You’ve achieved %0 out of 7 possible leaves this week.";

/* Days of week text */
"ev.daysOfTheWeek" = "Days of Week";

/* health Impact Reduction text */
"ev.healthImpactReduction" = "Potential Health Impact Reduction";

/* No History Found text */
"ev.noCdrHistoryMessage" = "No charge history yet";

/* No Charging Session text */
"ev.noChrgSessionHistoryMessage" = "No charging sessions";

/* no Charge History SubHeading */
"ev.noChargeHistorySubHeading" = "Your charging history will be available after the first completed charging cycle.";

/* no Charge Session SubHeading */
"ev.noChrgSessionHistorySubHeading" = "There was no charging activity in the selected time period, so we don’t have any history to show you.";

/* No saved home location Location Heading Text */
 "ev.noSavedHomeAddressHeadingText" = "No saved home address";

/** No Saved Home Address SubHeading Text */
"ev.noSavedHomeAddressSubHeadingText" = "We could not determine if this was charged at home or in the public. Please go to user profile and enter your home address.";

/* evmc Departure Time Info Text */
"ev.evmcDepartureTimeInfoText" = "Set the time when you want your vehicle to stop charging";

/* off-peak Description */
"ev.offpeakDescription" = "Off-peak vehicle charging between ";

/* and text */
"ev.and" = "and";

/* find out more text */
"ev.findOutMore" = "Find out more";

/* no Schedules Title */
"ev.noSchedulesTitle" = "No charging schedules yet";

/* no Schedules Subtitle */
"ev.noSchedulesSubtitle" = "Set charging schedules and control when you charge your vehicle.";

/* no Schedules Subtitle for PHEV */
"ev.phevNoSchedulesSubtitle" = "To use ECO charging or in-app schedule charging, you need to set the schedule in the vehicle head unit first. The schedule will sync with the app after the set up.";

/* On text */
"onText" = "On";

/* Off text */
"offText" = "Off";

/* Charge ScheduleTile text */
"ev.evmcChargeSchedulePageTitleText" = "Charge Schedule";

/* New ScheduleTile text */
"ev.evmcNewSchedulePageTitleText" = "New Schedule";

/* Public text */
"ev.publicText" = "Public";

/* Home text */
"ev.home" = "Home";

/* what Is Eco Charge Answer With Health */
"ev.whatIsEcoChargeAnswerWithHealth" = "ECO Charge is a way to do your part to help reduce the potential environmental and human health impacts caused by charging your EV. The power used to charge your EV could be from a pollution-causing source at one time, but at a different time it could be from a renewable source such as a wind farm. ECO Charge helps your EV draw power from the grid at times that are forecasted by WattTime to cause less harmful pollution.";

/* what Is Eco Charge Answer Without Health */
"ev.whatIsEcoChargeAnswerWithoutHealth" = "ECO Charge is a way to do your part to help reduce the environmental and potential human health impacts caused by charging your EV. The power used to charge your EV could be from a pollution-causing source at one time, but at a different time it could be from a renewable source such as a wind farm. ECO Charge helps your EV draw power from the grid at times that are forecasted by WattTime to cause less harmful pollution.";

/* how Eco Earned text */
"ev.howEcoEarned" = "How is a leaf earned?";

/* How Eco Earned detail text with health */
"ev.howEcoEarnedAnswerWithHealth" = "WattTime estimates the potential air pollution and health damage effects of using electricity at different times of day in particular locations. %0 uses ECO Charge to suggest charging %0 EVs at less harmful times as forecasted by WattTime in order to help support the environment, including air quality and potential health outcomes. The improvement achieved by ECO Charge is measured by comparing the predicted air pollution reduction  of an ECO Charge compared to a similar non-ECO Charge. You will earn a leaf for each charge made during those less harmful times.";

/* How Eco Earned detail text without health */
"ev.howEcoEarnedAnswerWithoutHealth" = "WattTime estimates the potential [air pollution and] health damage effects of using electricity at different times of day in particular locations. %0 uses ECO Charge to suggest charging %0 EVs at less harmful times as forecasted by WattTime in order to help support the environment, including air quality and potential health outcomes. [The improvement achieved by ECO Charge is measured by comparing the predicted air pollution reduction  of an ECO Charge compared to a similar non-ECO Charge.";

/* what Is Eco Charge */
"ev.whatIsEcoCharge" = "What is ECO Charge?";

/* how Health Impact Calculated */
"ev.howHealthImpactCalculated" = "How is potential health impact calculated?";

/* how Health Impact Calculated Answer */
"ev.howHealthImpactCalculatedAnswer" = "Fossil fueled power plants emit damaging substances including sulfur dioxide, nitrogen oxides and particulate matter, which contributes to health-related problems like asthma, heart disease and stroke. Power plants emitting pollution tend to cluster in particular areas, causing a disproportionate burden to the nearby community. WattTime has developed a model that measures the potential damage to human life and health caused by different power plants at different times. By using Eco Charge, you can prioritize using electricity at times that WattTime estimates are likely to cause less harm over the long term to people who live near power plants.";

/* Charge Info text */
"ev.chargeInfo" = "Charge Info";

/* EVgo text */
"ev.evgo" = "EVgo";

/* Ionna text */
"ev.ionna" = "IONNA";

/* Charging In Progress Text text */
"ev.chargingInProgress" = "Your vehicle charging is already in progress.";

/* chargepoint text */
"ev.chargePoint" = "ChargePoint";

/* register text */
"ev.register" = "Register";

/* Sign In text */
"ev.signIn" = "Sign In";

/* Call Now text */
"ev.forgetPasswordCallNowButtonText" = "Call Now";

/* reset password description text */
"ev.forgetPasswordConfirmationText" = "To reset your password, contact EVgo\nby phone or online.";

/* Forgot password text */
"ev.forgetPasswordText" = "Forgot Password?";

/* Open Website text */
"ev.forgetPasswordOpenWebsiteButtonText" = "Open Website";

/* confirm Password Error Text */
"ev.confirmPasswordErrorText" = "The passwords don’t match. Please confirm the password.";

/* confirm Password Hint Text */
"ev.confirmPasswordHintText" = "Confirm Password";

/* password Characters Rule */
"ev.passwordCharactersRule" = "Password must have at least 1 digit, 1 uppercase, 1 special character";

/* password Hint Text */
"ev.passwordHintText" = "Password";

/* password Length Rule */
"ev.passwordLengthRule" = "Password must be at least 8 characters in length";

/* evGo Account Found Text */
"ev.evGoAccountFoundText" = "If you already have an EVgo account with this email, continue to sign in:";

/* chargePoint account Not Found Text */
"ev.chargePointaccountNotFoundText" = "We created an account for you with the email used in your Toyota app. You can now continue to start charging at a station.";

/* chargePoint account Found Text */
"ev.chargePointAccountLinkedText" = "Looks like you're already a ChargePoint user.\n Continue below to login and link your account\n to the Toyota App.";

/* chargePoint Update Your Profile Text */
"ev.chargePointUpdateYourProfileText" = "Update Your Profile";

/* chargePoint Update Profile Button Text */
"ev.chargePointUpdateProfileButtonText" = "Update Profile";

/* chargePoint Error Message Text */
"ev.chargePointErrorMessageText" = "Something went wrong, Please try again";

/* clean Energy Graph Title */
"ev.cleanEnergyGraphTitle" = "Clean Energy Charged (kWh)";

/* co2 Emissions Graph Title */
"ev.co2EmissionsGraphTitle" = "CO2 Emissions Avoided (lbs)";

/* ca Consent Preface Text */
"ev.caConsentPrefaceText" = "%0 Customers can drive worry free knowing their vehicle is using 100% renewable energy!\n\n";

/* what Is Clean Assist */
"ev.whatIsCleanAssist" = "What is Clean Assist?";

/* terms And Privacy */
"ev.termsAndPrivacy" = "Terms & Privacy";

/* evgo Expiry Date text */
"ev.evgoExpiryDate" = "Complimentary until %0";

/* evgo Free One Year */
"ev.evgoFreeOneYear" = "1 Year Complimentary!";

/* Complimentary EVgo Access Expires Soon */
"ev.complimentaryEVgoAccessExpiresSoon" = "Complimentary EVgo access expires soon";

/* Complimentary EVgo Access Has Ended */
"ev.complimentaryEVgoAccessHasEnded" = "Complimentary EVgo access has ended.";

/* To Continue Accessing EVgo Register With ChargePoint */
"ev.continueAccEVgoRegWithChargePoint" = "To continue accessing EVgo, as well as other ChargePoint partners including EVConnect, Flo, and Shell Recharge, just register with ChargePoint.";

/* To Continue Accessing EVgo Add Payment Method */
"ev.toContinueAccessingEVgoAddPaymentMethod" = "To continue accessing EVgo, as well as other ChargePoint partners including EVConnect, Flo, and Shell Recharge, just register with ChargePoint and add a payment method to your Toyota Wallet.";

/* Register With ChargePoint */
"ev.registerWithChargePoint" = "Register with ChargePoint";

/* Setup Wallet */
"ev.setupWallet" = "Setup Wallet";

/* Skip For Now */
"ev.evgoSkipCta" = "Skip For Now";

/* Schedule text */
"ev.schedule" = "Schedule";

/* eco Charging Description */
"ev.ecoChargingDescription" = "Manage your vehicle’s ECO charging modes and set specific charging times. ";

/* renewable Energy Credit CA description */
"ev.renewableEnergyCredit" = "Match your charging with 100% renewable energy.";

/* Set timer in vehicle */
"ev.setTimerInVehicle" = "Set timer in vehicle";

/* Estimated CO2 emissions saved text */
"ev.EstimatedCO2EmissionsSaved" = "Estimated CO2 emissions saved";

/* Monthly challenge text */
"ev.MonthlyChallenge" = "Monthly challenge";

/* Trees Equivalence text */
"ev.TreesEquivalence" = "Trees Equivalence";

/* earned through ECO Charging text */
"ev.earnedthroughECOCharging" = "%0 of %1 leaves earned through ECO Charging.";

/* cumulative charging behavior text */
"ev.cumulativeChargingBehavior" = "Based on your cumulative charging behavior this month, charging your vehicle at cleaner times is estimated to be %0% better for local health outcomes in comparison to previous month’s behavior.";

/* co2 avoided text */
"ev.co2Avoided" = "By using ECO Charge, you’ve avoided an estimated %0 lbs of CO2 from being emitted by power plants, which is equivalent to %1 urban tree seedlings being grown for 10 years.";

/* Account Linked text */
"ev.accountLinked" = "Account Linked";

/* Account Linked Description text */
"ev.accountLinkedText" = "You can now start charging at any partner\nstation with the app.";

/* Account Created text */
"ev.accountCreated" = "Account Created";

/* Account Created Description text */
"ev.evgoAccountCreatedText" = "You can now start charging at any network station with the app.";

/* Account Linking Failed Heading text */
"ev.evgoLinkingFailedHeading" = "EVgo Account Not Active";

/* Account Linking Failed Error text */
"ev.evgo400FailedError" = "Toyota is unable to link to your existing EVgo account. Please complete the setup of your EVgo account or contact us for assistance.";

/* Account Linking Failed Website text */
"ev.openEVgo" = "Open EVgo.com";

/* Account Linking Failed Contact Support text */
"ev.contactSupportText" = "Contact Support";

/* EV Departure Time */
"ev.departureTime" = "Departure Time";

/* Find Nearby Station Button Text */
"find.findNearByStation" = "Find Nearby Station";

/* Vehicle software title text */
"vehicleSoftware.vehicleSoftware" = "Vehicle Software";

/* Installed detail text */
"vehicleSoftware.installed" = "Installed";

/* Update failed view */
"vehicleSoftware.updateFailed" = "Software Update Failed";

/* Update failed view subtitle */
"vehicleSoftware.updateFailedSubtitle" = "The software will update the next time you start your vehicle.";

/* Back to Dashboard button */
"vehicleSoftware.backToDashboard" = "Back to Dashboard";

/* Up to date view */
"vehicleSoftware.upToDate" = "Your Software is Up to Date";

/* Up to date view subtitle */
"vehicleSoftware.upToDateSubtitle" = "There are no software updates at this time.";

/* Status expandable text */
"vehicleSoftware.status" = "Status";

/* Instructions expandable text */
"vehicleSoftware.instructions" = "Instructions";

/* What's New expandable text */
"vehicleSoftware.whatsNew" = "What's New";

/* Owner's Manual expandable text */
"vehicleSoftware.ownersManual" = "Owner's Manual";

/* Working Time expandable text */
"vehicleSoftware.workingTime" = "Working Time";

/* Previous update expandable text */
"vehicleSoftware.previousUpdate" = "Previous Updates";

/* Software Versions expandable text */
"vehicleSoftware.softwareVersions" = "Software Versions";

/* Release Notes text */
"vehicleSoftware.releaseNotes" = "Release Notes";

/* Update Later button text */
"vehicleSoftware.updateLater" = "Update Later";

/* updateLater description */
"vehicleSoftware.confirmUpdateLater" = "If you select \"Update Later\", the features in \"What's new\" won't be available until this software update is completed.";

/* Agree button text */
"vehicleSoftware.agree" = "Agree & Update";

/* Agree title */
"vehicleSoftware.agreeTitle" = "Software Update Initialized";

/* Agree description */
"vehicleSoftware.agreeDescription" = "Software Update will start after the next vehicle restart. The vehicle can be driven during the Software Update process. You will receive a message when the Software Update has been completed.";

/* Important Details expandable text */
"vehicleSoftware.importantDetails" = "Important Details";

/* Caution expandable text */
"vehicleSoftware.caution" = "Caution";

/* update button expandable text */
"vehicleSoftware.updateHistory" = "Update History";

/* update available expandable text */
"vehicleSoftware.updateAvailable" = "Update Available";

/* Current Version text */
"vehicleSoftware.currentVersion" = "Current Version:";

/* New Version text */
"vehicleSoftware.newVersion" = "New Version:";

/* See Update button text */
"vehicleSoftware.seeUpdate" = "See Update";

/* Version concatenated text */
"vehicleSoftware.version" = "Version";

/* Continue button text */
"vehicleSoftware.continue" = "Continue";

/* confirmUpdateLater button text */
"vehicleSoftware.updateLaterConfirm" = "Are you sure?";

/* sure button text */
"vehicleSoftware.sure" = "I'm Sure";

/* go back button text */
"vehicleSoftware.goBack" = "Go Back";

/* Go To Dashboard button text */
"vehicleSoftware.goToDashboard" = "Go To Dashboard";

/* Charge management within the app is only available for network stations. */
"findStations.ChargingNotAvailable" = "Charge management within the app is only available for network stations.";

/* Sent text */
"findStations.Sent" = "Sent";

/* Your destination address sent text */
"findStations.DestinationSent" = "Your destination address sent.";

/* Error text */
"findStations.Error" = "Error";

/* There was a problem sending the address to your car text */
"findStations.AddressProblem" = "There was a problem sending the address to your car.";

/* Pricing title */
"findStations.Pricing" = "Pricing";

/* No tariff details to display text */
"findStations.NoTariffDetails" = "There is no tariff details to display";

/* Energy text */
"findStations.Energy" = "Energy";

/* Flat Fee text */
"findStations.FlatFee" = "Flat Fee";

/* Per kWh text */
"findStations.PerKWh" = "per kWh";

/* Nearby Stations title */
"findStations.NearbyStations" = "Nearby Stations";

/* No Charge Station */
"findStations.noChargeStation" = "No charging station found around this location.";

/* No Favorite Station */
"findStations.noFavoriteStation" = "No favorite station found";

/* Search by address or zip code placeholder */
"findStations.SearchPlaceholder" = "Recherche par adresse ou code postal";

/* Partners filter button */
"findStations.Partners" = "Partners";

/* Plug Types filter button */
"findStations.PlugTypes" = "Plug Types";

/* Favorites filter button */
"findStations.Favorites" = "Favorites";

/* Clear All filter button */
"findStations.ClearAll" = "Clear All";

/* Directions text */
"findStations.Directions" = "Directions";

/* Send to car text */
"findStations.SendToCar" = "Send to car";

/* Available Plugs text */
"findStations.AvailablePlugs" = "Available Plugs";

/* Charge Station Details title */
"findStations.InNetwork" = "Charge Station Details";

/* Charge Partner Station header */
"findStations.partnerStationHeading" = "In Network";

/* Charge Non Partner Station header */
"findStations.nonPartnerStationHeading" = "Out of Network";

/* Open 24 Hours text */
"findStations.Open24Hours" = "Open 24 Hours";

/* Level text */
"findStations.Level" = "Level";

/* Chargers text */
"findStations.Chargers" = "Chargers";

/* Call Charge Station text */
"findStations.CallChargeStation" = "Call Charge Station";

/* Website text */
"findStations.Website" = "Website";

/* Charge Stations Text */
"findStations.chargeStations" = "Charge Stations";

/* Fuel Stations Text */
"findStations.fuelStations" = "Fuel Stations";

/* Count Nearby Text */
"findStations.countNearBy" = "nearby";

/* Select Connector Text */
"findStations.select_connector" = "Select Connector";

/* Select connector prmpt text */
"findStations.select_connector_prompt" = "Please select the connector you want to use";

/* Charger */
"findStations.charger" = "Charger";

/* Unknown Connector */
"findStations.uknown_connector" = "Unknown Connector";

/* Stop Charging text */
"charging.stopCharging" = "Stop Charging";

/* charge session error text */
"charging.chargeSessionError" = "An error occurred starting the charge session. Please try again.";

/* charge session failure text */
"charging.chargeSessionFailure" = "We lost communication to the station. Please refer to the station screen for this session.";

/* unplug error message */
"charging.unplugErrorMessage" = "Please unplug the charger from vehicle \n place it back into the station.";

/* wait don't go yet */
"charging.waitDontGoYet" = "Wait, Don’t Go Yet!";

/* use station to stop */
"charging.useStationToStop" = "Use the station to stop charging. When you have finished, tap below.";

/* use station to Finish*/
"charging.useStationToFinish" = "When you have finished, tap below.";

/* Done text*/
"common.done" = "Done";

/* Charging Interrupted text */
"charging.chargingInterrupted" = "Charging Interrupted";

/* charging Error Confirmation Text */
"charging.chargingErrorConfirmationText" = "Please check connection to the plug.";

/* Report Station text */
"charging.reportStationText" = "Report Station";

/* Payment pending message */
"charging.paymentPendingMessage" = "The station will calculate your final bill \n and you will find your receipt in charge \n history within 30 minutes.";

/* Payment pending title */
"charging.paymentPendingTitle" = "You can go now!";

/* Go to Dashboard text */
"charging.goToDashboard" = "Go to Dashboard";

/* Energy update error message */
"charging.energyUpdateError" = "The info will automatically update when we receive it from the charging station";

/* Energy update error title */
"charging.energyUpdateErrorTitle" = "One Moment";

/* Estimate range text */
"charging.estimateRangeText" ="est. range without A/C";

/* Charge description tooltip subtitle */
"charging.chargeDescToolTipSubTitle" = "Multi-information display will show lower estimated range with AC/Heat on. \nActual range will vary.";

/* Charge description tooltip title */
"charging.chargeDescToolTipTitle" = "See Vehicle Display";

/* charging session stopped title text */
"charging.chargingSessionStoppedTitle" = "You’ve stopped charging before it began";

/* charging session stopped message */
"charging.chargingSessionStoppedMessage" = "You can try again by pressing Start Charging on the next screen.";

/* Time text */
"ev.time" = "Time";

/* "Charge Level" text */
"ev.chargeLevel" = "Charge Level";

/* Duration text */
"ev.duration" = "Duration";

/* "Energy Used" text */
"ev.energyUsed" = "Energy Used";

/* "Card" text */
"ev.card" = "Card";

/* "Total" text */
"ev.total" = "Total";

/* Description: Subtitle text for no schedule */
"ev.evmcNoScheduleSubtitleText" = "Create a schedule to charge your vehicle.";

/* Description: Title text for no schedule */
"ev.evmcNoScheduleTitleText" = "Recharge in app";

/* Create Schedule button text */
"ev.createSchedule" = "Create Schedule";

/* Create Schedule Toast text */
"ev.scheduleCreatedText" = "Your schedule has been created.";

/* Deleted Schedule Toast text */
"ev.scheduleDeletedText" = "Your schedule has been deleted.";

/* Updated Schedule Toast text */
"ev.scheduleUpdatedText" = "Your schedule has been updated.";

/* no Data To View Yet text */
"ev.noDataToViewYet" = "No data to view yet";

/* statistics available after first month text */
"ev.statisticsAvailableAfterFirstMonth" = "Your Statistics will be available after the first full month of charging.";

/* guestDriver text */
"gd.guestDrivers" = "Guest Drivers";

/* guestDriver text */
"gd.guestDrivers" = "Guest Drivers";

/* guestDriver desc */
"gd.desc" = "Share your vehicle’s remote commands with others and track their speed limits on the app.";

/* guestDriver text */
"gd.guestDrivers" = "Guest Drivers";

/* guestDriver desc */
"gd.desc" = "Share your vehicle’s remote commands with others and track their speed limits on the app.";

/* guestDriver valet text */
"gd.valet" = "Valet";

/* guestDriver AlertsOn text */
"gd.alertsOn" = "Alerts On";

/* guestDriver AlertsOff text */
"gd.alertsOff" = "Alerts Off";

/* guestDriver InviteDriver text */
"gd.inviteDriver" = "Invite Driver";

/* Share Remote Label */
"gd.shareRemote" = "Share Remote";

/* Add Driver CTA */
"gd.addDriver" = "Add Driver";

/* Remote Access Title */
"gd.remoteAccessTitle" = "Remote Access";

/* Remote Access SubTitle */
"gd.remoteAccessSubTitle" = "Give driver access to remote commands with mobile app (engine, lock, hazards, …)";

/* Search by Email or Phone */
"gd.searchHint" = "Search by Email or Phone";

/* Hint For Toyota */
"gd.hintToyotaApp" = "Driver needs to have the Toyota App.";

/* Hint For Lexus */
"gd.hintLexusApp" = "Driver needs to have the Lexus App.";

/* Hint For Subaru */
"gd.hintSubaruApp" = "Driver needs to have the Subaru App.";

/* Invite Driver CTA */
"gd.inviteDriver" = "Invite Driver";

/* No Result Title */
"gd.noResultTitle" = "No Results";

/* No Result SubTitle */
"gd.noResultSubTitle" = "We couldn't find anyone by the email or phone number. Please try again.";

/* Invite Sent Title */
"gd.inviteSentTitle" = "Invite Sent";

/* Invite Sent Toyota SubTitle */
"gd.inviteSentToyotaSubTitle" = "To begin using the remote, your guest will need to download the Toyota app. You can also set their driving limits on the next screen.";

/* Invite Sent Lexus SubTitle */
"gd.inviteSentLexusSubTitle" = "To begin using the remote, your guest will need to download the Lexus app. You can also set their driving limits on the next screen.";

/* Invite Sent Subaru SubTitle */
"gd.inviteSentSubaruSubTitle" = "To begin using the remote, your guest will need to download the Subaru app. You can also set their driving limits on the next screen.";

/* guestDriver speed text */
"gd.speed" = "Speed";

/* guestDriver miles text */
"gd.milesText" = "Miles";

/* guestDriver miles unit */
"gd.milesUnit" = "Miles";

/* guest driver mileage text */
"gd.mileageText" = "Mileage";

/* guestDriver area text */
"gd.area" = "Area";

/* guestDriver curfew text */
"gd.curfew" = "Curfew";

/* guestDriver time text */
"gd.time" = "Time";

/* guestDriver ignition text */
"gd.ignition" = "Ignition";

/* guestDriver profile updated success */
"gd.profileUpdatedSuccessfully" = "Profile Updated Successfully";

/* plugged in text */
"charging.pluggedIn" = "Plugged In";
/* Okay text */
"common.okay" = "Okay";

/* Month text */
"common.month" = "Month";
/* Delete Schedule Text */
"ev.deleteSchedule" = "Delete Schedule";

/* Delete Schedule Confirmation Text */
"ev.deleteScheduleConfirmation" = "Are you sure you want to delete this schedule?";

/* Yes, Delete button text */
"ev.yesDeleteButtonText" = "Yes, Delete";

/* EV schedule days of week validation text */
"ev.evmcDaysOfWeekValidationText" = "Please select day(s) of week";

/* Monday text */
"common.monday" = "Monday";
/* Tuesday text */
"common.tuesday" = "Tuesday";
/* Wednesday text */
"common.wednesday" = "Wednesday";
/* Thursday text */
"common.thursday" = "Thursday";
/* Friday text */
"common.friday" = "Friday";
/* Saturday text */
"common.saturday" = "Saturday";
/* Sunday text */
"common.sunday" = "Sunday";
/* Charge Details text*/
"ev.chargeDetails" = "Charge Details";

/* Enroll text */
"ev.enroll" = "Enroll";

/* View text */
"ev.view" = "View";

/* Charging est text */
"ev.withoutAC" = "Est. without AC/Heat. Actual range will vary.";

/* Visa title */
"evWallet.visaTitle" = "Visa";

/* American Express title */
"evWallet.amexTitle" = "American Express";

/* Chase title */
"evWallet.chaseTitle" = "Chase";

/* Discover title */
"evWallet.discoverTitle" = "Discover";

/* JCB title */
"evWallet.jcbTitle" = "JCB";

/* MasterCard title */
"evWallet.mastercardTitle" = "MasterCard";

/* UnionPay title */
"evWallet.unionPayTitle" = "UnionPay";

/* Default Card text */
"evWallet.defaultCardText" = "Default Card";

/* Exp text */
"evWallet.expText" = "Exp ";

/* Card Set to Default text */
"evWallet.cardAlreadyDefaultTitle" = "Card Set to Default";

/* cardAlreadyDefaultDescription */
"evWallet.cardAlreadyDefaultDescription" = "The %0 card ending in %1 is already set to default";

/* Done button */
"evWallet.doneButton" = "Done";

/* Default title */
"evWallet.defaultTitle" = "Default";

/* Make Default Button */
"evWallet.makeDefaultButton" = "Make Default";

/* setDefaultDescription */
"evWallet.setDefaultDescription" = "The %0 card ending in %1 was set default to your wallet.";

/* Add Card title */
"evWallet.addCardTitle" = "Add Card";

/* Add Card agreement */
"evWallet.addCardAgreeement" = "I agree for the %0 app to store my credit card info.";

/* Save button */
"evWallet.saveButton" = "Save";

/* Billing Address title */
"evWallet.billingAddressTitle" = "Billing Address";

/* cityTextFieldPlaceholder */
"evWallet.cityTextFieldPlaceholder" = "City ( Plano )";

/* stateTextFieldPlaceholder */
"evWallet.stateTextFieldPlaceholder" = "State ( TX )";

/* zipCodeTextFieldPlaceholder */
"evWallet.zipCodeTextFieldPlaceholder" = "ZIP code";

/* countryTextFieldPlaceholder */
"evWallet.countryTextFieldPlaceholder" = "Country ( US )";

/* cardInfoTitle */
"evWallet.cardInfoTitle" = "Card Info";

/* creditCardNumberTextFieldPlaceholder */
"evWallet.creditCardNumberTextFieldPlaceholder" = "Credit Card Number";

/* firstNamePlaceholder */
"evWallet.firstNamePlaceholder" = "First Name";

/* lastNamePlaceholder */
"evWallet.lastNamePlaceholder" = "Last Name";

/* expDatePlaceholder */
"evWallet.expDatePlaceholder" = "Exp ( 09/2030 )";

/* CVV Code placeholder */
"evWallet.cvvCodePlaceholder" = "CVV Code";

/* Exp Date Dash */
"evWallet.expDateDash" = "/";

/* Charging Stations title */
"evWallet.chargingStationsTitle" = "Charging Stations";

/* Transactions title */
"evWallet.multipleTransactionsTitle" = "Transactions";

/* No Transactions Found title */
"evWallet.noTransactionTitle" = "No Transactions Found";

/* Subscriptions title */
"evWallet.subscriptionsTitle" = "Subscriptions";

/* Subscriptions description */
"evWallet.subscriptionsDescription" = "Manage Payments";

/* Wallet title */
"evWallet.walletTitle" = "Wallet";

/* No Wallet Card title */
"evWallet.noWalletCardTitle" = "Set up your\npayment methods\nfor the app";

/* Remove Card title */
"evWallet.removeCardTitle" = "Remove Card";

/* Remove Card message */
"evWallet.removeCardMessage" = "Are you sure you want to remove this card?";

/* Yes, Remove button */
"evWallet.yesRemoveCardButton" = "Yes, Remove";

/* Cancel button */
"evWallet.cancelRemoveCardButton" = "Cancel";

/* Delete title */
"evWallet.successfulCardRemovalTitle" = "Delete";

/* Successful card removal message */
"evWallet.successfulCardRemovalMessage" = "The %0 card ending in %1 was deleted from your wallet";

/* Unsuccessful card removal title */
"evWallet.unsuccessfulCardRemovalTitle" = "Add New Payment Method";

/* Unsuccessful card removal message */
"evWallet.unsuccessfulCardRemovalMessage" = "At least one card is required to remain on file. Please add a replacement card before this one can be removed.";

/* Transactions title */
"evWallet.transactionsTitle" = "Transactions";

/* ChargePoint title */
"evWallet.chargepointTitle" = "ChargePoint";

/* Ionna title */
"evWallet.ionnaTitle" = "IONNA";

/* Five dots placeholder */
"evWallet.fiveDots" = "•••••";

/* Card added title */
"evWallet.cardAddedTitle" = "Card Added";

/* Card added message */
"evWallet.cardAddedMessage" = "The card ending in %0 was added to your wallet";

/* Card add failed title */
"evWallet.cardAddFailedTitle" = "Something Went Wrong";

/* Card add failed title */
"evWallet.cardExpiryYear" = "Your card's expiration year is invalid.";

/* Card add failed title */
"evWallet.cardExpiryMonth" = "Your card's expiration month is invalid.";

/* Card add failed message */
"evWallet.cardAddFailedMessage" = "Error getting payment methods";

/* Card Transaction */
"evWallet.transaction" = "Transaction";

/* Card Transaction Product */
"evWallet.transactionProduct" = "Product";

/* Card Transaction Description */
"evWallet.transactionDescription" = "Description";

/* Card Transaction Funding */
"evWallet.transactionFunding" = "Funding";

/* Card Transaction Status */
"evWallet.transactionStatus" = "Status";

/* Card Transaction Total */
"evWallet.transactionTotal" = "Total";

/* Card Transaction Charging */
"evWallet.transactionCharging" = "Charging";

/* dsaChangePreferredDealer text */
"service.changePreferredDealer" = "Change Preferred Dealer";

/* dsaChangePreferredDealer text */
"service.changeDealer" = "Change Dealer";

/* dsaSetPreferredDealer */
"service.setPreferredDealer" = "Set as Preferred Dealer";

/* dsaTransportation header */
"service.transportation" = "Transportation";

/* dsaAccessibility header */
"service.accessibility" = "Accessibility";

/* dsaPaymentMethods header */
"service.paymentMethods" = "Payment Methods";

/* dsaAmenities header */
"service.amenities" = "Amenities";

/* dsaServices header */
"service.services" = "Services";

/* dsaServiceHours header */
"service.serviceHours" = "Service Hours";

/* dsaCall text */
"service.call" = "Call";

/* dsaWebsite text */
"service.website" = "Website";

/* dsaDirections text */
"service.directions" = "Directions";

/* dsaDealerDetails text */
"service.dealerDetails" = "Dealer Details";

/* dsaMonday text */
"service.monday" = "Monday";
/* dsaTuesday text */
"service.tuesday" = "Tuesday";
/* dsaWednesday text */
"service.wednesday" = "Wednesday";
/* dsaThursday text */
"service.thursday" = "Thursday";
/* dsaFriday text */
"service.friday" = "Friday";
/* dsaSaturday text */
"service.saturday" = "Saturday";
/* dsaSunday text */
"service.sunday" = "Sunday";
/* dsaClosed text */
"service.closed" = "CLOSED";
/* dsaExitAppointment text */
"service.exitAppointment" = "Exit Appointment";
/* dsaConfirmationExit text */
"service.confirmationExit" = "Are you sure you want to exit this\n appointment?";
/* dsaYesExit text */
"service.yesExit" = "Yes, Exit";
/* dsaContinueAppointment text */
"dsaContinueAppointment" = "Continue Appointment";
/* dsaUpcoming */
"service.upcoming" = "Upcoming";
/* dsaPast */
"service.past" = "Past";
/* dsaNoUpcomingAppointment */
"service.noUpcomingAppointment" = "No Upcoming Appointments";
/* dsaNoPastAppointment */
"service.noPastAppointment" = "No Appointments";
/* dsaNoUpcomingAppointmentDetail */
"service.noUpcomingAppointmentDetail" = "Appointments that you create\nwill appear here.";
/* dsaNoPastAppointmentDetail */
"service.noPastAppointmentDetail" = "Appointments that are completed or cancelled will appear here.";

/* dsaDone text */
"service.done" = "Done";
/* dsaSuccessPreferredDealer text */
"service.successPreferredDealer" = "You've set your Preferred Dealer.";
/* dsaSuccess */
"service.success" = "Success";
/* dsaSelectDealer */
"service.selectDealer" = "Select Dealer";
/* dsaDealrSearchPrompt */
"service.dealerSearchPrompt" = "Search Name, City, or Zip Code";
/*dsaDealerFilters*/
"service.filters" = "Filters";
/*dsaDealerApplyFilters*/
"service.applyFilters" = "Apply Filters";
/*dsaDealerResetFilters*/
"service.reset" = "Reset";
/*dsaSmartPathDealersOnly*/
"service.smartpathOnly" = "SmartPath Dealers Only";
/*dsaTenMiles*/
"service.tenMiles" = "10 Miles";
/*dsaTwentyFiveMiles*/
"service.twentyFiveMiles" = "25 Miles";
/*dsaFiftyMiles*/
"service.fiftyMiles" = "50 Miles";
/* dsaAddRecord */
"service.addrecord" = "Add Record";
/*dsaServiceAdvisor*/
"service.serviceAdvisor" = "Service Advisor";
/*dsaConfirmAppointment*/
"service.confirmAppointment" = "Confirm Appointment";
/*dsaLastKnownMileage*/
"service.lastKnownMileage" = "Last Known Mileage";
/*dsaAppointmentConfirmDisclaimer*/
"service.appointmentConfirmDisclaimer" = "By clicking “Confirm Appointment,” you consent to receive auto dialed marketing calls or text messages at the number provided. Consent is not a condition of any purchase. Message and data rates apply.";
/*dsaAdditionalComments*/
"service.additionalComments" = "Additional Comments";
/*dsaConfirmingApopintment*/
"service.confirmingAppointment" = "Confirming Appointment";
/*dsaConfirmingAppointmentWait*/
"service.confirmingAppointmentWait" = "Please wait while your appointment is being confirmed.";
/*dsaAppointmentConfirmed*/
"service.appointmentConfirmed" = "Appointment Confirmed!";
/*dsaSomethingWentWrong*/
"service.somethingWentWrong" = "Something went wrong";
/*dsaAppointmentError*/
"service.appointmentError" = "There was an issue making an appointment. Please confirm again.";
/*dsaThreeAppointmentsMax*/
"service.threeAppointmentError" = "Sorry, we allow only 3 open appointments per vehicle. Try scheduling after you complete an appointment.";
/*editAppointmentHeading*/
"service.editAppointmentHeading" = "Continue Editing";
/*editAppointmentSubHeading*/
"editAppointmentSubHeading" = "Editing services will reset all other appointment selections.";
/*dsaCancelAppointment*/
"service.cancelAppointment" = "Cancel Appointment";
/*dsaSureCancelAppointment*/
"service.sureCancelAppointment" = "Are you sure you want to cancel this \n appointment?";
/*dsaYesCancel*/
"service.yesCancel" = "Yes, Cancel";
/*dsaWaitAppointmentCancel*/
"service.waitCancelAppointment" = "Please wait while your appointment is being cancelled.";
/*dsaCancelAppointmentError*/
"service.cancelAppointmentError" = "There was an issue canceling the appointment. \nPlease confirm again.";
/*dsaAppointmentCancelled*/
"service.appointmentCancelled" = "Appointment cancelled!";
/*dsaDealerTimezone*/
"service.dealerTimezone" = "*All available times are in dealership timezone";

/* Current Location */
"findStations.currentLocation" = "Current Location";

/* Tesla Stations */
"tesla.station.adaptor.warning" = "Adaptor may be needed to charge at this station.";
