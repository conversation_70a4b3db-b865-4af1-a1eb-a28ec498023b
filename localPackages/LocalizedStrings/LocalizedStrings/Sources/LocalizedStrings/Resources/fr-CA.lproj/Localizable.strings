/*Date & Time Title*/
"service.datetime" = "Date et heure";

/* Avaiable Times Title*/
"service.AvailableTimes" = "Heures disponibles";

/* Custom Service Title */
"service.customService" = "Custom Service";

/* Biometry_faceid_failed_enter_passcode */
"Biometric.Biometry_faceid_failed_enter_passcode" = "Face ID est verrouillée en raison d'un trop grand nombre de tentatives sans succès. Entrez le mot de passe pour déverrouiller.";

/* Biometry_faceid_failed_reason */
"Biometric.Biometry_faceid_failed_reason" = "Face ID n'est pas reconnu. Veuillez réessayer.";

/* Biometry_faceid_not_enrolled */
"Biometric.Biometry_faceid_not_enrolled" = "Aucun visage n'est enregistré dans l'appareil. Veuillez aller aux réglages de l'appareil et enregistrez votre visage.";

/* Biometry_login_to_OneApp */
"Biometric.Biometry_login_to_OneApp" = "Veuillez vous authentifier pour continuer";

/* Biometry_not_available_note */
"Biometric.Biometry_not_available_note" = "L'authentification biométrique n'est pas disponible pour cet appareil.";

/* Biometry_set_passcode_to_use_faceid */
"Biometric.Biometry_set_passcode_to_use_faceid" = "Veuillez configurer le code d'accès de l'appareil pour utiliser Face ID pour l'authentification.";

/* Biometry_set_passcode_to_use_touchid */
"Biometric.Biometry_set_passcode_to_use_touchid" = "Veuillez configurer le code d'accès de l'appareil pour utiliser Touch ID pour l'authentification.";

/* Biometry_touchid_failed_enter_passcode */
"Biometric.Biometry_touchid_failed_enter_passcode" = "Touch ID est maintenant verrouillé en raison d'un trop grand nombre de tentatives sans succès. Entrez le mot de passe pour déverrouiller.";

/* Biometry_touchid_failed_reason */
"Biometric.Biometry_touchid_failed_reason" = "Touch ID ne reconnaît pas votre empreinte digitale. Veuillez réessayer.";

/* Biometry_touchid_not_enrolled */
"Biometric.Biometry_touchid_not_enrolled" = "Aucune empreinte digitale n'est enregistrée dans l'appareil. Veuillez aller aux réglages de l'appareil pour enregistrer votre empreinte digitale.";

/* DKPopup CtaHeaderLabel */
"DKPopup.ConnectVehicleCtaHeaderLabel" = "Gérer la clé";

/* DKPopup CtaTitleLabel */
"DKPopup.ConnectVehicleCtaTitleLabel" = "Se connecter au véhicule";

/* DKPopup Description */
"DKPopup.ConnectVehicleDescription" = "Utilisez votre appareil mobile comme clé pour démarrer le moteur et verrouiller/déverrouiller votre véhicule. Vous pouvez partager une clé numérique avec d'autres et gérer leur accès.";

/* DKPopup Image Description */
"DKPopup.ConnectVehicleImageDescription" = "Démarrez, verrouillez et déverrouillez votre véhicule, et partagez une clé avec d’autres.";

/* DKPopup Image Header */
"DKPopup.ConnectVehicleImageHeader" = "Votre téléphone est la clé";

/* DKPopup Title */
"DKPopup.ConnectVehicleTitle" = "Clé numérique";

/* DKPopup CtaTitleLabel */
"DKPopup.DKErrorNotDownloadedCtaTitleLabel" = "Réessayez";

/* DKPopup Description */
"DKPopup.DKErrorNotDownloadedDescription" = "Confirmez que votre appareil mobile dispose d'une pile suffisamment chargée, d'une connexion Internet ou cellulaire et que la fonction Bluetooth est activée. Si ce message s’affiche de nouveau, veuillez réessayer plus tard.";

/* DKPopup Title */
"DKPopup.DKErrorNotDownloadedTitle" = "Clé numérique non téléchargée";

/* DKPopup Description */
"DKPopup.DKNoInternetDescription" = "Pas de connexion Internet. Veuillez vérifier vos réglages et réessayer.";

/* DKPopup Shared TitleDKPopup Shared Title */
"DKPopup.DKSharedInviteTitle" = "A partagé une clé numérique";

/* DKPopup Resend Invite Error TitleDKPopup Error Title */
"DKPopup.DKPersonaResendFailedTitle" = "";

/* DKPopup Resend Invite Error DescriptionDKPopup Error Description */
"DKPopup.DKPersonaResendFailedDescription" = "Cette clé partagée a déjà été acceptée. Supprimez l'utilisateur partagé et renvoyez la clé partagée.";

/* DKPopup Resend Invite Error DescriptionDKPopup Error Description */
"DKPopup.DKPersonaResendFailedPairedDescription" = "Guest has Active digital key paired with vehicle.";

/* DKPopup CtaHeaderLabel */
"DKPopup.DownloadGuestKeyCtaHeaderLabel" = "Télécharger une clé invité";

/* DKPopup Description */
"DKPopup.DownloadGuestKeyDescription" = "La Clé numérique pour ce véhicule a déjà été configurée sur un autre appareil. Voulez-vous transférer cette clé sur cet appareil?";

/* DKPopup CtaTitleLabel */
"DKPopup.DownloadGuestKeySubTitleLabel" = "Se connecter au véhicule";

/* DKPopup Title */
"DKPopup.DownloadGuestKeyTitle" = "Transfert de la Clé numérique principale";

/* DKPopup CtaTitleLabel */
"DKPopup.TurnOnBluetoothCtaTitleLabel" = "Réessayez";

/* DKPopup Description */
"DKPopup.TurnOnBluetoothDescription" = "Vous devez avoir Bluetooth pour utiliser la Clé numérique.\nActivez Bluetooth et réessayez.";

/* DKPopup Title */
"DKPopup.TurnOnBluetoothTitle" = "Activez Bluetooth";

/* DigitalKey Cancel */
"DigitalKey.Cancel" = "Annuler";

/* DigitalKey DigitalKeyTitle */
"DigitalKey.DigitalKeyTitle" = "Clé numérique";

/* DigitalKey Download */
"DigitalKey.Download" = "Télécharger";

/* DigitalKey DownloadDigitalKey */
"DigitalKey.DownloadDigitalKey" = "Télécharger une Clé numérique";

/* DigitalKey DownloadForAnotherVehicle */
"DigitalKey.DownloadForAnotherVehicle" = "Une Clé numérique est en cours de téléchargement sur un autre véhicule.";

/* DigitalKey Downloading... */
"DigitalKey.Downloading" = "Téléchargement...";

/* Digital Key Loading... */
"DigitalKey.Loading" = "Chargement en cours...";

/* DigitalKey DigitalKeyManage */
"DigitalKey.Manage" = "Gérer";

/* DigitalKey ManageAndShare */
"DigitalKey.ManageAndShare" = "Gérer et partager";

/* DigitalKey NotLongNow */
"DigitalKey.NotLongNow" = "Not long now...";

/* DigitalKey SharedWith */
"DigitalKey.SharedWith" = "Partagé avec %@";

/* DigitalKey Syncing */
"DigitalKey.Syncing" = "Synchronisation...";

/* DigitalKey TryAgainLater */
"DigitalKey.TryAgainLater" = "Veuillez réessayer plus tard";

/* parts and accessories title */
"Shop.parts.and.accessories" = "Parts & Accessories";

/* Shop SXM trial end */
"Shop.sxm.details.expired.title" = "Expired";

/* Shop SXM RadioShop SXM Radio */
"Shop.sxm.details.radio.title" = "Radio";

/* Shop SXM Status */
"Shop.sxm.details.status.title" = "Status";

/* Shop SXM trial end */
"Shop.sxm.details.trial.end.title" = "Trial End";

/* Shop Manage sub */
"Subscription.manage.subscriptions" = "Gérer les abonnements";

/* Shop Subscriptions title */
"Subscription.subscriptions" = "Abonnements";

/* no packages found */
"Subscriptions.no.packages.found" = "Vous n'avez pas de forfaits pour votrer véhicule";

/* no subscription text */
"Subscriptions.no.service.found" = "Aucun service n'a été trouvé";

/* no subscription lexus subtitle text */
"Subscriptions.noSubscriptions.lexus" = "Pour vous abonner, appuyez sur le bouton SOS pour parler à un agent ou appelez le Service aux invités de Lexus au 800-290-5462.";

/* no subscription common subtitle text */
"Subscriptions.noSubscriptions.subtitle.text" = "Il n'y a aucun abonnement pour le véhicule %@. Voulez-vous en ajouter un?";

/* no subscription toyota subtitle text */
"Subscriptions.noSubscriptions.toyota" = "Pour vous abonner, appuyez sur le bouton SOS pour parler à un agent ou appelez le Service à la clientèle de Toyota au 800-290-4380.";

/* Announcement subtitle */
"account.announcements.subtitle" = "Restez au fait des annonces importantes";

/* Announcements */
"account.announcements.title" = "Annonces";

/* Account */
"account.button.title" = "Compte";

/* Dark Mode */
"account.darkMode.title" = "Mode sombre";

/* Account Inbox */
"account.inbox.title" = "Boîte de réception";

/* Notifications subtitle */
"account.notifications.subtitle" = "Voir les notifications pour votre véhicule et votre compte";

/* Notifications */
"account.notifications.title" = "Notifications";

/* Take a Tour subtitle */
"account.takeATour.subtitle" = "Explorez l’appli pour découvrir les nouveautés";

/* Take a Tour */
"account.takeATour.title" = "Démonstration";

/* enroll now */
"announcement.enrollNow" = "Enroll Now";

/* lexus reserve */
"announcement.evSwap" = "Lexus Reserve";

/* learn more */
"announcement.learnMore" = "En savoir plus";

/* Manage sub */
"announcement.manage.subscription" = "Administrar suscripción";

/* marketing consent button */
"announcement.marketing.consent.button" = "En savoir plus";

/* of text */
"announcement.of.text" = "sur";

/* planning long trip */
"announcement.planning.longer.trip" = "Planning a longer trip?";

/* Announcement popup title */
"announcement.popup.title" = "Annonces";

/* reserve days */
"announcement.reserveDays" = "Lexus Reserve days added to your account";

/* subscription sutbtitle */
"announcement.subscription.subtitle" = "Votre service expire dans %@ jours le %@";

/* lexus reserve take advanatage */
"announcement.take.advantage" = "Take advantage of the Lexus Reserve Days on your account";

/* no active announcements */
"announcementcenter.noActiveAnnouncements" = "There are no active announcements for this vehicle.";

/* Date & Time text */
"calendar.dateAndTime" = "Date & Temps";

/* Sunday */
"calendar.sunday" = "Dimanche";

/* Monday */
"calendar.monday" = "Lundi";

/* Tuesday */
"calendar.tuesday" = "Mardi";

/* Wednesday */
"calendar.wednesday" = "Mercredi";

/* Thursday */
"calendar.thursday" = "Jeudi";

/* Friday */
"calendar.friday" = "Vendredi";

/* Saturday */
"calendar.saturday" = "Samedi";

/* Sunday Short */
"calendar.sundayShort" = "Dim";

/* Monday Short */
"calendar.mondayShort" = "Lun";

/* Tuesday Short */
"calendar.tuesdayShort" = "Mar";

/* Wednesday Short */
"calendar.wednesdayShort" = "Mer";

/* Thursday Short */
"calendar.thursdayShort" = "Jeu";

/* Friday Short */
"calendar.fridayShort" = "Ven";

/* Saturday Short */
"calendar.saturdayShort" = "Sam";

/* January */
"calendar.january" = "Janvier";

/* February */
"calendar.february" = "Février";

/* March */
"calendar.march" = "Mars";

/* April */
"calendar.april" = "Avril";

/* May */
"calendar.may" = "Mai";

/* June */
"calendar.june" = "Juin";

/* July */
"calendar.july" = "Juillet";

/* August */
"calendar.august" = "Août";

/* September */
"calendar.september" = "Septembre";

/* October */
"calendar.october" = "Octobre";

/* November */
"calendar.november" = "Novembre";

/* December */
"calendar.december" = "Décembre";

/* January Short */
"calendar.januaryShort" = "Jan";

/* February Short */
"calendar.februaryShort" = "Fév";

/* March Short */
"calendar.marchShort" = "Mars";

/* April Short */
"calendar.aprilShort" = "Avr";

/* May Short */
"calendar.mayShort" = "Mai";

/* June Short */
"calendar.juneShort" = "Juin";

/* July Short */
"calendar.julyShort" = "Juil";

/* August Short */
"calendar.augustShort" = "Août";

/* September Short */
"calendar.septemberShort" = "Sept";

/* October Short */
"calendar.octoberShort" = "Oct";

/* November Short */
"calendar.novemberShort" = "Nov";

/* December Short */
"calendar.decemberShort" = "Déc";

/* Clean Assist */
"clean.assist" = "Aide au nettoyage";

/* clean assist lexus subtitle */
"clean.assist.lexus.subtitle" = "Take control with the Clean Assist program allows you to charge your vehicle with 100% renewable electricity credits. The program helps minimize the overall environmental impact of your vehicle resulting from your charging activities through Renewable Energy Certificates (RECs). The best part? After you enroll, there is no additional work on your end";

/* clean assist subtitle */
"clean.assist.subtitle" = "Rechargez votre véhicule avec 100 % d'énergie renouvelable.";

/* renewable Energy Credit CA description */
"ev.renewableEnergyCredit" = "Rechargez votre véhicule avec 100 % d'énergie renouvelable.";

/* invalid Time Schedule Text */
"ev.evmcInvalidTimeText" = "L'heure de fin doit être postérieure à l'heure de début.";

/* clean assist title2 */
"clean.assist.title2" = "Clean Assist Details";

/* clean assist toyota subtitle */
"clean.assist.toyota.subtitle" = "This Clean Assist program allows Toyota to match the amount of electricity you use to charge your vehicle with 100% renewable electricity credits. Not only will this program help minimize the overall environmental impact of your vehicle resulting from your charging activities, but it also gives Toyota an opportunity to further promote electric vehicles with the use of these credits. The best part&#63; After you enroll, there is no additional work on your end";

/* Climate Title */
"climate.title" = "Température";

/* Climate Heated Seat Off */
"climate.off" = "désactivé";

/* Climate Heated Seat Cool */
"climate.cool" = "frais";

/* Climate Heated Seat Heat */
"climate.heat" = "chauffer";

/* Climate Airflow Heading */
"climate.airFlow" = "Flux d'air";

/* Climate Fanspeed Heading */
"climate.fanSpeed" = "Vitesse du ventilateur";

/* Climate Steering Wheel Heading */
"climate.steeringWheel" = "Steering Wheel";

/* Climate Heating Heading */
"climate.heating" = "Volant";

/* Climate Defrost Heading */
"climate.defrost" = "Dégivrer";

/* Climate Defrost Front */
"climate.front" = "Devant";

/* Climate Defrost Back */
"climate.back" = "Arrière";

/* Climate Air Circulation Heading */
"climate.airCirculation" = "Circulation d'air";

/* Climate Air Circulation Inside */
"climate.inside" = "À l'intérieur";

/* Climate Air Circulation Outside */
"climate.outside" = "Dehors";

/* Custom Climate Settings */
"climate.customTitle" = "Paramètres de climatisation personnalisés";

/* Custom Climate Subtitle */
"climate.customSubTitle" = "Lorsque ces paramètres sont activés, les modifications prendront effet la prochaine fois que vous démarrerez votre voiture à distance.";

/* Save Settings Button */
"climate.saveSettings" = "Enregistrer les paramètres";

/* Toast success message for settings */
"climate.settingsSuccess" = "Vos paramètres climatiques ont été enregistrés";

/* Toast failure message for settings */
"climate.settingsFailure" = "Erreur - les modifications ne peuvent pas être effectuées.";

/* Climate Tab Settings Title */
"climate.settingsTitle" = "Les paramètres";

/* Climate Tab Settings Title */
"climate.scheduleTitle" = "Calendrier";

/* Start Climate Button */
"climate.startClimate" = "Démarrer Climat";

/* Stop Climate Button */
"climate.stopClimate" = "Désactiver le climat";

/* Caution Heading */
"climate.caution" = "Mise en garde";

/* Start Alert Sub Title */
"climate.startAlertSubTitle" = ". Veuillez vérifier les alentours du véhicule et vous assurer qu’il se trouve à un endroit sûr avant d’utiliser le contrôle de la température à distance.\n. N’activez pas le contrôle de la température à distance si des personnes ou des animaux se trouvent dans le véhicule. Même lorsque le système est activé, la température à l’intérieur du véhicule pourrait être très élevée ou très basse en raison de dispositifs comme l’arrêt automatique. Les personnes et les animaux se trouvant à l’intérieur du véhicule pourraient souffrir d’un coup de chaleur, de déshydratation ou d’hypothermie, ce qui peut causer des blessures graves ou la mort.\n. Selon le milieu environnant, par exemple l’état du véhicule et la température extérieure, l’activation du contrôle de la température à distance pourrait ne pas fonctionner ou mettre longtemps à fonctionner.\n・ Consultez votre manuel du propriétaire pour obtenir plus de renseignements.";

/* Alert Confirmation */
"climate.dontAskMeAgain" = "Ne me demande plus";

/* Cancel Button */
"climate.cancel" = "Annuler";

/* Apply Button */
"climate.apply" = "Appliquer";

/* PreSeventeen Alert Sub Title */
"climate.preSeventeenSubTitle" = "In order for remote start function to activate please ensure HVAC is set to “LO” inside vehicle";

/* Last In-Car Setting */
"climate.lastInCarSetting" = "Dernier réglage en voiture";

/* Data not available text */
"climate.dataNotAvailable" = "Les données du véhicule ne sont pas disponibles. Si ce véhicule a été ajouté récemment, veuillez réessayer plus tard.";

/* Check Vehicle Toast */
"climate.checkVehicle" = "Le système de climatisation n'a pas pu démarrer. Veuillez vérifier l'état de communication du véhicule.";

/* Server Error Toast */
"climate.serverError" = "Une erreur de serveur s'est produite.";

/* Time Out Toast */
"climate.timedOut" = "La demande a expiré.";

/* Stop Alert Sub Title */
"climate.stopAlertSubTitle" = "Voulez-vous vraiment désactiver le système de climatisation ?";

/* Turn Off Button */
"climate.turnOff" = "Éteindre";

/* Keep On Button */
"climate.keepOn" = "Continuer à";

/* Ends In */
"climate.endsIn" = "Se termine dans";

/* Empty Title */
"climate.scheduleEmptyTitle" = "Veuillez appuyer sur le bouton '+' ci-dessous pour définir le jour et l'heure de début auxquels vous souhaitez que votre véhicule commence à chauffer ou à refroidir";

/* Schedule Text */
"climate.schedule" = "Calendrier";

/* Climate Schedule Text */
"climate.climateSchedule" = "Calendrier climatique";

/* Climate Schedule Start Time */
"climate.scheduleTime" = "Quand vous voulez que les réglages climatiques commencent";

/* CSchedule Start Time */
"climate.startTime" = "Heure de début";

/* Schedule Start Date */
"climate.date" = "Date";

/* Schedule Days */
"climate.scheduleDay" = "Jour de la semaine";

/* Select Hint */
"climate.select" = "Sélectionner";

/* Toast - Past Time */
"climate.pastTimeToast" = "La climatisation à distance ne peut pas être activée pour une date antérieure.";

/* Remove Schedule - Title */
"climate.removeScheduleTitle" = "Supprimer l'horaire";

/* Remove Schedule - Sub Title */
"climate.removeScheduleSubTitle" = "Voulez-vous vraiment supprimer ce programme climatique ?";

/* Remove - Button */
"climate.remove" = "Supprimer";

/* Button OK */
"common.Ok" = "OK";

/* Common AcceptCommon Accept */
"common.accept" = "Accepter";

/* Navigation button */
"common.add" = "Add";

/* At string */
"common.at" = "à";

/* Button Continue */
"common.continue" = "Continuer";

/* Button Retry */
"common.retry" = "Rever";

/* Common DeclineCommon Decline */
"common.decline" = "Déclin";

/* Common Agree */
"common.agree" = "Accepter";

/* Navigation Button */
"common.edit" = "Edit";

/* Button Exit */
"common.exit" = "Exit";

/* Feature is not supported for this vehicle. */
"common.featureNotSupported" = "La fonction sélectionnée n’est pas prise\n en charge pour ce véhicule.";

/* Last updated string */
"common.lastVehicleStatusUpdate" = "Dernière mise à jour: ";

/* No Image found */
"common.noImageFound" = "Aucune image trouvée";

/* Today string */
"common.today" = "Aujourd'hui";

/* Yesterday string */
"common.yesterday" = "Hier";

/* Back String */
"common.back" = "Retour";

/* Go Back string */
"common.goBack" = "Retourner";
/* On string*/
"common.on" = "Au";

/* Off string*/
"common.off" = "Désactivé";

/* Error string */
"common.error" = "Error";

/* Total String */
"common.total" = "Le total";

/* Service Prices Text */
"common.servicesPrices" = "Les prix des services sont estimés et peuvent varier en fonction de l'emplacement du concessionnaire. Certains prix peuvent ne pas être disponibles dans l'application. Contactez votre concessionnaire pour plus de détails.";

/* Current Location text */
"common.currentLocation" = "Localisation actuelle";
/* Sunday */
"common.sunday" = "Dimanche";

/* Monday */
"common.monday" = "Lundi";

/* Tuesday */
"common.tuesday" = "Mardi";

/* Wednesday */
"common.wednesday" = "Mercredi";

/* Thursday */
"common.thursday" = "Jeudi";

/* Friday */
"common.friday" = "Vendredi";

/* Saturday */
"common.saturday" = "Samedi";

/* Sunday Short Two Word */
"common.sundayShortTwo" = "D";

/* Monday Short Two Word */
"common.mondayShortTwo" = "L";

/* Tuesday Short Two Word */
"common.tuesdayShortTwo" = "M";

/* Wednesday Short Two Word */
"common.wednesdayShortTwo" = "M";

/* Thursday Short Two Word */
"common.thursdayShortTwo" = "J";

/* Friday Short Two Word */
"common.fridayShortTwo" = "V";

/* Saturday Short Two Word */
"common.saturdayShortTwo" = "S";

/* Add a Vehicle buttonAdd a Vehicle button */
"dashboard.addavehicle" = "Ajouter un véhicule";

/* Find your favorite vehicle to rent LabelFind your favorite vehicle to rent Label */
"dashboard.findfavouritevehicletorent" = "Trouvez votre véhicule préféré à louer";

/* Make a Reservation buttonMake a Reservation button */
"dashboard.makeareservation" = "Faire une réservation";

/* Rent a Toyota LabelRent a Toyota Label */
"dashboard.rentaToyota" = "Louer une Toyota";

/* Rentals headlineRentals headline */
"dashboard.rentals" = "Locations";

/* See Upcoming labelSee Upcoming label */
"dashboard.seeupcoming" = "See upcoming";

/* Driving limits Title */
"gd.drivingLimits" = "Limites de conduite";

/* Save Driving limits */
"gd.save" = "Sauvegarder";

/* Stay connected LabelStay connected Label */
"dashboard.stayconnected" = "Rester connecté";

/* Upcoming Reservation labelUpcoming Reservation label */
"dashboard.upcomingreservation" = "Upcoming Reservation";

/* wherever you go Labelwherever you go Label */
"dashboard.whereveryougo" = "peu importe où tu vas";

/* Driving Limit - Miles Screen Title */
"dashboard.miles" = "Nbre total de kilomètres";

/* Miles Screen - Reset Time Title */
"dashboard.resetTime" = "Heure de remise à zéro";

/* Miles Screen - Reset Time Description */
"dashboard.resetMaxMilesDescription" = "Ceci réinitialisera également la distance maximale";

/* Miles Screen - Max Miles Title */
"dashboard.maxMiles" = "Kilométrage maximal";

/* Miles Screen - Distance Unit */
"dashboard.distanceUnit" = "km";

/* Miles Screen - Mileage */
"dashboard.mileage" = "Kilométrage";

/* Driving Limit - Time Title */
"dashboard.time" = "Temps";

/* Time Screen - Reset Time Description */
"dashboard.resetMaxTimeDescription" = "Ceci réinitialisera également le temps maximal";

/* Time Screen - Max Time */
"dashboard.maxTime" = "Temps maximum";

/* Time Screen -  Time Unit */
"dashboard.timeUnit" = "Hrs";

/* Curfew Screen -  Start Time Title */
"gd.startTimeUnit" = "Heure de début";

/* Curfew Screen -  End Time Title */
"gd.endTimeUnit" = "Heure de fin";

/* Curfew Screen -  Days of Week Title */
"gd.daysUnit" = "Jours de semaine";

/* Area Screen -  Guest Driver Search Bar Placeholder Title */
"gd.areaSearchPlaceholderText" = "Rechercher un nom, une ville ou un code postal";

/* Dealers Label */
"find.dealers" = "Concessionnaires";

/* Destinations Label */
"find.destinations" = "Destinations";

/* Drive Pulse & Trips Label */
"find.drivePulseandTrips" = "Drive Pulse et trajets";

/* Drive pulse and recent trips Label */
"find.drivePulseandrecentTrips" = "Drive Pulse et trajets récents";

/* Drive pulse and Trips Driver Score Header Label */
"drivePulse.driverScoreHeaderText" = "Note du conducteur";

/* Drive pulse and Trips Opt Out Header Label */
"drivePulse.optOutHeaderText" = "Se désengager";

/* Drive pulse and Trips Header Label */
"drivePulse.tripsHeaderText" = "Voyages";

/* Drive pulse and Trips Details Header Label */
"drivePulse.tripsDetailsHeaderText" = "Détails du voyage";

/* Drive pulse and Trips Opt Out Body Label */
"drivePulse.optOutBodyText" = "Je souhaite désactiver Drive Pulse, y compris le suivi de mes trajets récents et les données sur le comportement de conduite\n\nJe ne souhaite pas que %0 utilise ma position (latitude et longitude à un moment donné) et les données de conduite (y compris l'accélération, la vitesse, le freinage et la direction du véhicule) pour calculer mon score Drive Pulse ni suivre les trajets récents.\n\nLe score Drive Pulse et les trajets récents ne seront plus suivis ni calculés pour le NIV sélectionné.\n\nJe comprends que la désactivation de Drive Pulse n'empêche pas mon véhicule d'envoyer des données de localisation et de conduite à {brand} ni de me désinscrire d'autres Services connectés. Nous ne partageons pas ces données avec des tiers sans votre consentement.";

/* Drive pulse and Trips Opt In Body Label */
"drivePulse.optInBodyText" = "Je souhaite que %0 utilise ma position (latitude et longitude à un moment donné) et les données de conduite (y compris l'accélération, la vitesse, le freinage et la direction du véhicule) pour calculer mon score Drive Pulse et suivre les trajets récents.\n\nLe score Drive Pulse et les trajets récents seront calculés et suivis pour le NIV sélectionné.\n\nLe score Drive Pulse, le comportement de conduite et les informations sur les trajets sont à titre informatif uniquement et ne sont partagés avec aucun tiers à ses propres fins sans votre consentement exprès.";

/* Drive pulse and Trips Opt In No Trips Available Label */
"drivePulse.noTripsAvailableText" = "Aucun voyage disponible";

/* Drive pulse and Trips Opt Out Confirm Button */
"drivePulse.textConfirm" = "Confirmer";

/* Drive pulse and Trips Opt In Button */
"drivePulse.optInText" = "S'inscrire";

/* Drive pulse and Trips Clear Trips Button Label */
"drivePulse.clearTripsText" = "Effacer les trajets";

/* Drive pulse and Trips Cancel Button Label */
"drivePulse.cancelText" = "Annuler";

/* Drive pulse and Trips Drive Pulse & Trips Header Text Label */
"drivePulse.drivePulseTipsHeaderText" = "Drive Pulse et trajets";

/* Drive pulse and Trips Drive Pulse & Trips Body Text Label */
"drivePulse.drivePulseTipsBodyText" = "Drive Pulse utilise les données du capteur du véhicule connecté pour calculer un score Drive Pulse pour chaque trajet. Les données de ce capteur comprennent la lecture de l’odomètre, l'accélération et la vitesse. La performance de conduite pour chaque trajet est traduite en un score sur une échelle de 0 à 100. Les principaux critères de comportement pris en compte dans le score du conducteur sont l'accélération, le freinage et la prise de virages. \nEn plus du score Drive Pulse pour un trajet donné, un score Drive Pulse global agrégé ,  fondé sur tous les trajets antérieurs au cours des 30 derniers jours, est calculé pour vous fournir une meilleure représentation de votre comportement au volant.";

/* Drive pulse and Trips Drive Pulse & Trips Behavior Header Text Label */
"drivePulse.drivingBehaviorHeaderText" = "%0 partage-t-il ces données sur le comportement de conduite ?";

/* Drive pulse and Trips Drive Pulse & Trips Behavior Body Text Label */
"drivePulse.drivingBehaviorBodyText" = "%0 ne partage pas ces informations avec des tiers à ses propres fins sans votre consentement exprès.";

/* Drive pulse and Trips Drive Pulse & Trips Opt Out Header Text Label */
"drivePulse.drivePulseOptOutHeaderText" = "Puis-je me retirer de la fonction Drive Pulse ?";

/* Drive pulse and Trips Drive Pulse & Trips Opt Out Body Text Label */
"drivePulse.drivePulseOptOutBodyText" = "Oui. Pour vous retirer de la fonction Drive Pulse et des trajets récents, sélectionnez « Drive Pulse » sur le tableau de bord de l’application Lexus, puis cliquez sur « En savoir plus » pour voir les détails de Drive Pulse. Sélectionnez « Me retirer » en haut à droite pour afficher la confirmation et sélectionnez « Confirmer ».";

/* Drive pulse and Trips Label */
"drivePulse.drivePulseText" = "Drive Pulse";

/* Drive pulse and Trips Harsh Cornering Label */
"drivePulse.harshCorneringText" = "Rude\nVirage";

/* Drive pulse and Trips Fast Acceleration Label */
"drivePulse.fastAccelerationText" = "Rapide\nAccélérations";

/* Drive pulse and Trips Harsh Braking Label */
"drivePulse.harshBrakingText" = "Rude\nFreinage";

/* Drive pulse and Trips Clear Text Header Label */
"drivePulse.clearTripsHeaderText" = "Effacer les trajets";

/* Drive pulse and Trips Clear Text Body Label */
"drivePulse.clearTripsBodyText" = "Voulez-vous vraiment effacer les trajets de votre compte ?";

/* Drive pulse and Trips See FAQs Label */
"drivePulse.seeFAQsText" = "Voir FAQ";

/* Drive pulse and Trips No Score Text */
"drivePulse.noScoreText" = "Pas de score";

/* Drive pulse and Trips Fair Score Text */
"drivePulse.fairScoreText" = "Juste";

/* Drive pulse and Trips Good Score Text */
"drivePulse.goodScoreText" = "Bien";

/* Drive pulse and Trips Excellent Score Text */
"drivePulse.excellentScoreText" = "Excellent";

/* Drive pulse and Trips Remove Trips Success Text */
"drivePulse.removeTripsSuccessText" = "Les trajets sont supprimés avec succès";

/* Drive pulse and Trips Trip A Text */
"drivePulse.tripAText" = "Voyage A";

/* Drive pulse and Trips Trip B Text */
"drivePulse.tripBText" = "Voyage B";

/* Drive pulse and Trips Minutes Text */
"drivePulse.minutesText" = "minutes";

/* Lexus Reserve Label */
"find.evSwap" = "Lexus Reserve";

/* Favorites, recent, and sent to car Label */
"find.favoritesrecent" = "Favoris, récents et envoyés à la voiture";

/* find dealer Label */
"find.findDealer" = "Trouver un concessionnaire";

/* Find your favorite vehicle to rent Label */
"find.findfavouritevehicletorent" = "Trouvez votre véhicule préféré à louer";

/* km in Label */
"find.km" = "mi";

/* Last Parked Location Label */
"find.lastParkedLocation" = "Dernier emplacement de stationnement";

/* Lexus Reserve Label */
"find.lexusReserve" = "Lexus Reserve";

/* Recharge in Label */
"find.rechargein" = "Trouver des stations à proximité pour recharger";

/* Rent a toyota Label */
"find.rentaToyota" = "Louer une Toyota";

/* Rentals Label */
"find.rentals" = "Locations";

/* Stations Label */
"find.stations" = "Gares";

/* Drive Pulse Sunset Banner Title */
"find.drivePulseBannerTitle" = "Mise à jour importante";

/* Drive Pulse Sunset Banner No Longer Text */
"find.drivePulseBannerNoLongerText" = "À compter du 23 avril 2025, la fonction Drive Pulse & Trips ne sera plus disponible.";

/* Drive Pulse Sunset Banner Detail Text */
"find.drivePulseBannerDetailText" = "Vous pouvez continuer d’accéder à vos Services connectés via l’app %0 sur votre téléphone ou montre intelligents compatibles.";

/* FTUE Carousel Sub Title */
"ftue.carouselSubTitle" = "Découvrez notre nouveau look, qui se traduira par une expérience encore améliorée. Les caractéristiques peuvent varier en fonction du véhicule ou de l’emplacement.";

/* FTUE Carousel Title */
"ftue.carouselTitle" = "Nous venons de recevoir une mise à jour";

/* FTUE Done CTA */
"ftue.doneCta" = "Terminé";

/* FTUE Health Count */
"ftue.healthCount" = "4 sur 5";

/* FTUE Health SubTitle */
"ftue.healthSubTitle" = "Vérifiez l’entretien et les alertes de votre véhicule et plus encore.";

/* FTUE Health Title */
"ftue.healthTitle" = "Santé";

/* FTUE Info Count */
"ftue.infoCount" = "5 sur 5";

/* FTUE Info SubTitle */
"ftue.infoSubTitle" = "Consultez les manuels et les garanties de votre véhicule, les abonnements, etc.";

/* FTUE Info Title */
"ftue.infoTitle" = "Informations sur le véhicule";

/* FTUE Next CTA */
"ftue.nextCta" = "Suivant";

/* FTUE Remote Count */
"ftue.remoteCount" = "2 sur 5";

/* FTUE Remote SubTitle */
"ftue.remoteSubTitle" = "Démarrez, arrêtez, verrouillez et déverrouillez votre véhicule à distance. Et permettez à un conducteur invité d’avoir accès à votre véhicule.";

/* FTUE Remote Title */
"ftue.remoteTitle" = "Remote Connect";

/* Revist SubTitle */
"ftue.revistSubTitle" = "Profitez de la nouvelle appli!";

/* Revist Title */
"ftue.revistTitle" = "Revisiter le tutoriel à partir de votre compte, en tout temps";

/* FTUE Carousel See CTA */
"ftue.seeCta" = "Voir les nouveautés";

/* FTUE Carousel Skip CTA */
"ftue.skipCta" = "Ignorer pour l’instant";

/* FTUE Skip Heading */
"ftue.skipTitle" = "Touchez l’icône du compte pour voir la « Démonstration ».";

/* FTUE Status Count */
"ftue.statusCount" = "3 sur 5";

/* FTUE Status SubTitle */
"ftue.statusSubTitle" = "Vérifiez instantanément l’état des alertes importantes.";

/* FTUE Status Title */
"ftue.statusTitle" = "État du véhicule";

/* FTUE Switcher Count */
"ftue.switcherCount" = "1 sur 5";

/* FTUE Switcher SubTitle */
"ftue.switcherSubTitle" = "Touchez le nom du véhicule pour changer de véhicule.";

/* FTUE Switcher Title */
"ftue.switcherTitle" = "Une nouvelle façon d’ajouter un véhicule ou de changer de véhicule";

/* Whats New CTA */
"ftue.whatsNewCta" = "Lancer l’aperçu";

/* Whats New Title */
"ftue.whatsNewTitle" = "Voir les nouveautés";

/* Charge Now buttonCharge Now buttonCharge Now button */
"fuel.chargeNow" = "Rechargez Maintenant";

/* charging stringcharging stringcharging string */
"fuel.charging" = "Mise en charge";

/* charging session */
"fuel.chargingSession" = "Votre Sesssion de Charge";

/* Charging details */
"fuel.chargingDetails" = "Détails de recharge";

/* Day description */
"fuel.dayUntilFull" = "journée";

/* distance to empty string */
"fuel.distanceToEmpty" = "Distance restante";

/* Fuel est. */
"fuel.estimation" = "est.";

/* Find Stations buttonFind Stations buttonFind Stations button */
"fuel.findStations" = "Trouver des stations";

/* Charge Stations Text */
"findStations.chargeStations" = "Bornes de recharge";

/* Hrs description */
"fuel.hoursUntilFull" = "heures";

/* Min description */
"fuel.minUntilFull" = "min";

/* percent charged stringpercent charged stringpercent charged string */
"fuel.percentCharged" = "Chargé à %0%";

/* range string */
"fuel.range" = "Autonomie";

/* Unplug button */
"fuel.unplug" = "Débrancher";

/* Fuel description */
"fuel.untilFull" = "jusq plein";

/* * until fully charged */
"fuel.untillFullyCharged" = "jusqu'à ce qu'il soit complètement chargé";

/* Guest Driver Heading */
"gd.guestDriver" = "Chauffeurs invités";

/* Guest Driver Sub Heading */
"gd.guestDriverSubTitle" = "Partagez la télécommande et suivez\n les limites de vitesse";

/* Used to check profile activated as guestFour */
"gd.guestFour" = "Invité 4";

/* Remote Share Heading */
"gd.remoteShareSubTitle" = "Share your vehicle's remote";

/* Secondary User Text */
"gd.secondaryUser" = "Vous êtes un conducteur secondaire pour ce véhicule.";

/* Used to check profile activated as valet */
"gd.valet" = "Valet";

/* Guest Text */
"gd.guest" = "Invitée";

/* Driving Limits Title */
"gd.drivingLimitsTitle" = "Limites de conduite";

/* Driving Limits Sub Title */
"gd.drivingLimitsSubTitle" = "Vous serez averti lorsque votre chauffeur invité dépassera les limites de conduite que vous avez définies.";

/* Driving Limits Cta */
"gd.drivingLimitCta" = "Définir les limites de conduite";

/* Everyday */
"gd.everyday" = "Tous les jours";

/* Label or Textfield title */
"login.email" = "Email";

/* Manage Profile and preferences */
"manageProfilePreference.title" = "Gérer votre profil et vos paramètres";

/* Service History Card Description */
"noncv.appointmentsDescription" = "Service à venir et passé";

/* Service History Card Description */
"noncv.appointmentsHeading" = "Rendez-vous";

/* Maintanance Schedule Card Description When Having Values */
"noncv.maintananceScheduleComingUp" = "À faire dans";

/* Maintanance Schedule Card Description */
"noncv.maintananceScheduleDescription" = "Aucun horaire trouvé";

/* Maintanance Schedule Card Description */
"noncv.maintananceScheduleHeading" = "Calendrier d'entretien";

/* Preferred Dealer Card Description */
"noncv.preferredDealerDescription" = "Aucun concessionnaire préféré sélectionné";

/* Preferred Dealer Card Heading */
"noncv.preferredDealerHeading" = "Concessionnaire Préféré";

/* Service History Card Description */
"noncv.serviceHistoryDescription" = "Aucun historique de service trouvé";

/* Service History Card Description */
"noncv.serviceHistoryHeading" = "Aucun historique de service";

/* Service History Card Description When Having Values */
"noncv.serviceHistoryLastAppointment" = "Dernier rendez-vous le";

/* Odometer N/A text */
"odometer.notApplicable" = "N/A";

/* Odometer text */
"odometer.odometer" = "Odomètre";

/* Access Acount Card Text */
"pay.accessAccountCardText" = "Click below to access your %0\n Financial Services account.";

/* Access Account CardTitle */
"pay.accessAccountTitle" = "Access Account";

/* Account label */
"pay.account" = "Account";

/* Account closed */
"pay.accountClosed" = "The financial account associated with the selected vehicle is closed.";

/* Account Linking Card Text */
"pay.accountLinkCardText" = "Link your account to make a payment on\n your vehicle from the %0 app.";

/* Account Linking Flow Card Title */
"pay.accountLinkFlowTitle" = "Account Linking Required";

/* Account Locked */
"pay.accountLocked" = "Account Locked";

/* Account not found */
"pay.accountNotFound" = "We are unable to locate this account.";

/* Account Unverified */
"pay.accountUnverified" = "Account Unverified";

/* Make Payment All caught up! */
"pay.allCaughtUp" = "All caught up!";

/* And Text */
"pay.and" = "and";

/* Cancel button */
"pay.cancel" = "Cancel";

/* Continue Button */
"pay.continueBtn" = "Continue";

/* Accept Button */
"pay.iAccept" = "I Accept";

/* Create Account */
"pay.createAccount" = "to create an account.";

/* Disclosure title text */
"pay.disclouse" = "Disclosure";

/* Make Payment Due On */
"pay.dueOn" = "Due On";

/* Electronic Communications and Agreement */
"pay.electronicCommunicationAgreement" = "Consent to Electronic Communications and Agreement";

/* Financial Com */
"pay.financialCom" = " %0financial.com";

/* Including text */
"pay.includingThe" = "including the";

/* Disclosure New User latest Agreement Text */
"pay.newUserLatestDisclosureAgreement" = "By selecting the “I Accept” button, you direct and authorize %0 Motor North America to disclose your Vehicle Identification Number to %0 Financial Services, for the purpose of linking your accounts on this app. Select the “Cancel” button if you do not wish to do so. By selecting the “I Accept” button, you acknowledge that you have read and agreed to the";

/* Disclosure Existing User latest Agreement Text */
"pay.existingUserLatestDisclosureAgreement" = "By selecting “I Accept” below, I acknowledge that I have read and agreed to the";

/* Learn More Button */
"pay.learnMore" = "Learn More";

/* Learn More Card body text */
"pay.learnMoreCompareRateText" = "Apply for credit, compare rates from\n top insurance carriers, or make payments\n for a vehicle you own or lease.";

/* Learn More Flow Card Title */
"pay.learnMoreFlowTitle" = "Flexible Financing Options";

/* Learn More Card body text */
"pay.learnMoreMakePaymentText" = "make payments for a vehicle you own or lease.";

/* Link Account Button */
"pay.linkAccount" = "Link Account";

/* Make Payment Button */
"pay.makePayment" = "Make a Payment";

/* Manage Payments Button */
"pay.managePayments" = "Manage Payments";

/* Online Policies & Agreements */
"pay.onlinePoliciesAgreement" = "Online Policies & Agreements";

/* online registration */
"pay.onlineRegistration" = "online registration";

/* online self service */
"pay.onlineSelfService" = "online self-service";

/* Make Payment Past Due */
"pay.pastDue" = "Past Due";

/* Please visit */
"pay.pleaseVisit" = "Please visit";

/* Privacy Policy Text */
"pay.privacyPolicy" = "Privacy Policy";

/* Verify your Account */
"pay.registeredUser" = "If you're a %0 Financial Services Registered user, please visit";

/* Make Payment Scheduled */
"pay.scheduled" = "Scheduled";

/* Make Payment Scheduled for Text */
"pay.scheduledFor" = "Scheduled for";

/* Server Error */
"pay.serverError" = "It looks like there was an error on our end.";

/* tap To Refresh */
"pay.tapToRefresh" = "Tap to refresh";

/* TFS card title */
"pay.tfsCardTitle" = "%0 Financial Services";

/* tryAgain */
"pay.tryAgain" = "Please try again soon.";

/* Unlock Account Content */
"pay.unlockAccountContent" = "to unlock your account. If you're not a %0 Financial Services Registered user, please visit";

/* Verify your Account */
"pay.verifyAccount" = "to complete registration and verify your account";

/* Wallet card title */
"pay.wallet" = "Portefeuille";

/* Wallet card Set up Text */
"pay.walletSetup" = "Configurer";

/* Wallet Card Default Title */
"pay.walletCardDefaultTitle" = "Carte par défaut";

/* Wallet Card Four Digits */
"pay.walletCardFourDigits" = "•••• ";

/* Wallet Manage Payment for Vehicle Subscriptions */
"pay.walletManagePayment" = "Gérer le paiement\n méthodes pour votre véhicule\n abonnements.";

/* TFS card Tfs Consent Text */
"pay.tfsConsentText" = "There are new TFS terms and conditions that need to be consented to before you can access the TFS feature.";

/* Action Button on submittingAction Button on submitting */
"register.register" = "Register";

/* Remote Button Text - Info */
"remote.Info" = "Info";

/* Text for heading */
"remote.advancedRemote" = "Advanced Remote";

/* AutoFix Popup Destructive Button */
"remote.autoFixDestructive" = "Cancel";

/* AutoFix Popup Message */
"remote.autoFixMessage" = "We were unable to start the engine because all of the vehicle’s doors are not locked. Would you like to lock your doors and resume your engine start request?";

/* AutoFix Popup Primary Button */
"remote.autoFixPrimary" = "Try Again";

/* AutoFix Popup Title */
"remote.autoFixTitle" = "Unable To Process Engine Start Command";

/* Remote Button Text - Buzzer */
"remote.buzzer" = "Avertisseur sonore";

/* Buzzer on */
"remote.buzzerOn" = "Avertisseur sonore activé";

/* Remote Button Text - Climate */
"remote.climate" = "Climat";

/* Remote Button Text While Connecting */
"remote.connecting" = "Connexion";

/* Feature not supported */
"remote.featureNotSupported" = "Fonction non prise en charge";

/* Guest Driver Heading */
"remote.guestDriver" = "Chauffeurs invités";

/* Guest Driver Sub Heading */
"remote.guestDriverSubTitle" = "Partagez la télécommande et suivez\n les limites de vitesse";

/* Remote Button Text - Hazard */
"remote.hazards" = "Feux de détresse";

/* Hazards on */
"remote.hazardsOn" = "Feux de détresse allumés";

/* Remote Button Text - Horn */
"remote.horn" = "Klaxon";

/* Horn on */
"remote.hornOn" = "Klaxon activé";

/* Remote Button Text - Info */
"remote.info" = "Info";

/* Remote Button Text - Lights */
"remote.lights" = "Phares";

/* Lights on */
"remote.lightsOn" = "Phares allumés";

/* Remote Button Text - Lock */
"remote.lock" = "Verrouiller";

/* Remote Button Text - Lock Trunk */
"remote.lockTrunk" = "Verrouiller";

/* Remote Button Text While Contacting - Lock */
"remote.locking" = "Verrouillage";

/* Remote Button Text - Park */
"remote.park" = "Garer";

/* Toast for short press */
"remote.pressAndHold" = "Appuyez et maintenez";

/* Remote Button Text - Remote */
"remote.remote" = "À distance";

/* Remote Share Heading */
"remote.remoteShareSubTitle" = "Partagez la télécommande de votre véhicule";

/* Remote Command failure message */
"remote.requestFailed" = "Oups, on dirait que quelque chose a mal tourné. Veuillez réessayer dans quelques minutes";

/* Remote Button Text While Sending */
"remote.sending" = "Envoi";

/* Remote Button Text - Start */
"remote.start" = "Démarrer";

/* Toast for start command already execuiting */
"remote.startInProgress" = "Démarrage à distance déjà en cours";

/* Starting */
"remote.starting" = "Démarrage";

/* Remote Button Text - Stop */
"remote.stop" = "Arrêter";

/* Stopping */
"remote.stopping" = "Arrêt";

/* Tailgate Locking */
"remote.tailgateLocking" = "Verrouillage du hayon";

/* Tailgate Unlocking */
"remote.tailgateUnlocking" = "Déverrouillage du hayon";

/* Trunk Locking */
"remote.trunkLocking" = "Verrouillage du coffre";

/* Trunk Unlocking */
"remote.trunkUnlocking" = "Déverrouillage du coffre";

/* Vehicle is unable to perform this command */
"remote.unableToPerformCommand" = "Véhicule est incapable d'exécuter cette commande";

/* Remote Button Text - Unlock */
"remote.unlock" = "Déverrouiller";

/* Remote Button Text - Unlock Trunk */
"remote.unlockTrunk" = "Déverrouiller";

/* Remote Button Text While Contacting - UnLock */
"remote.unlocking" = "Déverrouillage";

/* Activate - Remote State Button TextActivate - Remote State Button Text */
"remotestate.activate" = "Activer";

/* Activate Remote - Remote State BodyActivate Remote - Remote State Body */
"remotestate.activateRemoteBody" = "Pour utiliser des fonctions à distance telles que démarrer, arrêter, verrouiller et déverrouiller, nous devons vérifier que vous êtes le propriétaire du véhicule.";

/* Body text for activate remote ng86 */
"remotestate.activateRemoteBodyNG86" = "Nous devons vérifier que vous êtes propriétaire du véhicule avant d’activer les services à distance. Pour autoriser les services Remote Connect, appuyez sur le bouton SOS dans votre véhicule.";

/* Activate Remote - Remote State TitleActivate Remote - Remote State Title */
"remotestate.activateRemoteTitle" = "Activer la télécommande";

/* Activation Error - Remote State BodyActivation Error - Remote State Body */
"remotestate.activationErrorBody" = "Votre demande d'activation n'a pas pu être traitée. Veuillez nous contacter pour plus d'aide.";

/* Activation Error - Remote State Button TextActivation Error - Remote State Button Text */
"remotestate.activationErrorBt" = "Contactez le support";

/* Activation Error - Remote State TitleActivation Error - Remote State Title */
"remotestate.activationErrorTitle" = "Erreur d'activation à distance";

/* Activation Pending - Remote State BodyActivation Pending - Remote State Body */
"remotestate.activationPendingBody" = "Vos services à distance sont activés. Le processus d'activation peut prendre jusqu'à 24 heures.";

/* Activation Pending - Remote State Button TextActivation Pending - Remote State Button Text */
"remotestate.activationPendingBt" = "Actualiser l'état";

/* Activation Pending - Remote State TitleActivation Pending - Remote State Title */
"remotestate.activationPendingTitle" = "Activation à distance en attente";

/* Button text for auth required LMEX */
"remotestate.authRequiredLmexBt" = "Entrez le code d'autorisation";

/* Notification Disabled - Remote State BodyNotification Disabled - Remote State Body */
"remotestate.notificationDisabledBody" = "Pour démarrer, arrêter, verrouiller et déverrouiller votre véhicule à distance, vous devez activer les notifications push sur cet appareil.";

/* Notification Disabled - Remote State Button TextNotification Disabled - Remote State Button Text */
"remotestate.notificationDisabledBt" = "Allumer";

/* Notification Disabled - Remote State TitleNotification Disabled - Remote State Title */
"remotestate.notificationDisabledTitle" = "Notifications désactivées";

/* Stolen vehicle sheet title */
"remotestate.reactivateRemoteHeading" = "Réactiver la connexion à distance ?";

/* Stolen vehicle sheet sub title */
"remotestate.reactivateRemoteSubHeading" = "En sélectionnant  'Confirmer' , vous reconnaissez que votre véhicule est en votre possession. Vous ne pourrez pas procéder à l'activation à distance sans elle.";

/* Body text for remote shared */
"remotestate.remoteSharedBody" = "Pour reprendre le contrôle de votre télécommande et la partager avec quelqu'un d'autre, supprimez le pilote.";

/* Button text for remote shared */
"remotestate.remoteSharedBt" = "Supprimer le pilote";

/* Title text for remote shared */
"remotestate.remoteSharedTitle" = "Votre invité a un accès à distance";

/* Remote Share sheet Button */
"remotestate.remove" = "Supprimer";

/* Remote sheet Share Title */
"remotestate.removeDriver" = "Supprimer le pilote";

/* Remote Share sheet Sub Title */
"remotestate.removeDriverNotes" = "Supprimez le pilote pour reprendre le contrôle de votre télécommande et la partager avec quelqu'un d'autre.";

/* Renew Subscription - Remote State Button TextRenew Subscription - Remote State Button Text */
"remotestate.renew" = "Renouveler l'abonnement";

/* Subscription Cancelled - Remote State TitleSubscription Cancelled - Remote State Title */
"remotestate.subCancelledTitle" = "Abonnement annulé";

/* Subscription Expired - Remote State Title TextSubscription Expired - Remote State Title Text */
"remotestate.subExpiredTitle" = "Abonnement à distance expiréé";

/* Subscription Cancelled/Expired - Remote State BodySubscription Cancelled/Expired - Remote State Body */
"remotestate.subscriptionBody" = "Renouvelez votre abonnement maintenant pour restaurer vos services à distance.";

/* Stolen vehicle sheet secondary btn */
"remotestate.textCancel" = "Annuler";

/* Stolen vehicle sheet primary btn */
"remotestate.textConfirm" = "Confirmer";

/* Unable to Activate - Remote State BodyUnable to Activate - Remote State Body */
"remotestate.unableToActivateBody" = "Il y avait un problème lors du traitement de votre demande. Veuillez réessayer d'activer les services à distance.";

/* Unable to Activate - Remote State TitleUnable to Activate - Remote State Title */
"remotestate.unableToActivateTitle" = "Impossible d'activer la télécommande";

/* Body text for stolen vehicle */
"remotestate.vehicleStolenBody" = "Ce véhicule a été déclaré volé; certaines fonctionnalités ne sont pas disponibles. Pour obtenir de l’aide, communiquez avec le Centre de réponse Safety Connect.\nPour réactiver vos services à distance, appuyez sur « Réactiver » et suivez les instructions.";

/* Button text for stolen vehicle */
"remotestate.vehicleStolenBt" = "Nous joindre";

/* Secondary Button text for stolen vehicle */
"remotestate.vehicleStolenSecBt" = "Réactiver";

/* Title text for stolen vehicle */
"remotestate.vehicleStolenTitle" = "Véhicule signalé comme volé";

/* LMEX Auth Sheet Title */
"remotestate.verifyOwnership" = "Vérification de propriété";

/* LMEX Auth Sheet Sub Title */
"remotestate.verifyOwnershipDescription" = "Lorsque votre véhicule se trouve dans une zone ayant une bonne couverture cellulaire, appuyez sur le bouton SOS et suivez les instructions pour l’activation de «Activation de Remote».";

/* See FAQs button textSee FAQs button text */
"service.SeeFAQs" = "Voir FAQ";

/* Call Roadside button textCall Roadside button text */
"service.callRoadside" = "Appelez %0 Roadside";

/* Roadside card content textRoadside card content text */
"service.connectWithRoadside" = "Communiquez avec l'Assistance routière %0 pour obtenir des réponses à vos questions.";

/* SubTitleSubTitle */
"service.getHelpOnTheRoad" = "Obtenez de l'aide sur la route";

/* buttonbutton */
"service.makeAnAppointment" = "Prendre rendez-vous";

/* TitleTitle */
"service.roadsideAssistance" = "I'Assistance routière";

/* TitleTitle */
"service.serviceAppointments" = "Programmer un entretien";

/* Shop Announcements Title */
"shop.announcements.description" = "Voir les annonces %@";

/* Shop Announcements Title */
"shop.announcements.title" = "Annonces %@";

/* Shop issurance title */
"shop.insurance.offers" = "Insurance Offers";

/* Shop parts title */
"shop.parts.and.accessories" = "Parts and Accessories";

/* Shop parts subtitle */
"shop.shop.genuine.parts" = "Shop Genuine Parts";

/* Shop SXM title */
"shop.siriusXM" = "SiriusXM";

/* Shop SXM Active status */
"shop.siriusXM.active.status" = "Account status active";

/* Shop SXM InActive status */
"shop.siriusXM.inActive.status" = "Account status inactive";

/* Shop issurance subtitle */
"shop.view.insurance.offer" = "View your personalized offers";

/* Sign out */
"signOut.button.title" = "Se déconnecter";

/* Sign out cancel */
"signout.cancel" = "Annuler";

/* Sign out message question */
"signout.message.question" = "Êtes-vous certain de vouloir vous déconnecter?";

/* Sign out message */
"signout.message.text" = "Pour vous connecter, vous devrez saisir à nouveau votre nom d'utilisateur et votre mot de passe.";

/* closeclose */
"status.close" = "Fermer";

/* closedclosed */
"status.closed" = "Fermée";

/* status constant Driver Side */
"status.constant.Driver.Side" = "Côté conducteur";

/* status constant Passenger Side */
"status.constant.Passenger.Side" = "Côté passager";

/* status constant Rear Door */
"status.constant.Rear.Door" = "Porte arrière";

/* status constant Rear Window */
"status.constant.Rear.Window" = "Vitre arrière";

/* status constant door */
"status.constant.door" = "Porte";

/* status constant hatch */
"status.constant.hatch" = "hayon";

/* status constant hood */
"status.constant.hood" = "capot";

/* status constant moonroof */
"status.constant.moonroof" = "toit ouvrant";

/* status constant open */
"status.constant.open" = "ouvrir";

/* status constant trunk */
"status.constant.trunk" = "tronc";

/* status constant unlocked */
"status.constant.unlocked" = "déverrouille";

/* status constant window */
"status.constant.window" = "vitre";

/* doordoor */
"status.door" = "Porte";

/* doorsdoors */
"status.doors" = "Portières";

/* getting status */
"status.getting.status.text" = "Obtenir le statut";

/* goodgood */
"status.good" = "Bien";

/* hatchhatch */
"status.hatch" = "Hayon";

/* hoodhood */
"status.hood" = "Capot";

/* LockLock */
"status.lock" = "Verrouiller";

/* lockedlocked */
"status.locked" = "Verrouillé";

/* lowlow */
"status.low" = "faible";

/* Moon roofMoon roof */
"status.moonroof" = "toit ouvrant";

/* Moon roof openMoon roof open */
"status.moonroof.open" = "ouverte + toit ouvrant";

/* openopen */
"status.open" = "ouvertes";

/* request in progress */
"status.request.inProgress" = "Demande en cours";

/* tailgate */
"status.tailgate" = "Hayon";

/* tailgate text */
"status.tailgate.text" = "Hayon";

/* Tire PressureTire Pressure */
"status.tirePressure" = "Pression des pneus";

/* Tire Status UpdateTire Status Update */
"status.tireStatusUpdate" = "Statut des pneus actualisé: ";

/* Vehicle StatusVehicle Status */
"status.title" = "Statut";

/* trunktrunk */
"status.trunk" = "Coffre";

/* UnitUnit */
"status.unit" = "psi";

/* unlockedunlocked */
"status.unlocked" = "Déverrouillé";

/* vehicle information updatedvehicle information updated */
"status.vehicleInformation.Updated" = "Info du véhicule actualisée:";

/* windowwindow */
"status.window" = "Vitres";

/* windowswindows */
"status.windows" = "Vitres";

/* status disclaimer title*/
"status.disclaimer.title" = "Vérifier la fenêtre arrière";

/* status disclaimer subtitle*/
"status.disclaimer.subtitle" = "L'état de la fenêtre arrière manuellement ouverte ou fermée ne s'affiche pas dans l'app.";

/* status disclaimer cta*/
"status.disclaimer.cta" = "Annuler";

/* Active TextActive Text */
"subscription.active" = "Active";

/* Add Service Button TextAdd Service Button Text */
"subscription.addServices" = "Ajouter un service";

/* Auto Renew Off TextAuto Renew Off Text */
"subscription.autorenewOff" = "Renouvellement automatique désactivé";

/* Auto Renew On TextAuto Renew On Text */
"subscription.autorenewOn" = "Renouvellement automatique activé";

/* Disclamier Description TextDisclamier Description Text */
"subscription.content" = "Les services dépendent de la connexion à un réseau sans fil compatible 4G fourni par un fournisseur de services sans fil tiers.";

/* Disclamier Description Toyota text */
"subscription.content02" = "Content Toyota";

/* Disclamier Description Lexus TextDisclamier Description Lexus Text */
"subscription.content02Lexus" = "Lexus n’est pas responsable des interruptions de service du réseau cellulaire et n’offrira pas d’indemnisation en cas de disponibilité réduite du service.";

/* Disclamier Description Toyota TextDisclamier Description Toyota Text */
"subscription.content02Toyota" = "Toyota n’est pas responsable des interruptions de service du réseau cellulaire et n’offrira pas d’indemnisation en cas de disponibilité réduite du service.";

/* Disclamier Title TextDisclamier Title Text */
"subscription.disclamiertitle" = "Mention légale";

/* Add Service Button Text */
"subscription.enableAllTrials" = "Activer tous les essais";

/* Exit Button Text */
"subscription.exit" = "Quitter";

/* Paid Services TextPaid Services Text */
"subscription.servicePaidPackage" = "Services payants";

/* Trial Services TextTrial Services Text */
"subscription.serviceTrialPackage" = "Services d'essai";

/* Snippet Title */
"subscription.subscriptionExpiring" = "abonnement expire";

/* Subscriptions Title Text */
"subscription.subscriptionTitle" = "Abonnements";

/* Snippet Title */
"subscription.subscriptionsExpiring" = "abonnements expirent";

/* Trial Available TextTrial Available Text */
"subscription.trialAvailable" = "Essai disponible";

/* HealthHealth */
"tabWidget.health" = "Santé";

/* Remote TabRemote Tab */
"tabWidget.remote" = "À distance";

/* StatusStatus */
"tabWidget.status" = "Statut";

/* Find Tabbar button */
"tabbar.find" = "Trouver";

/* Key Fob Text */
"tabbar.keyfob" = "Porte-clés";

/* Pay Tabbar button */
"tabbar.pay" = "Payer";

/* Safety Recalls Text */
"tabbar.safetyrecalls" = "Rappels de Sécurité";

/* Tabbar button */
"tabbar.service" = "Service";

/* Service Campaigns Text */
"tabbar.servicecampaigns" = "Campagne de services";

/* Shop Tabbar button */
"tabbar.shop" = "Boutique";

/* Vehicle Alerts Text */
"tabbar.vehiclealerts" = "Alertes de véhicule";

/* Vehicle Health Report text */
"tabbar.vehiclehealthreport" = "Bilan de santé du véhicule";

/* Cancel button */
"vehicleSwitcher.cancel" = "Annuler";

/* Copy */
"vehicleSwitcher.copy" = "Copy";

/* Default button */
"vehicleSwitcher.defaultText" = "Primaire";

/* Disclaimer Text */
"vehicleSwitcher.disclaimerText" = "Image montrée à des fins d'illustration seulement. Le véhicule peut différer de l'illustration.";

/* Make Default button */
"vehicleSwitcher.makeDefault" = "Utiliser comme primarie";

/* Remove button */
"vehicleSwitcher.remove" = "Supprimer";

/* Remove confirmation */
"vehicleSwitcher.removeConfirmation" = "Voulez-vous vraiment supprimer ce véhicule de votre compte ?";

/* Remove Digital Key */
"vehicleSwitcher.removeDigitalKeyMessage" = "Vous et les détenteurs de clés partagées ne pourrez plus accéder à ce véhicule avec une Clé numérique.";

/* Remove Digital Key */
"vehicleSwitcher.removeDigitalKeyTitle" = "Êtes-vous certain de vouloir supprimer votre Clé numérique?";

/* Remove Vehicle */
"vehicleSwitcher.removeVehicle" = "Supprimer le véhicule";

/* Remove Vehicle Subscriptions */
"vehicleSwitcher.removeVehicleAllSubscriptions" = "Tous les services auxquels vous vous êtes abonné et les informations d'abonnement pour ce véhicule seront supprimés.";

/* Remove Vehicle Confirmation when active padi subscription */
"vehicleSwitcher.removeVehicleConfirmation" = "Une confirmation sera envoyée à l'adresse e-mail de ce compte.";

/* All subscriptions cancel info */
"vehicleSwitcher.removeVehicleSubscriptions" = "La sélection de « Supprimer le véhicule » annulera tous les abonnements pour le véhicule sélectionné.";

/* Select button */
"vehicleSwitcher.select" = "Sélectionner";

/* Vehicle disclaimer */
"vehicleSwitcher.vehicleDisclaimer" = "Le véhicule peut différer de l'illustration.";

/* Vehicle Switch title */
"vehicleSwitcher.vehicles" = "Véhicules";

/* VIN */
"vehicleSwitcher.vin" = "VIN";

/* active alert text */
"vehiclehealth.activealert" = "alerte de véhicule actives";

/* active alerts text */
"vehiclehealth.activealerts" = "alertes de véhicule actives";

/* active service campaign text */
"vehiclehealth.activeservicecampaign" = "campagne de service actif";

/* active service campaigns text */
"vehiclehealth.activeservicecampaigns" = "campagnes de service actif";

/* Call dealer text */
"vehiclehealth.callDealer" = "Appeler le concessionnaire";

/* Remedy tile header text */
"vehiclehealth.remedy" = "Remède";

/* Overview tile header text */
"vehiclehealth.overview" = "Aperçu";

/* Description tile header text */
"vehiclehealth.description" = "Description";

/* Dealer id tile sub text */
"vehiclehealth.dealerId" = "Identifiant du concessionnaire";

/* Nhtsa id tile sub text */
"vehiclehealth.nhtsaId" = "NHTSA ID";

/* Remedy Status tile sub header text */
"vehiclehealth.remedyStatus" = "Statut de la solution : ";

/* Engine oil good text */
"vehiclehealth.engineoilgood" = "Huile moteur bonne";

/* Engine oil low text */
"vehiclehealth.engineoillow" = "Niveau d'huile moteur bas";

/* good text */
"vehiclehealth.good" = "Bien";

/* Key fob good text */
"vehiclehealth.keyfobgood" = "Le niveau de la pile de la télécommande porte-clés est bon.";

/* Key fob low text */
"vehiclehealth.keyfoblow" = "La pile de la télécommande porte-clés est faible, veuillez la remplacer très bientôt.";

/* low text */
"vehiclehealth.low" = "Faible";

/* No Safety Recalls text */
"vehiclehealth.noSafetyRecalls" = "Aucune Rappels de Sécurité";

/* No service campaigns text */
"vehiclehealth.noservicecampaigns" = "Aucune Campagne de Service";

/* safety recall text */
"vehiclehealth.safetyRecall" = "rappel de sécurité actifs";

/* safety recalls text */
"vehiclehealth.safetyRecalls" = "rappels de sécurité actifs";

/* See report text */
"vehiclehealth.seereport" = "Voir le rapport";

/* Vehicle alert text */
"vehiclehealth.vehicleAlertGood" = "Aucune Alertes de véhicule";

/* App Suite Text */
"vehicleinfo.appsuite" = "Suite d'applications";

/* Ask Siri to help you with your vehicle Text */
"vehicleinfo.asksiri" = "Demandez à Siri de vous aider avec votre véhicule";

/* Connected services for your vehicle Text */
"vehicleinfo.connectedservicesforyourvehicle" = "Services connectés pour votre véhicule";

/* Connected Services Support Text */
"vehicleinfo.connectedservicessupport" = "Assistance Services Connectés";

/* Connect to in-vehicle apps Text */
"vehicleinfo.connecttoinvehicleapps" = "Connectez-vous aux applis embarquées";

/* VIN Copied Text */
"vehicleinfo.copied" = "Copié";

/* Customer Action Complete Text */
"vehicleinfo.customeractioncomplete" = "Action client terminée";

/* Dynamic Navi Text */
"vehicleinfo.dynamicnavi" = "Dynamic Navi";

/* Enter a nickname Text */
"vehicleinfo.enteranickname" = "(Entrez un surnom)";

/* Get support Text */
"vehicleinfo.getsupport" = "Obtenir de l'aide";

/* glovebox Text */
"vehicleinfo.glovebox" = "Boite à gants";

/* New update available Text */
"vehicleinfo.newupdateavailable" = "New update available";

/* Remove Vehicle Button */
"vehicleinfo.removevehicle" = "Supprimer le véhicule";

/* Save Button */
"vehicleinfo.save" = "Sauvegarder";

/* Siri Shortcuts Text */
"vehicleinfo.sirishortcuts" = "Raccourcis Siri";

/* Unable to update vehicle software Text */
"vehicleinfo.softwareerrornotification" = "Impossible de mettre à jour le logiciel du véhicule";

/* Software update available Text */
"vehicleinfo.softwareupdateavailable" = "Mise à jour disponible";

/* Up to date Text */
"vehicleinfo.softwareupdatecomplete" = "À jour";

/* Software update initiated Text */
"vehicleinfo.softwareupdateprogress" = "Mise à jour logicielle lancée";

/* Specs Text */
"vehicleinfo.specs" = "Spécifications";

/* Capabilities Text */
"vehicleinfo.vehiclecapabilities" = "Capacités du Véhicule";

/* Specs, manuals Text */
"vehicleinfo.specsmanuals" = "Spécifications, manuels";

/* Specs, manuals, dashboard lights Text */
"vehicleinfo.specsmanualsdashboardlights" = "Spécifications, manuels, voyants du tableau de bord...";

/* Subscriptions Text */
"vehicleinfo.subscriptions" = "Abonnements";

/* Update Nickname Text */
"vehicleinfo.updatenickname" = "Modifier le surnom";

/* Update your renewal date Text */
"vehicleinfo.updateyourrenewaldate" = "Update your renewal date";

/* Vehicle Identification Number Text */
"vehicleinfo.vehicleidentificationnumber" = "Plaque d'immatriculation";

/* Vehicle Software Text */
"vehicleinfo.vehiclesoftware" = "Logiciel de véhicule";

/* Update Initialized Text */
"vehicleinfo.updateInitialized" = "Mise à jour lancée";

/* Glovebox header Text */
"glovebox.glovebox" = "Boite à gants";

/* specsAndCapabilities tile text */
"glovebox.specsAndCapabilities" = "Spécifications et capacités";

/* manualsAndWarranties tile text */
"glovebox.manualsAndWarranties" = "Manuels et guides";

/* howToVideos tile text */
"glovebox.howToVideos" = "How-To \nVideos";

/* dashboardLights tile text */
"glovebox.dashboardLights" = "Guide des voyants du tableau de bord";

/* toyotaForFamilies tile text */
"glovebox.toyotaForFamilies" = "Toyota pour les familles";

/* proXSeats tile text */
"glovebox.proXSeats" = "Sièges IsoDynamic Performance";

/* seeFaq tile text */
"glovebox.seeFaq" = "Voir FAQ";

/* noResults text */
"glovebox.noResults" = "Aucun résultat trouvé";

/* search text */
"glovebox.search" = "Chercher";

/* noManualsAndWarranties text */
"glovebox.noManualsAndWarranties" = "Pas de manuels et de garanties";

/* noCapabilities text */
"glovebox.noCapabilities" = "Aucune capacité";

/* specifications text */
"glovebox.specifications" = "Caractéristiques";

/* capabilities text */
"glovebox.capabilities" = "Capacités";

/* noSpecifications text */
"glovebox.noSpecifications" = "Aucune spécification";

/* vehicleInfo text */
"glovebox.vehicleInfo" = "Informations sur le véhicule";

/* noDataFound text */
"glovebox.noDataFound" = "Aucune donnée disponible";
/* Charge history title */
"ev.history" = "Historique";

/* Clean assist description */
"ev.howCAworks" = "Comment fonctionne Clean Assist ?";

/* Start */
"ev.start" = "Démarrer";

/* Start Time */
"ev.startTime" = "Heure de début";

/* startTime Sub Heading */
"ev.startTimeSubHeading" = "Quand vous voulez que le véhicule commence à se recharger";

/* end Time Sub Heading text */
"ev.endTimeSubHeading" = "When you want vehicle recharged by";

/* same Start End Time */
"ev.sameStartEndTime" = "L'heure de début et de fin ne peut pas être la même";

/* wattTime StartTime SubHeading */
"ev.wtStartTimeSubHeading" = "Réglez l'heure à laquelle vous voulez que votre véhicule commence à se recharger";

/* wattTime EndTime Sub Heading */
"ev.wtEndTimeSubHeading" = "Réglez l'heure à laquelle vous souhaitez que votre véhicule soit rechargé en";

/* statistics Info */
"ev.statisticsInfo" = "Les statistiques de consommation d'énergie seront disponibles après votre premier mois complet de conduite.";

/* manual Schedule Heading */
"ev.manualScheduleHeading" = "Calendrier de charge manuelle";

/*
 evgo Free One Year Text */
"ev.evgoFreeOneYearText" = "1 an sans frais!";

/* evgo Free One Year */
"ev.evgoFreeOneYear" = "1 an sans frais!";

/* learn more button text */
"ev.learnMore" = "En savoir plus";

/* End Time Text */
"ev.endTime" = "Heure de fin";

/* Off Peak Hours Text */
"ev.offPeakHours" = "Définir l'horaire hors pointe";

/* Off Peak Schedule Text */
"ev.offpeakSchedule" = "Trafic léger";

/* No History Found text */
"ev.noCdrHistoryMessage" = "Aucun historique trouvé";

/* evmc Departure Time Info Text */
"ev.evmcDepartureTimeInfoText" = "Réglez l'heure à laquelle vous souhaitez que votre véhicule arrête de se recharger";

/* off-peak Description */
"ev.offpeakDescription" = "Recharge du véhicule en heures creuses entre ";

/* and text */
"ev.and" = " et ";

/* find out more text */
"ev.findOutMore" = "En savoir plus";

/* no Schedules Title */
"ev.noSchedulesTitle" = "Pas encore d'horaires de recharge";

/* no Schedules Subtitle */
"ev.noSchedulesSubtitle" = "Définissez des horaires de charge et contrôlez quand vous chargez votre véhicule.";

/* no Schedules Subtitle for PHEV */
"ev.phevNoSchedulesSubtitle" = "To use ECO charging or in-app schedule charging, you need to set the schedule in the vehicle head unit first. The schedule will sync with the app after the set up.";

/* Set timer in vehicle */
"ev.setTimerInVehicle" = "Régler la minuterie ici";

/* On text */
"onText" = "Au";

/* Off text */
"offText" = "Désactivé";

/* Charge ScheduleTile text */
"ev.evmcChargeSchedulePageTitleText" = "Barème des charges";

/* New ScheduleTile text */
"ev.evmcNewSchedulePageTitleText" = "Nouveau programme";

/* Home text */
"ev.home" = "Maison";

/* Charge Info text */
"ev.chargeInfo" = "Info sur la recharge";

 /* EVgo text */
 "ev.evgo" = "EVgo";

 /* chargepoint text */
 "ev.chargePoint" = "Borne de recharge";

/* register text */
"ev.register" = "S'inscrire";

/* Sign In text */
"ev.signIn" = "Se connecter";

/* evGo Account Found Text */
"ev.evGoAccountFoundText" = "Si vous avez déjà un compte EVgo avec cet e-mail, continuez à vous connecter :";

/* Forgot password text */
"ev.forgetPasswordText" = "Mot de passe oublié?";

/* clean Energy Graph Title */
"ev.cleanEnergyGraphTitle" = "Énergie propre chargée (kWh)";

/* co2 Emissions Graph Title */
"ev.co2EmissionsGraphTitle" = "Émissions de CO2 évitées (lb)";

/* ca Consent Preface Text */
"ev.caConsentPrefaceText" = "Les clients %0 peuvent conduire sans souci en sachant que leur véhicule utilise 100 % d'énergie renouvelable!\n\n";

/* what Is Clean Assist */
"ev.whatIsCleanAssist" = "Qu'est-ce que l'assistance propre?";

/* terms And Privacy */
"ev.termsAndPrivacy" = "Conditions et confidentialité";

/* Schedule text */
"ev.schedule" = "Calendrier";

/* Days of the week */
"ev.daysOfTheWeek" = "Jours de la semaine";

/* Everyday */
"ev.everyday" = "Tous les jours";

/* Account Linked text */
"ev.accountLinked" = "Compte lié";

/* Account Linked Description text */
"ev.accountLinkedText" = "Vous pouvez maintenant commencer à facturer chez n'importe quel partenaire\nstation avec l'application.";

/* Account Created text */
"ev.accountCreated" = "Compte créé";

/* Account Created Description text */
"ev.evgoAccountCreatedText" = "Vous pouvez maintenant commencer à charger à n'importe quelle station partenaire avec l'application.";

/* ev Departure Time */
"ev.departureTime" = "Heure de départ";

/* Vehicle software title text */
"vehicleSoftware.vehicleSoftware" = "Logiciel de véhicule";

/* Installed detail text */
"vehicleSoftware.installed" = "Installé";

/* Update failed view */
"vehicleSoftware.updateFailed" = "Échec de la mise à jour logicielle";

/* Update failed view subtitle */
"vehicleSoftware.updateFailedSubtitle" = "Le logiciel sera mis à jour la prochaine fois que vous démarrerez votre véhicule.";

/* Back to Dashboard button */
"vehicleSoftware.backToDashboard" = "Retour au tableau de bord";

/* Up to date view */
"vehicleSoftware.upToDate" = "Votre logiciel est à jour";

/* Up to date view subtitle */
"vehicleSoftware.upToDateSubtitle" = "Il n’y a pas de mise à jour logicielle pour l’instant.";

/* Status expandable text */
"vehicleSoftware.status" = "Statut";

/* Instructions expandable text */
"vehicleSoftware.instructions" = "Instructions";

/* What's New expandable text */
"vehicleSoftware.whatsNew" = "Quoi de neuf";

/* Owner's Manual expandable text */
"vehicleSoftware.ownersManual" = "Manuel du propriétaire";

/* Working Time expandable text */
"vehicleSoftware.workingTime" = "Temps de travail";

/* Previous update expandable text */
"vehicleSoftware.previousUpdate" = "Mises à jour précédentes";

/* Software Versions expandable text */
"vehicleSoftware.softwareVersions" = "Versions du logiciel";

/* Release Notes text */
"vehicleSoftware.releaseNotes" = "Notes de mise à jour";

/* Update Later button text */
"vehicleSoftware.updateLater" = "Mettre à jour plus tard";

/* updateLater description */
"vehicleSoftware.confirmUpdateLater" = "Si vous sélectionnez « Mettre à jour plus tard », les fonctionnalités décrites dans la rubrique « Quoi de neuf » ne seront pas disponibles tant que la mise à jour logicielle ne sera pas terminée.";

/* Agree button text */
"vehicleSoftware.agree" = "Accepter et mettre à jour";

/* Agree title */
"vehicleSoftware.agreeTitle" = "Mise à jour logicielle lancée";

/* Agree description */
"vehicleSoftware.agreeDescription" = "La mise à jour logicielle commencera après le prochain redémarrage du véhicule. Le véhicule peut être conduit pendant la mise à jour logicielle. Vous recevrez un message de confirmation une fois la mise à jour logicielle terminée.";

/* Important Details expandable text */
"vehicleSoftware.importantDetails" = "Détails importants";

/* Caution expandable text */
"vehicleSoftware.caution" = "Mise en garde";

/* update button expandable text */
"vehicleSoftware.updateHistory" = "Historique des mises à jour";

/* update available expandable text */
"vehicleSoftware.updateAvailable" = "Mise à jour disponible";

/* Current Version text */
"vehicleSoftware.currentVersion" = "Version actuelle:";

/* New Version text */
"vehicleSoftware.newVersion" = "Nouvelle version:";

/* See Update button text */
"vehicleSoftware.seeUpdate" = "Voir la mise à jour";

/* Version concatenated text */
"vehicleSoftware.version" = "Version";

/* Continue button text */
"vehicleSoftware.continue" = "Continuer";

/* confirmUpdateLater button text */
"vehicleSoftware.updateLaterConfirm" = "Êtes-vous certain?";

/* sure button text */
"vehicleSoftware.sure" = "Je suis certain";

/* go back button text */
"vehicleSoftware.goBack" = "Retour";

/* Go To Dashboard button text */
"vehicleSoftware.goToDashboard" = "Allez au tableau de bord";

/* Done text*/
"common.done" = "Terminé";

/* Charging Interrupted text */
"charging.chargingInterrupted" = "Interruption de charge";

/* charging Error Confirmation Text */
"charging.chargingErrorConfirmationText" = "Veuillez vérifier la connexion à la prise.";

/* Report Station text */
"charging.reportStationText" = "Station de rapport";

/* Energy update error message */
"charging.energyUpdateError" = "Les informations seront automatiquement mises à jour lorsque nous les recevrons de la station de charge";

/* Energy update error title */
"charging.energyUpdateErrorTitle" = "Un moment";

/* Estimate range text */
"charging.estimateRangeText" = "est. de la rangée sans climatiseur";

/* Estimate Range Text */
"ev.estimateRangeText" = "est. de la rangée sans climatiseur";

/* HV range */
"ev.gasRange" = "La rangée sur HV";

/* EV Range */
"ev.evRange" = "La rangée sur EV";

/* Charge description tooltip subtitle */
"charging.chargeDescToolTipSubTitle" = "L’écran multifonction indiquera une autonomie estimée plus basse avec la climatisation ou le chauffage en marche. L’autonomie réelle variera.";

/* Charge description tooltip title */
"charging.chargeDescToolTipTitle" = "Voir l’écran du véhicule";

/* charging session stopped title text */
"charging.chargingSessionStoppedTitle" = "Vous avez arrêté de charger avant qu'il ne commence";

/* charging session stopped message */
"charging.chargingSessionStoppedMessage" = "Vous pouvez réessayer en appuyant sur Démarrer la charge sur l'écran suivant.";

/* Time text */
"ev.time" = "Temps";
/* Description: Subtitle text for no schedule */
"ev.evmcNoScheduleSubtitleText" = "Créez un horaire pour recharger votre véhicule";

/* Description: Title text for no schedule */
"ev.evmcNoScheduleTitleText" = "Recharger dans l’app";

/* Create Schedule button text */
"ev.createSchedule" = "Créer un horaire";

/* Create Schedule Toast text */
"ev.scheduleCreatedText" = "Votre emploi du temps est créé.";

/* Deleted Schedule Toast text */
"ev.scheduleDeletedText" = "Votre horaire a été supprimé.";

/* Updated Schedule Toast text */
"ev.scheduleUpdatedText" = "Votre emploi du temps a été mis à jour.";

/* guestDriver text */
"gd.guestDrivers" = "Chauffeurs invités";

/* guestDriver desc */
"gd.desc" = "Partagez les commandes à distance de votre véhicule avec d'autres et suivez leurs limites de vitesse sur l'application.";

/* guestDriver text */
"gd.guestDrivers" = "Chauffeurs invités";

/* guestDriver desc */
"gd.desc" = "Partagez les commandes à distance de votre véhicule avec d'autres et suivez leurs limites de vitesse sur l'application.";

/* guestDriver valet text */
"gd.valet" = "Valet";

/* guestDriver AlertsOn text */
"gd.alertsOn" = "Alerts Au";

/* guestDriver AlertsOff text */
"gd.alertsOff" = "Alerts Désactivé";

/* guestDriver InviteDriver text */
"gd.inviteDriver" = "Invitar Conductor";

/* Share Remote Label */
"gd.shareRemote" = "Partager à distance";

/* Add Driver CTA */
"gd.addDriver" = "Ajouter un pilote";

/* Remote Access Title */
"gd.remoteAccessTitle" = "Accès à distance";

/* Remote Access SubTitle */
"gd.remoteAccessSubTitle" = "Donner accès au conducteur aux commandes à distance avec application mobile (moteur, serrure, dangers, …)";

/* Search by Email or Phone */
"gd.searchHint" = "Chercher par courriel ou numéro de téléphone";

/* Hint For Toyota */
"gd.hintToyotaApp" = "Le conducteur doit disposer de l'application Toyota.";

/* Hint For Lexus */
"gd.hintLexusApp" = "Le conducteur doit disposer de l'application Lexus.";

/* Hint For Subaru */
"gd.hintSubaruApp" = "Le conducteur doit disposer de l'application Subaru.";

/* Invite Driver CTA */
"gd.inviteDriver" = "Inviter le conducteur";

/* No Result Title */
"gd.noResultTitle" = "Aucun résultat";

/* No Result SubTitle */
"gd.noResultSubTitle" = "Nous n'avons trouvé personne à l'aide de cette adresse e-mail ou de ce numéro de téléphone. Veuillez réessayer.";

/* Invite Sent Title */
"gd.inviteSentTitle" = "Invitation envoyée";

/* Invite Sent Toyota SubTitle */
"gd.inviteSentToyotaSubTitle" = "Pour commencer à utiliser la télécommande, votre invité devra télécharger l'application Toyota. Vous pouvez également définir leurs limites de conduite sur l'écran suivant.";

/* Invite Sent Lexus SubTitle */
"gd.inviteSentLexusSubTitle" = "Pour commencer à utiliser la télécommande, votre invité devra télécharger l'application Lexus. Vous pouvez également définir leurs limites de conduite sur l'écran suivant.";

/* Invite Sent Subaru SubTitle */
"gd.inviteSentSubaruSubTitle" = "Pour commencer à utiliser la télécommande, votre invité devra télécharger l'application Subaru. Vous pouvez également définir leurs limites de conduite sur l'écran suivant.";

/* guestDriver speed text */
"gd.speed" = "La vitesse";

/* guestDriver miles text */
"gd.milesText" = "Nbre total de kilomètres";

/* guestDriver miles unit */
"gd.milesUnit" = "kilomètres";

/* guest driver mileage text */
"gd.mileageText" = "Kilométrage";

/* guestDriver area text */
"gd.area" = "Area";

/* guestDriver curfew text */
"gd.curfew" = "Couvre-feu";

/* guestDriver time text */
"gd.time" = "Temps";

/* guestDriver ignition text */
"gd.ignition" = "Allumage";

/* guestDriver profile updated success */
"gd.profileUpdatedSuccessfully" = "Profil mis à jour avec succès";

/* plugged in text */
"charging.pluggedIn" = "Branché";
/* Okay text */
"common.okay" = "Okay";

/* Month text */
"common.month" = "Mois";
/* Delete Schedule Text */
"ev.deleteSchedule" = "Supprimer l'horaire";

/* Delete Schedule Confirmation Text */
"ev.deleteScheduleConfirmation" = "Voulez-vous vraiment supprimer ce programme ?";

/* Yes, Delete button text */
"ev.yesDeleteButtonText" = "Oui, supprimer";

/* EV schedule days of week validation text */
"ev.evmcDaysOfWeekValidationText" = "Veuillez sélectionner le(s) jour(s) de la semaine";

/* Monday text */
"common.monday" = "Lundi";
/* Tuesday text */
"common.tuesday" = "Mardi";
/* Wednesday text */
"common.wednesday" = "Mercredi";
/* Thursday text */
"common.thursday" = "Jeudi";
/* Friday text */
"common.friday" = "Vendredi";
/* Saturday text */
"common.saturday" = "Samedi";
/* Sunday text */
"common.sunday" = "Dimanche";
/* Not Now text */
"common.notNow" = "Pas maintenant";

/* Enroll text */
"ev.enroll" = "inscrire";

/* View text */
"ev.view" = "Voir";

/* Charging est text */
"ev.withoutAC" = "Est. sans clim./chauffage. L’autonomie réelle variera.";

/* Visa title */
"evWallet.visaTitle" = "Visa";

/* American Express title */
"evWallet.amexTitle" = "American Express";

/* Chase title */
"evWallet.chaseTitle" = "Chase";

/* Discover title */
"evWallet.discoverTitle" = "Discover";

/* JCB title */
"evWallet.jcbTitle" = "JCB";

/* MasterCard title */
"evWallet.mastercardTitle" = "MasterCard";

/* UnionPay title */
"evWallet.unionPayTitle" = "UnionPay";

/* Default Card text */
"evWallet.defaultCardText" = "Carte par défaut";

/* Exp text */
"evWallet.expText" = "Exp ";

/* Card Set to Default text */
"evWallet.cardAlreadyDefaultTitle" = "Carte définie par défaut";

/* cardAlreadyDefaultDescription */
"evWallet.cardAlreadyDefaultDescription" = "The %0 card ending in %1 is already set to default";

/* Odometer text */
"common.odometer" = "Odomètre";

/* Brakes Text */
"common.brakes" = "Freins";

/* Clutch Text */
"common.clutch" = "Embrayage";

/* Electrical Text */
"common.electrical" = "Électrique";

/* Motor Text */
"common.engine" = "Moteur";

/* Exhaust Text */
"common.exhaust" = "Échappement";

/* Fuel Text */
"common.fuel" = "Le carburant";

/* Maintenance Text */
"common.maintenance" = "Entretien";

/* Suspension Text */
"common.suspension" = "Suspension";

/* Transmission Text */
"common.transmission" = "Transmission";

/* Done button */
"evWallet.doneButton" = "Terminé";

/* Default title */
"evWallet.defaultTitle" = "Primaire";

/* Make Default Button */
"evWallet.makeDefaultButton" = "Utiliser comme primaire";

/* setDefaultDescription */
"evWallet.setDefaultDescription" = "The %0 card ending in %1 was set default to your wallet.";

/* Add Card title */
"evWallet.addCardTitle" = "Ajouter une carte";

/* Add Card agreement */
"evWallet.addCardAgreeement" = "J'accepte que l'application %0 stocke mes informations de carte de crédit.";

/* Save button */
"evWallet.saveButton" = "Sauvegarder";

/* Billing Address title */
"evWallet.billingAddressTitle" = "adresse de facturation";

/* cityTextFieldPlaceholder */
"evWallet.cityTextFieldPlaceholder" = "Ville ( Plano)";

/* stateTextFieldPlaceholder */
"evWallet.stateTextFieldPlaceholder" = "Province ( TX )";

/* zipCodeTextFieldPlaceholder */
"evWallet.zipCodeTextFieldPlaceholder" = "Code postal";

/* countryTextFieldPlaceholder */
"evWallet.countryTextFieldPlaceholder" = "Pays ( US )";

/* cardInfoTitle */
"evWallet.cardInfoTitle" = "Informations sur la carte";

/* creditCardNumberTextFieldPlaceholder */
"evWallet.creditCardNumberTextFieldPlaceholder" = "Numéro de Carte de Crédit";

/* firstNamePlaceholder */
"evWallet.firstNamePlaceholder" = "Prénom";

/* lastNamePlaceholder */
"evWallet.lastNamePlaceholder" = "Nom de famille";

/* City Text */
"common.city" = "Ville";

/* State Text */
"common.state" = "Province";

/* Zip Code */
"common.zip" = "Code postal";

/* Country Text*/
"common.country" = "Pays";

/* expDatePlaceholder */
"evWallet.expDatePlaceholder" = "Exp ( 09/2030 )";

/* CVV Code placeholder */
"evWallet.cvvCodePlaceholder" = "Code CVV";

/* Exp Date Dash */
"evWallet.expDateDash" = "/";

/* Charging Stations title */
"evWallet.chargingStationsTitle" = "Charging Stations";

/* Transactions title */
"evWallet.multipleTransactionsTitle" = "Transactions";

/* No Transactions Found title */
"evWallet.noTransactionTitle" = "No Transactions Found";

/* Subscriptions title */
"evWallet.subscriptionsTitle" = "Abonnements";

/* Subscriptions description */
"evWallet.subscriptionsDescription" = "Manage Payments";

/* Wallet title */
"evWallet.walletTitle" = "Portefeuille";

/* No Wallet Card title */
"evWallet.noWalletCardTitle" = "Set up your\npayment methods\nfor the app";

/* Remove Card title */
"evWallet.removeCardTitle" = "Supprimer la carte";

/* Remove Card message */
"evWallet.removeCardMessage" = "Voulez-vous vraiment supprimer cette carte ?";

/* Yes, Remove button */
"evWallet.yesRemoveCardButton" = "Oui, supprimer";

/* Cancel button */
"evWallet.cancelRemoveCardButton" = "Annuler";

/* Delete title */
"evWallet.successfulCardRemovalTitle" = "Effacer";

/* Successful card removal message */
"evWallet.successfulCardRemovalMessage" = "The %0 card ending in %1 was deleted from your wallet";

/* Unsuccessful card removal title */
"evWallet.unsuccessfulCardRemovalTitle" = "Ajouter une nouvelle méthode de paiement";

/* Unsuccessful card removal message */
"evWallet.unsuccessfulCardRemovalMessage" = "Le dossier doit comporter au moins une carte. Veuillez ajouter une carte de remplacement avant de supprimer celle-ci.";

/* Transactions title */
"evWallet.transactionsTitle" = "Transactions";

/* ChargePoint title */
"evWallet.chargepointTitle" = "Borne de recharge";

/* Ionna title */
"evWallet.ionnaTitle" = "IONNA";

/* Five dots placeholder */
"evWallet.fiveDots" = "•••••";

/* Card added title */
"evWallet.cardAddedTitle" = "Card Added";

/* Card added message */
"evWallet.cardAddedMessage" = "The card ending in %0 was added to your wallet";

/* Card add failed title */
"evWallet.cardAddFailedTitle" = "Quelque chose s'est mal passé";

/* Card add failed message */
"evWallet.cardAddFailedMessage" = "Error getting payment methods";

/* Card Transaction */
"evWallet.transaction" = "Transaction";

/* Card Transaction Product */
"evWallet.transactionProduct" = "Product";

/* Card Transaction Description */
"evWallet.transactionDescription" = "Description";

/* Card Transaction Funding */
"evWallet.transactionFunding" = "Funding";

/* Card Transaction Status */
"evWallet.transactionStatus" = "Status";

/* Card Transaction Total */
"evWallet.transactionTotal" = "Total";

/* Card Transaction Charging */
"evWallet.transactionCharging" = "Charging";

/* dsaPreferredDealer text */
"service.preferredDealer" = "Concessionnaire préféré";

/* Pick Up only instructions text */
"service.puoInstructions" = "The dealer will contact you regarding pick only details. Locations will need to be with in 15 miles of the delaership.";

/* Confirm button text */
"service.confirmBtn" = "Confirm";

/* Change Transportation Button text */
"service.changeTransportation" = "Change Transportation";

/* dsaNoPreferredDealerSelected text */
"service.dsaNoPreferredDealerSelected" = "Aucun concessionnaire préféré sélectionné";

/* dsaVehicleDueForMaintenance text */
"service.dsaVehicleDueForMaintenance" = "Votre véhicule doit faire l'objet d'un entretien.";

/* dsaVehicleNotDueForMaintenance text */
"service.dsaVehicleDueForMaintenance" = "Votre véhicule n'a pas besoin d'entretien.";

/* dsaMakeAppointment text */
"service.dsaMakeAppointment" = "Prendre rendez-vous";

/* dsaDueForMaintenance text */
"service.dsaDueForMaintenance" = "En raison de l'entretien";

/* dsaMaintenanceSchedule text */
"service.dsaMaintenanceSchedule" = "Calendrier d'entretien";

/* dsaAppointments text */
"service.dsaAppointments" = "Rendez-vous";

/* dsaUpcomingAndPastService text */
"service.dsaUpcomingAndPastService" = "Service à venir et passé";

/* dsaChangePreferredDealer text */
"service.changePreferredDealer" = "Changer de concessionnaire";

/* dsaSetPreferredDealer */
"service.setPreferredDealer" = "Définir comme revendeur préféré";

/* dsaTransportation header */
"service.transportation" = "Transport";

/* dsaAccessibility header */
"service.accessibility" = "Accessibilité";

/* dsaPaymentMethods header */
"service.paymentMethods" = "Modes de paiement";

/* dsaAmenities header */
"service.amenities" = "Agréments";

/* dsaServices header */
"service.services" = "Prestations de service";

/* dsaServiceHours header */
"service.serviceHours" = "Heures de service";

/* dsaCall text */
"service.call" = "Appel";

/* dsaWebsite text */
"service.website" = "Site Internet";

/* dsaDirections text */
"service.directions" = "Directions";

/* dsaDealerDetails text */
"service.dealerDetails" = "Détails du concessionnaire";

/* dsaMonday text */
"service.monday" = "Lundi";
/* dsaTuesday text */
"service.tuesday" = "Mardi";
/* dsaWednesday text */
"service.wednesday" = "Mercredi";
/* dsaThursday text */
"service.thursday" = "Jeudi";
/* dsaFriday text */
"service.friday" = "Vendredi";
/* dsaSaturday text */
"service.saturday" = "Samedi";
/* dsaSunday text */
"service.sunday" = "Dimanche";
/* dsaClosed text */
"service.closed" = "fermée";
/* dsaExitAppointment text */
"service.exitAppointment" = "Quitter le rendez-vous";
/* dsaConfirmationExit text */
"service.confirmationExit" = "Êtes-vous sûr de vouloir quitter ce \n rendez-vous ?";
/* dsaYesExit text */
"service.yesExit" = "Oui, sortie";
/* dsaContinueAppointment text */
"dsaContinueAppointment" = "Continuer le rendez-vous";
/* dsaUpcoming */
"service.upcoming" = "A venir";
/* dsaPast */
"service.past" = "Passé";
/* dsaNoUpcomingAppointment */
"service.noUpcomingAppointment" = "Pas de rendez-vous à venir";
/* dsaNoPastAppointment */
"service.noPastAppointment" = "Aucun rendez-vous passé";
/* dsaNoUpcomingAppointmentDetail */
"service.noUpcomingAppointmentDetail" = "Les rendez-vous que vous créez\n apparaîtra ici.";
/* dsaNoPastAppointmentDetail */
"service.noPastAppointmentDetail" = "Les rendez-vous terminés ou annulés apparaîtront ici.";

/* dsaDone text */
"service.done" = "Terminé";
/* dsaSuccessPreferredDealer text */
"service.successPreferredDealer" = "Vous avez défini votre concessionnaire préféré.";
/* dsaSuccess */
"service.success" = "Succès";
/* dsaSelectDealer */
"service.selectDealer" = "Sélectionnez un revendeur";
/* dsaDealrSearchPrompt */
"service.dealerSearchPrompt" = "Rechercher un nom, une ville ou un code postal";
/*dsaDealerFilters*/
"service.filters" = "Filtres";
/*dsaDealerApplyFilters*/
"service.applyFilters" = "Appliquer des filtres";
/*dsaDealerResetFilters*/
"service.reset" = "Le réinitialiser";
/*dsaSmartPathDealersOnly*/
"service.smartpathOnly" = "Concessionnaires Smartpath seulement";
/*dsaTenMiles*/
"service.tenMiles" = "10 Miles";
/*dsaTwentyFiveMiles*/
"service.twentyFiveMiles" = "25 Miles";
/*dsaFiftyMiles*/
"service.fiftyMiles" = "50 Miles";
/* dsaAddRecord */
"service.addrecord" = "Ajouter un enregistrement";
/*dsaServiceAdvisor*/
"service.serviceAdvisor" = "Conseiller du service";
/*dsaConfirmAppointment*/
"service.confirmAppointment" = "Confirmer le rendez-vous";
/*dsaLastKnownMileage*/
"service.lastKnownMileage" = "Dernier kilométrage connu";
/*dsaAppointmentConfirmDisclaimer*/
"service.appointmentConfirmDisclaimer" = "En cliquant sur ”Confirmer le rendez-vous”, vous consentez à recevoir des appels marketing ou des messages texte composés automatiquement au numéro fourni. Le consentement n'est pas une condition d'achat. Des frais de messagerie et de données s'appliquent.";
/*dsaAdditionalComments*/
"service.additionalComments" = "Commentaires supplémentaires";
/*dsaConfirmingApopintment*/
"service.confirmingAppointment" = "Confirmation de rendez-vous";
/*dsaConfirmingAppointmentWait*/
"service.confirmingAppointmentWait" = "Veuillez patienter pendant la confirmation de votre rendez-vous.";
/*dsaAppointmentConfirmed*/
"service.appointmentConfirmed" = "Rendez-vous confirmé !";
/*dsaSomethingWentWrong*/
"service.somethingWentWrong" = "Quelque chose s'est mal passé";
/*dsaAppointmentError*/
"service.appointmentError" = "Un problème est survenu lors de la prise de rendez-vous. Veuillez confirmer à nouveau.";
/*dsaThreeAppointmentsMax*/
"service.threeAppointmentError" = "Désolé, nous n'autorisons que 3 rendez-vous ouverts par véhicule. Essayez de planifier une fois que vous avez terminé un rendez-vous.";
/*editAppointmentHeading*/
"service.editAppointmentHeading" = "Continuer l'édition";
/*editAppointmentSubHeading*/
"editAppointmentSubHeading" = "La modification des services réinitialisera toutes les autres sélections de rendez-vous.";
/*dsaCancelAppointment*/
"service.cancelAppointment" = "Annuler rendez-vous";
/*dsaSureCancelAppointment*/
"service.sureCancelAppointment" = "Êtes-vous sûr de vouloir annuler ce rendez-vous ?";
/*dsaYesCancel*/
"service.yesCancel" = "Oui, Annuler";
/*dsaWaitAppointmentCancel*/
"service.waitCancelAppointment" = "Veuillez patienter pendant l'annulation de votre rendez-vous.";
/*dsaCancelAppointmentError*/
"service.cancelAppointmentError" = "Un problème est survenu lors de l'annulation du rendez-vous. Veuillez confirmer à nouveau.";
/*dsaAppointmentCancelled*/
"service.appointmentCancelled" = "Rendez-vous annulé !";
/*dsaDealerTimezone*/
"service.dealerTimezone" = "*Tous les horaires disponibles sont dans le fuseau horaire du concessionnaire";

/* Nearby Stations */
"findStations.NearbyStations" = "Stations à proximité";

/* Partners Filter */
"findStations.partners" = "Les partenaires";

/* Current Location */
"findStations.currentLocation" = "Localisation actuelle";

/* Station Details */
"findStations.InNetwork" = "Detalles de la estación de carga";
"findStations.partnerStationHeading" = "Sur réseau";
"findStations.nonPartnerStationHeading" = "Hors réseau";
"findStations.SendToCar" = "Envoyer à la voiture";
"findStations.Pricing" = "Prix";
"findStations.Directions" = "Itinéraire";
"findStations.UnlockStation" = "Déverrouiller station";
"findStations.StartCharging" = "Démarrer la charge";
"findStations.PlugTypes" = "Types de prises";
"findStations.Favorites" = "Favoris";
/* Search by address or zip code placeholder */
"findStations.SearchPlaceholder" = "Recherche par adresse ou code postal";
/* Available Plugs text */
"findStations.AvailablePlugs" = "Connecteurs disponibles";

/* Charge management within the app is only available for network stations. */
"findStations.ChargingNotAvailable" = "Cette station ne fait pas partie du réseau, alors la recharge n’est pas disponible depuis l’appli.";

/* Clear All filter button */
"findStations.ClearAll" = "Tout effacer";

/* Partners filter button */
"findStations.Partners" = "Les partenaires";

/* Count Nearby Text */
"findStations.countNearBy" = "à proximité";

/* Open 24 Hours text */
"findStations.Open24Hours" = "Ouvert 24 heures";

/* Chargers text */
"findStations.Chargers" = "Chargeurs";

/* Level text */
"findStations.Level" = "Niveau";

/* Call Charge Station text */
"findStations.CallChargeStation" = "Appeler la station de charge";

/* Website text */
"findStations.Website" = "Site web";
/* Open 24 Hours text */
"findStations.Open24Hours" = "Ouvert 24 heures";
/* Departure Time */
"ev.departureTime" = "Heure de départ";

/* Tesla Stations */
"tesla.station.adaptor.warning" = "Adaptor may be needed to charge at this station.";
