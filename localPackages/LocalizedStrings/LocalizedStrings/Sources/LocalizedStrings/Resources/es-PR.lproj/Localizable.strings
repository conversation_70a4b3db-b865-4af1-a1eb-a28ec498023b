/*Date & Time Title*/
"service.datetime" = "Fecha y hora";

/* Avaiable Times Title*/
"service.AvailableTimes" = "Horarios disponibles";

/* Custom Service Title */
"service.customService" = "Custom Service";

/* Biometry_faceid_failed_enter_passcode */
"Biometric.Biometry_faceid_failed_enter_passcode" = "Face ID está bloqueado ahora, debido a demasiados intentos fallidos. Introduzca la contraseña para desbloquear Face ID.";

/* Biometry_faceid_failed_reason */
"Biometric.Biometry_faceid_failed_reason" = "Face ID no reconoce su cara. Por favor, intente de nuevo.";

/* Biometry_faceid_not_enrolled */
"Biometric.Biometry_faceid_not_enrolled" = "No hay ninguna cara asociada con este dispositivo. Por favor, vaya a Configuración en su dispositivo -> Face ID para asociar su cara con este dispositivo.";

/* Biometry_login_to_OneApp */
"Biometric.Biometry_login_to_OneApp" = "Autentícate para continuar";

/* Biometry_not_available_note */
"Biometric.Biometry_not_available_note" = "La autenticación biométrica no está disponible para este dispositivo.";

/* Biometry_set_passcode_to_use_faceid */
"Biometric.Biometry_set_passcode_to_use_faceid" = "Por favor configurar el código de acceso de dispositivo a usar Face ID para la autenticación.";

/* Biometry_set_passcode_to_use_touchid */
"Biometric.Biometry_set_passcode_to_use_touchid" = "Por favor configurar el código de acceso de dispositivo a usar Touch ID para la autenticación.";

/* Biometry_touchid_failed_enter_passcode */
"Biometric.Biometry_touchid_failed_enter_passcode" = "Touch ID está bloqueado ahora, debido a demasiados intentos fallidos. Introduzca la contraseña para desbloquear Touch ID.";

/* Biometry_touchid_failed_reason */
"Biometric.Biometry_touchid_failed_reason" = "Touch ID no reconoce la huella dactilar. Por favor, inténtelo de nuevo.";

/* Biometry_touchid_not_enrolled */
"Biometric.Biometry_touchid_not_enrolled" = "No hay una huella dactilar registrada en el dispositivo. Por favor, vaya a Configuración del dispositivo y regístrela.";

/* DKPopup CtaHeaderLabel */
"DKPopup.ConnectVehicleCtaHeaderLabel" = "Administrar llave";

/* DKPopup CtaTitleLabel */
"DKPopup.ConnectVehicleCtaTitleLabel" = "Conectar al vehículo";

/* DKPopup Description */
"DKPopup.ConnectVehicleDescription" = "Utilice su dispositivo móvil como llave para encender el motor,  cerrar / abrir su vehículo. Puede compartir Digital Key con otras personas y administrar su acceso.";

/* DKPopup Image Description */
"DKPopup.ConnectVehicleImageDescription" = "Encienda, cierre y abra su vehículo y comparta una llave con otros.";

/* DKPopup Image Header */
"DKPopup.ConnectVehicleImageHeader" = "Su teléfono es la llave";

/* DKPopup Title */
"DKPopup.ConnectVehicleTitle" = "Digital key";

/* DKPopup CtaTitleLabel */
"DKPopup.DKErrorNotDownloadedCtaTitleLabel" = "Reintentar";

/* DKPopup Description */
"DKPopup.DKErrorNotDownloadedDescription" = "Confirme que su dispositivo móvil tenga suficiente carga de batería, conexión a Internet o celular y que el Bluetooth esté activado. Si continúa viendo este mensaje, vuelva a intentarlo más tarde.";

/* DKPopup Title */
"DKPopup.DKErrorNotDownloadedTitle" = "Digital Key no descargada";

/* DKPopup Description */
"DKPopup.DKNoInternetDescription" = "Sin conexión a Internet. Compruebe su configuración e inténtelo de nuevo.";

/* DKPopup Shared TitleDKPopup Shared Title */
"DKPopup.DKSharedInviteTitle" = "Compartió un Digital Key";

/* DKPopup Resend Invite Error TitleDKPopup Error Title */
"DKPopup.DKPersonaResendFailedTitle" = "";

/* DKPopup Resend Invite Error DescriptionDKPopup Error Description */
"DKPopup.DKPersonaResendFailedDescription" = "Esta llave compartida ya fue aceptada. Elimine el usuario con quien la compartió y vuelva a enviar la llave compartida.";

/* DKPopup Resend Invite Error DescriptionDKPopup Error Description */
"DKPopup.DKPersonaResendFailedPairedDescription" = "Guest has Active digital key paired with vehicle.";

/* DKPopup CtaHeaderLabel */
"DKPopup.DownloadGuestKeyCtaHeaderLabel" = "Descargar la llave de un invitado";

/* DKPopup Description */
"DKPopup.DownloadGuestKeyDescription" = "El Digital Key de este vehículo ya se configuró en otro dispositivo. ¿Quiere transferir esa llave a este dispositivo?";

/* DKPopup CtaTitleLabel */
"DKPopup.DownloadGuestKeySubTitleLabel" = "Conectar al vehículo";

/* DKPopup Title */
"DKPopup.DownloadGuestKeyTitle" = "Transferir el Digital Key Principal";

/* DKPopup CtaTitleLabel */
"DKPopup.TurnOnBluetoothCtaTitleLabel" = "Reintentar";

/* DKPopup Description */
"DKPopup.TurnOnBluetoothDescription" = "Se requiere Bluetooth para usar Digital Key.\nActive el Bluetooth y vuelva a intentarlo.";

/* DKPopup Title */
"DKPopup.TurnOnBluetoothTitle" = "Activar el Bluetooth";

/* DigitalKey Cancel */
"DigitalKey.Cancel" = "Cancelar";

/* DigitalKey DigitalKeyTitle */
"DigitalKey.DigitalKeyTitle" = "Digital Key";

/* DigitalKey Download */
"DigitalKey.Download" = "Descargar";

/* DigitalKey DownloadDigitalKey */
"DigitalKey.DownloadDigitalKey" = "Descargar Digital Key";

/* DigitalKey DownloadForAnotherVehicle */
"DigitalKey.DownloadForAnotherVehicle" = "Digital Key se está descargando actualmente en otro vehículo.";

/* DigitalKey Downloading... */
"DigitalKey.Downloading" = "Descargando...";

/* Digital Key Loading... */
"DigitalKey.Loading" = "Cargando...";

/* DigitalKey DigitalKeyManage */
"DigitalKey.Manage" = "Administrar";

/* DigitalKey ManageAndShare */
"DigitalKey.ManageAndShare" = "Gestionar y compartir";

/* DigitalKey NotLongNow */
"DigitalKey.NotLongNow" = "Not long now...";

/* DigitalKey SharedWith */
"DigitalKey.SharedWith" = "Compartido con %@";

/* DigitalKey Syncing */
"DigitalKey.Syncing" = "Sincronizando...";

/* DigitalKey TryAgainLater */
"DigitalKey.TryAgainLater" = "Vuelva a intentarlo más tarde";

/* parts and accessories title */
"Shop.parts.and.accessories" = "Parts & Accessories";

/* Shop SXM trial end */
"Shop.sxm.details.expired.title" = "Expired";

/* Shop SXM RadioShop SXM Radio */
"Shop.sxm.details.radio.title" = "Radio";

/* Shop SXM Status */
"Shop.sxm.details.status.title" = "Status";

/* Shop SXM trial end */
"Shop.sxm.details.trial.end.title" = "Trial End";

/* Shop Manage sub */
"Subscription.manage.subscriptions" = "Administrar Suscripciones";

/* Shop Subscriptions title */
"Subscription.subscriptions" = "Suscripciones";

/* no packages found */
"Subscriptions.no.packages.found" = "No tienes paquetes para este vehículo";

/* no subscription text */
"Subscriptions.no.service.found" = "No se han encontrado Servicios";

/* no subscription lexus subtitle text */
"Subscriptions.noSubscriptions.lexus" = "To subscribe, push the SOS button to speak with an agent. Alternatively, you can call Lexus Guest Service for support at 800-290-5506.";

/* no subscription common subtitle text */
"Subscriptions.noSubscriptions.subtitle.text" = "No tienes ninguna suscripción para su %@, ¿quiere añadir una?";

/* no subscription toyota subtitle text */
"Subscriptions.noSubscriptions.toyota" = "To subscribe, push the SOS button to speak with an agent. Alternatively, you can call Toyota Customer Service for support at 800-290-4431.";

/* Announcement subtitle */
"account.announcements.subtitle" = "Manténgase informado sobre anuncios importantes";

/* Announcements */
"account.announcements.title" = "Anuncios";

/* Account */
"account.button.title" = "Cuenta";

/* Dark Mode */
"account.darkMode.title" = "Modo Oscuro";

/* Account Inbox */
"account.inbox.title" = "Bandeja de entrada";

/* Notifications subtitle */
"account.notifications.subtitle" = "Ver notificaciones para su vehículo y cuenta";

/* Notifications */
"account.notifications.title" = "Notificaciones";

/* Take a Tour subtitle */
"account.takeATour.subtitle" = "Explore la aplicación para ver las novedades";

/* Take a Tour */
"account.takeATour.title" = "Hacer Recorrido";

/* enroll now */
"announcement.enrollNow" = "Enroll Now";

/* lexus reserve */
"announcement.evSwap" = "Lexus Reserve";

/* learn more */
"announcement.learnMore" = "Más información";

/* Manage sub */
"announcement.manage.subscription" = "Administrar suscripción";

/* marketing consent button */
"announcement.marketing.consent.button" = "Más información";

/* of text */
"announcement.of.text" = "de";

/* planning long trip */
"announcement.planning.longer.trip" = "Planning a longer trip?";

/* Announcement popup title */
"announcement.popup.title" = "Anuncios";

/* reserve days */
"announcement.reserveDays" = "Lexus Reserve days added to your account";

/* subscription sutbtitle */
"announcement.subscription.subtitle" = "Su servicio vence en %@ días el %@";

/* lexus reserve take advanatage */
"announcement.take.advantage" = "Take advantage of the Lexus Reserve Days on your account";

/* no active announcements */
"announcementcenter.noActiveAnnouncements" = "There are no active announcements for this vehicle.";

/* City Text */
"common.city" = "Ciudad";

/* State Text */
"common.state" = "Estado";

/* Zip Code Text */
"common.zip" = "Código postal";

/* Country Text*/
"common.country" = "País";

/* Date & Time text */
"calendar.dateAndTime" = "Fecha & Tiempo";

/* Sunday */
"calendar.sunday" = "Domingo";

/* Monday */
"calendar.monday" = "Lunes";

/* Tuesday */
"calendar.tuesday" = "Martes";

/* Wednesday */
"calendar.wednesday" = "Miércoles";

/* Thursday */
"calendar.thursday" = "Jueves";

/* Friday */
"calendar.friday" = "Viernes";

/* Saturday  */
"calendar.saturday" = "Sábado";

/* Sunday Short */
"calendar.sundayShort" = "Dom";

/* Monday Short */
"calendar.mondayShort" = "Lun";

/* Tuesday Short */
"calendar.tuesdayShort" = "Mar";

/* Wednesday Short */
"calendar.wednesdayShort" = "Mié";

/* Thursday Short */
"calendar.thursdayShort" = "Jue";

/* Friday Short */
"calendar.fridayShort" = "Vie";

/* Saturday Short */
"calendar.saturdayShort" = "Sáb";

/* January */
"calendar.january" = "Enero";

/* February */
"calendar.february" = "Febrero";

/* March */
"calendar.march" = "Marzo";

/* April */
"calendar.april" = "Abril";

/* May */
"calendar.may" = "Mayo";

/* June */
"calendar.june" = "Junio";

/* July */
"calendar.july" = "Julio";

/* August */
"calendar.august" = "Agosto";

/* September */
"calendar.september" = "Septiembre";

/* October */
"calendar.october" = "Octubre";

/* November */
"calendar.november" = "Noviembre";

/* December */
"calendar.december" = "Diciembre";

/* January Short */
"calendar.januaryShort" = "Ene";

/* February Short */
"calendar.februaryShort" = "Feb";

/* March Short */
"calendar.marchShort" = "Mar";

/* April Short */
"calendar.aprilShort" = "Abr";

/* May Short */
"calendar.mayShort" = "May";

/* June Short */
"calendar.juneShort" = "Jun";

/* July Short */
"calendar.julyShort" = "Jul";

/* August Short */
"calendar.augustShort" = "Ago";

/* September Short */
"calendar.septemberShort" = "Set";

/* October Short */
"calendar.octoberShort" = "Oct";

/* November Short */
"calendar.novemberShort" = "Nov";

/* December Short */
"calendar.decemberShort" = "Dic";

/* Clean Assist */
"clean.assist" = "Asistencia de limpieza";

/* clean assist lexus subtitle */
"clean.assist.lexus.subtitle" = "Take control with the Clean Assist program allows you to charge your vehicle with 100% renewable electricity credits. The program helps minimize the overall environmental impact of your vehicle resulting from your charging activities through Renewable Energy Certificates (RECs). The best part? After you enroll, there is no additional work on your end";

/* clean assist subtitle */
"clean.assist.subtitle" = "Carga tu vehículo con energía 100% renovable.";

/* renewable Energy Credit CA description */
"ev.renewableEnergyCredit" = "Carga tu vehículo con energía 100% renovable.";

/* clean assist title2 */
"clean.assist.title2" = "Clean Assist Details";

/* clean assist toyota subtitle */
"clean.assist.toyota.subtitle" = "This Clean Assist program allows Toyota to match the amount of electricity you use to charge your vehicle with 100% renewable electricity credits. Not only will this program help minimize the overall environmental impact of your vehicle resulting from your charging activities, but it also gives Toyota an opportunity to further promote electric vehicles with the use of these credits. The best part&#63; After you enroll, there is no additional work on your end";

/* Climate Title */
"climate.title" = "Clima";

/* Climate Heated Seat Off */
"climate.off" = "apagado";

/* Climate Heated Seat Cool */
"climate.cool" = "frio";

/* Climate Heated Seat Heat */
"climate.heat" = "calor";

/* Climate Airflow Heading */
"climate.airFlow" = "Flujo de aire";

/* Climate Fanspeed Heading */
"climate.fanSpeed" = "Velocidad del ventilador";

/* Climate Steering Wheel Heading */
"climate.steeringWheel" = "Volante";

/* Climate Heating Heading */
"climate.heating" = "Calefacción";

/* Climate Defrost Heading */
"climate.defrost" = "Desempañador";

/* Climate Defrost Front */
"climate.front" = "Delantero";

/* Climate Defrost Back */
"climate.back" = "Trasero";

/* Climate Air Circulation Heading */
"climate.airCirculation" = "Circulación de aire";

/* Climate Air Circulation Inside */
"climate.inside" = "Dentro";

/* Climate Air Circulation Outside */
"climate.outside" = "Fuera de";

/* Custom Climate Settings */
"climate.customTitle" = "Configuraciones climáticas personalizadas";

/* Custom Climate Subtitle */
"climate.customSubTitle" = "Cuando estas configuraciones están habilitadas, los cambios surtirán efecto la próxima vez que arranque su automóvil de forma remota.";

/* Save Settings Button */
"climate.saveSettings" = "Guardar ajustes";

/* Toast success message for settings */
"climate.settingsSuccess" = "Tu configuración climática se ha guardado";

/* Toast failure message for settings */
"climate.settingsFailure" = "Error: no se pueden realizar cambios.";

/* Climate Tab Settings Title */
"climate.settingsTitle" = "Configuración";

/* Climate Tab Settings Title */
"climate.scheduleTitle" = "Calendario";

/* Start Climate Button */
"climate.startClimate" = "Iniciar el AC";

/* Stop Climate Button */
"climate.stopClimate" = "Apagar aire acondicionado";

/* Caution Heading */
"climate.caution" = "Advertencia";

/* Start Alert Sub Title */
"climate.startAlertSubTitle" = "・ Verifique los alrededores del vehículo y asegúrese de que esté en un área segura antes utilizar las funciones remotas del aire acondicionado.\n・ No inicie el aire acondicionado de forma remota si hay ocupantes o animales dentro del vehículo. Incluso cuando el sistema está en uso, las temperaturas internas aún pueden alcanzar un nivel alto o bajo debido a funciones como el apagado automático. Los ocupantes y las mascotas que se dejen dentro del vehículo pueden sufrir un ataque al corazón, deshidratación o hipotermia, lo que podría provocar lesiones graves o la muerte.\n・ Según el entorno circundante, como las condiciones del vehículo y la temperatura exterior, es posible que la activación remota del clima no funcione o tarde mucho tiempo en funcionar.\n・ Consulte el manual del propietario para obtener más información.\n・ No muestre otra vez.";

/* Alert Confirmation */
"climate.dontAskMeAgain" = "No pregunte otra vez";

/* Cancel Button */
"climate.cancel" = "Cancelar";

/* Apply Button */
"climate.apply" = "Solicitar";

/* PreSeventeen Alert Sub Title */
"climate.preSeventeenSubTitle" = "In order for remote start function to activate please ensure HVAC is set to “LO” inside vehicle";

/* Last In-Car Setting */
"climate.lastInCarSetting" = "Última configuración en el automóvil";

/* Data not available text */
"climate.dataNotAvailable" = "Los datos del vehículo no están disponibles. Si este vehículo se agregó recientemente, vuelva a intentarlo más tarde.";

/* Check Vehicle Toast */
"climate.checkVehicle" = "No se ha podido iniciar el sistema de climatización. Compruebe el estado de comunicación del vehículo.";

/* Server Error Toast */
"climate.serverError" = "Ocurrió un error del servidor.";

/* Time Out Toast */
"climate.timedOut" = "Tiempo de espera agotado.";

/* Stop Alert Sub Title */
"climate.stopAlertSubTitle" = "¿Está seguro de que quiere apagar el sistema de aire acondicionado?";

/* Turn Off Button */
"climate.turnOff" = "Apagar";

/* Keep On Button */
"climate.keepOn" = "Continuar";

/* Ends In */
"climate.endsIn" = "Finaliza en";

/* Empty Title */
"climate.scheduleEmptyTitle" = "Toque el botón '+' a continuación para establecer el día y la hora de inicio en que desea que su vehículo comience a calentar o enfriar";

/* Schedule Text */
"climate.schedule" = "Calendario";

/* Climate Schedule Text */
"climate.climateSchedule" = "Programación climática";

/* Climate Schedule Start Time */
"climate.scheduleTime" = "¿Cuándo quiere que comience la configuración del clima?";

/* CSchedule Start Time */
"climate.startTime" = "Hora de Comienzo";

/* Schedule Start Date */
"climate.date" = "Fecha";

/* Schedule Days */
"climate.scheduleDay" = "Día de la semana";

/* Select Hint */
"climate.select" = "Seleccione";

/* Toast - Past Time */
"climate.pastTimeToast" = "El clima remoto no se puede habilitar para una fecha pasada.";

/* Remove Schedule - Title */
"climate.removeScheduleTitle" = "Eliminar horario";

/* Remove Schedule - Sub Title */
"climate.removeScheduleSubTitle" = "¿Está seguro de que quiere eliminar este itinerario para el aire acondicionado?";

/* Remove - Button */
"climate.remove" = "Eliminar";

/* Button OK */
"common.Ok" = "OK";

/* Common AcceptCommon Accept */
"common.accept" = "Accept";

/* Navigation button */
"common.add" = "Add";

/* At string */
"common.at" = "a las";

/* Button Continue */
"common.continue" = "Continuar";

/* Button Retry */
"common.retry" = "Rever";

/* Common DeclineCommon Decline */
"common.decline" = "Disminución";

/* Common Agree */
"common.agree" = "Aceptar";

/* Navigation Button */
"common.edit" = "Edit";

/* Button Exit */
"common.exit" = "Exit";

/* Feature is not supported for this vehicle. */
"common.featureNotSupported" = "La funcionalidad seleccionada no es\n compatible con este vehículo.";

/* Last updated string */
"common.lastVehicleStatusUpdate" = "Ultima actualizacion: ";

/* No Image found */
"common.noImageFound" = "Imagen no encontrada";

/* Today string */
"common.today" = "Hoy";

/* Yesterday string */
"common.yesterday" = "Ayer";

/* Back String */
"common.back" = "Retroceder";

/* Go Back string */
"common.goBack" = "Regrese";
/* On string*/
"common.on" = "Encendido";

/* Off string*/
"common.off" = "Apagado";

/* Error string */
"common.error" = "Error";

/* Total String */
"common.total" = "Total";

/* Current Location text */
"common.currentLocation" = "Ubicación Actual";

/* Not Now text */
"common.notNow" = "Ahora no";
/* Sunday */
"common.sunday" = "Domingo";

/* Monday */
"common.monday" = "Lunes";

/* Tuesday */
"common.tuesday" = "Martes";

/* Wednesday */
"common.wednesday" = "Miércoles";

/* Thursday */
"common.thursday" = "Jueves";

/* Friday */
"common.friday" = "Viernes";

/* Saturday */
"common.saturday" = "Sábado";

/* Sunday Short Two Word */
"common.sundayShortTwo" = "Do";

/* Monday Short Two Word */
"common.mondayShortTwo" = "Lu";

/* Tuesday Short Two Word */
"common.tuesdayShortTwo" = "Ma";

/* Wednesday Short Two Word */
"common.wednesdayShortTwo" = "Mi";

/* Thursday Short Two Word */
"common.thursdayShortTwo" = "Ju";

/* Friday Short Two Word */
"common.fridayShortTwo" = "Vi";

/* Saturday Short Two Word */
"common.saturdayShortTwo" = "Sa";

/* Add a Vehicle buttonAdd a Vehicle button */
"dashboard.addavehicle" = "Añadir un vehículo";

/* Find your favorite vehicle to rent LabelFind your favorite vehicle to rent Label */
"dashboard.findfavouritevehicletorent" = "Encontrar su vehículo favorito para alquilar";

/* Make a Reservation buttonMake a Reservation button */
"dashboard.makeareservation" = "Hacer una reservación";

/* Rent a Toyota LabelRent a Toyota Label */
"dashboard.rentaToyota" = "Alquilar un Toyota";

/* Rentals headlineRentals headline */
"dashboard.rentals" = "Alquileres";

/* See Upcoming labelSee Upcoming label */
"dashboard.seeupcoming" = "See upcoming";

/* Driving limits Title */
"gd.drivingLimits" = "Límites de Manejo del Vehículo";

/* Save Driving limits */
"gd.save" = "Guardar";

/* Stay connected LabelStay connected Label */
"dashboard.stayconnected" = "Mantente conectado";

/* Upcoming Reservation labelUpcoming Reservation label */
"dashboard.upcomingreservation" = "Upcoming Reservation";

/* wherever you go Labelwherever you go Label */
"dashboard.whereveryougo" = "donde quiera que vayas";

/* Driving Limit - Miles Screen Title */
"dashboard.miles" = "Millas";

/* Miles Screen - Reset Time Title */
"dashboard.resetTime" = "Restablecer el tiempo";

/* Miles Screen - Reset Time Description */
"dashboard.resetMaxMilesDescription" = "Esto también restablecerá el millaje máximo";

/* Miles Screen - Max Miles Title */
"dashboard.maxMiles" = "Millas máximas";

/* Miles Screen - Distance Unit */
"dashboard.distanceUnit" = "mi";

/* Miles Screen - Mileage */
"dashboard.mileage" = "Millaje";

/* Driving Limit - Time Title */
"dashboard.time" = "Tiempo";

/* Time Screen - Reset Time Description */
"dashboard.resetMaxTimeDescription" = "Esto también restablecerá el tiempo máximo";

/* Time Screen - Max Time */
"dashboard.maxTime" = "Tiempo máximo";

/* Time Screen -  Time Unit */
"dashboard.timeUnit" = "Horas";

/* Curfew Screen -  Start Time Title */
"gd.startTimeUnit" = "Hora de Inicio";

/* Curfew Screen -  End Time Title */
"gd.endTimeUnit" = "Hora de finalización";

/* Curfew Screen -  Days of Week Title */
"gd.daysUnit" = "Días de la semana";

/* Area Screen -  Guest Driver Search Bar Placeholder Title */
"gd.areaSearchPlaceholderText" = "Buscar nombre, ciudad o código postal";

/* Dealers Label */
"find.dealers" = "Concesionarios";

/* Destinations Label */
"find.destinations" = "Destinos";

/* Drive Pulse & Trips Label */
"find.drivePulseandTrips" = "Drive Pulse y viajes";

/* Drive pulse and recent trips Label */
"find.drivePulseandrecentTrips" = "Drive Pulse y viajes recientes";

/* Drive pulse and Trips Driver Score Header Label */
"drivePulse.driverScoreHeaderText" = "Puntuación del conductor";

/* Drive pulse and Trips Opt Out Header Label */
"drivePulse.optOutHeaderText" = "Optar por no tener";

/* Drive pulse and Trips Header Label */
"drivePulse.tripsHeaderText" = "Detalles del viaje";

/* Drive pulse and Trips Details Header Label */
"drivePulse.tripsDetailsHeaderText" = "Trip Details";

/* Drive pulse and Trips Opt Out Body Label */
"drivePulse.optOutBodyText" = "Quiero darme de baja de Drive Pulse, incluido el seguimiento de mis viajes recientes y los datos de comportamiento de conducción.\n\nNo quiero que %0 use mi ubicación (latitud y longitud en un momento determinado) ni los datos de conducción (incluida la aceleración, la velocidad, el frenado y la dirección del vehículo) para calcular mi puntuación de Drive Pulse ni realizar un seguimiento de los viajes recientes.\n\nLa puntuación de Drive Pulse y los viajes recientes ya no se rastrearán ni calcularán para el VIN seleccionado.\n\nEntiendo que la exclusión voluntaria de Drive Pulse no impide que mi vehículo envíe datos de ubicación y manejo a {brand} ni impide que yo deje de recibir otros servicios conectados. No compartimos estos datos con terceros sin su consentimiento.";

/* Drive pulse and Trips Opt In Body Label */
"drivePulse.optInBodyText" = "Quiero que %0 use mi ubicación (latitud y longitud en un momento determinado) y los datos de conducción (que incluyen la aceleración, la velocidad, el frenado y la dirección del vehículo) para calcular mi puntaje de Drive Pulse y realizar un seguimiento de los viajes recientes.\n\nLa puntuación de Drive Pulse y los viajes recientes se calcularán y rastrearán para el VIN seleccionado.\n\nLa puntuación de Drive Pulse, el comportamiento de conducción y la información del viaje son solo para su información y no se comparten con terceros para sus propios fines sin su consentimiento expreso.";

/* Drive pulse and Trips Opt In No Trips Available Label */
"drivePulse.noTripsAvailableText" = "No hay viajes disponibles";

/* Drive pulse and Trips Opt Out Confirm Button */
"drivePulse.textConfirm" = "Confirmar";

/* Drive pulse and Trips Opt In Button */
"drivePulse.optInText" = "Optar ";

/* Drive pulse and Trips Clear Trips Button Label */
"drivePulse.clearTripsText" = "Borrar Viajes";

/* Drive pulse and Trips Cancel Button Label */
"drivePulse.cancelText" = "Cancelar";

/* Drive pulse and Trips Drive Pulse & Trips Header Text Label */
"drivePulse.drivePulseTipsHeaderText" = "Drive Pulse y viajes";

/* Drive pulse and Trips Drive Pulse & Trips Body Text Label */
"drivePulse.drivePulseTipsBodyText" = "Driver Pulse utiliza los datos del sensor del vehículo conectado para calcular la puntuación del conductor de Drive Pulse para cada viaje. Estos datos del sensor incluyen la lectura del odómetro, la aceleración y la velocidad. El rendimiento de manejo de cada viaje se asigna a una puntuación en una escala de 0 a 100. Las métricas clave para el cálculo de la puntuación de un viaje son el comportamiento de la aceleración, del frenado y en las curvas. \nJunto con la puntuación del conductor de Drive Pulse de un viaje en particular, se calcula una puntuación global agregada del conductor basada en todos los viajes anteriores, de los últimos 30 días, para proporcionarle una mejor representación de su comportamiento de manejo.";

/* Drive pulse and Trips Drive Pulse & Trips Behavior Header Text Label */
"drivePulse.drivingBehaviorHeaderText" = "%0 comparte estos datos de forma de manejo?";

/* Drive pulse and Trips Drive Pulse & Trips Behavior Body Text Label */
"drivePulse.drivingBehaviorBodyText" = "%0 no comparte esta información con terceros para sus propios fines sin su consentimiento expreso.";

/* Drive pulse and Trips Drive Pulse & Trips Opt Out Header Text Label */
"drivePulse.drivePulseOptOutHeaderText" = "¿Puedo optar por no participar en Drive Pulse?";

/* Drive pulse and Trips Drive Pulse & Trips Opt Out Body Text Label */
"drivePulse.drivePulseOptOutBodyText" = "Sí. Para no optar por participar de Drive Pulse y viajes recientes, \"Drive Pulse\" en el panel de aplicaciones, y luego haga clic en \"Más Información\" para ver los detalles de Drive Pulse. Seleccione \"No Optar\" en la parte superior derecha para ver la confirmación de exclusión y seleccione \"Confirmar\".";

/* Drive pulse and Trips Label */
"drivePulse.drivePulseText" = "Drive Pulse";

/* Drive pulse and Trips Harsh Cornering Label */
"drivePulse.harshCorneringText" = "Duro\nCurva";

/* Drive pulse and Trips Fast Acceleration Label */
"drivePulse.fastAccelerationText" = "Rápido\nAceleraciones";

/* Drive pulse and Trips Harsh Braking Label */
"drivePulse.harshBrakingText" = "Duro\nFrenado";

/* Drive pulse and Trips Clear Text Header Label */
"drivePulse.clearTripsHeaderText" = "Borrar Viajes";

/* Drive pulse and Trips Clear Text Body Label */
"drivePulse.clearTripsBodyText" = "¿Está seguro de que quiere borrar los viajes de su cuenta?";

/* Drive pulse and Trips See FAQs Label */
"drivePulse.seeFAQsText" = "Ver preguntas frecuentes";

/* Drive pulse and Trips No Score Text */
"drivePulse.noScoreText" = "Sin puntuación";

/* Drive pulse and Trips Fair Score Text */
"drivePulse.fairScoreText" = "Promedio";

/* Drive pulse and Trips Good Score Text */
"drivePulse.goodScoreText" = "Buena";

/* Drive pulse and Trips Excellent Score Text */
"drivePulse.excellentScoreText" = "Excelente";

/* Drive pulse and Trips Remove Trips Success Text */
"drivePulse.removeTripsSuccessText" = "Los viajes se eliminaron correctamente";

/* Drive pulse and Trips Trip A Text */
"drivePulse.tripAText" = "Viaje A";

/* Drive pulse and Trips Trip B Text */
"drivePulse.tripBText" = "Viaje B";

/* Drive pulse and Trips Minutes Text */
"drivePulse.minutesText" = "minutos";

/* Lexus Reserve Label */
"find.evSwap" = "Lexus Reserve";

/* Favorites, recent, and sent to car Label */
"find.favoritesrecent" = "Favoritos, recientes y enviados al automóvil";

/* find dealer Label */
"find.findDealer" = "Encuentre un concesionario";

/* Find your favorite vehicle to rent Label */
"find.findfavouritevehicletorent" = "Encontrar su vehículo favorito para alquilar";

/* km in Label */
"find.km" = "km";

/* Last Parked Location Label */
"find.lastParkedLocation" = "Estacionado por última vez";

/* Lexus Reserve Label */
"find.lexusReserve" = "Lexus Reserve";

/* Recharge in Label */
"find.rechargein" = "Encuentra estaciones cercanas para recargar";

/* Rent a toyota Label */
"find.rentaToyota" = "alquilar un Toyota";

/* Rentals Label */
"find.rentals" = "Alquileres";

/* Stations Label */
"find.stations" = "Estaciones";

/* Drive Pulse Sunset Banner Title */
"find.drivePulseBannerTitle" = "Información importante";

/* Drive Pulse Sunset Banner No Longer Text */
"find.drivePulseBannerNoLongerText" = "A partir del 23 de abril de 2025, Drive Pulse y Viajes ya no estará disponible.";

/* Drive Pulse Sunset Banner Detail Text */
"find.drivePulseBannerDetailText" = "Usted podrá seguir accediendo a Connected Services en la %0 app desde su smartphone o smartwatch compatible.";

/* FTUE Carousel Sub Title */
"ftue.carouselSubTitle" = "Verifique a nuestra nueva imagen diseñada para brindarte una mejor experiencia. Las características pueden variar según el vehículo o la ubicación.";

/* FTUE Carousel Title */
"ftue.carouselTitle" = "Acabamos de recibir una actualización";

/* FTUE Done CTA */
"ftue.doneCta" = "Completado";

/* FTUE Health Count */
"ftue.healthCount" = "4 de 5";

/* FTUE Health SubTitle */
"ftue.healthSubTitle" = "Consulte el mantenimiento de su vehículo, alertas y más.";

/* FTUE Health Title */
"ftue.healthTitle" = "Salud";

/* FTUE Info Count */
"ftue.infoCount" = "5 de 5";

/* FTUE Info SubTitle */
"ftue.infoSubTitle" = "Revise los manuales y garantías de su vehículo, suscripciones y más.";

/* FTUE Info Title */
"ftue.infoTitle" = "Información del vehículo";

/* FTUE Next CTA */
"ftue.nextCta" = "Próximo";

/* FTUE Remote Count */
"ftue.remoteCount" = "2 de 5";

/* FTUE Remote SubTitle */
"ftue.remoteSubTitle" = "Encienda, apague, abra y cierre su vehículo de forma remota. Además, comparta con otros invitados el acceso a su vehículo.";

/* FTUE Remote Title */
"ftue.remoteTitle" = "Remote Connect";

/* Revist SubTitle */
"ftue.revistSubTitle" = "¡Disfrute de la nueva aplicación!";

/* Revist Title */
"ftue.revistTitle" = "Vuelva a consultar el tutorial desde su cuenta, en cualquier momento.";

/* FTUE Carousel See CTA */
"ftue.seeCta" = "Ver qué hay de nuevo";

/* FTUE Carousel Skip CTA */
"ftue.skipCta" = "Omitir por ahora";

/* FTUE Skip Heading */
"ftue.skipTitle" = "Pulse el ícono de Cuenta para explorar la opción Hacer Recorrido.";

/* FTUE Status Count */
"ftue.statusCount" = "3 de 5";

/* FTUE Status SubTitle */
"ftue.statusSubTitle" = "Compruebe al instante el estado de las alertas importantes.";

/* FTUE Status Title */
"ftue.statusTitle" = "Estatus del vehículo";

/* FTUE Switcher Count */
"ftue.switcherCount" = "1 de 5";

/* FTUE Switcher SubTitle */
"ftue.switcherSubTitle" = "Pulse el nombre de su vehículo para seleccionar otro vehículo.";

/* FTUE Switcher Title */
"ftue.switcherTitle" = "Una nueva forma de añadir o intercambiar vehículos";

/* Whats New CTA */
"ftue.whatsNewCta" = "Iniciar el recorrido";

/* Whats New Title */
"ftue.whatsNewTitle" = "Ver qué hay de nuevo";

/* Charge Now buttonCharge Now buttonCharge Now button */
"fuel.chargeNow" = "Cargar Ahora";

/* charging stringcharging stringcharging string */
"fuel.charging" = "Cargando";

/* charging session */
"fuel.chargingSession" = "Su Sesion de Carga";

/* Charging details */
"fuel.chargingDetails" = "Detalles del Cargo";

/* Day description */
"fuel.dayUntilFull" = "día";

/* distance to empty string */
"fuel.distanceToEmpty" = "Distancia restante";

/* Fuel est. */
"fuel.estimation" = "est.";

/* Find Stations buttonFind Stations buttonFind Stations button */
"fuel.findStations" = "Buscar Estaciones";

/* Hrs description */
"fuel.hoursUntilFull" = "horas";

/* Min description */
"fuel.minUntilFull" = "min";

/* percent charged stringpercent charged stringpercent charged string */
"fuel.percentCharged" = "%0% cargado";

/* range string */
"fuel.range" = "Rango";

/* Unplug button */
"fuel.unplug" = "Desconectar";

/* Fuel description */
"fuel.untilFull" = "hasta carga completa";

/* * until fully charged */
"fuel.untillFullyCharged" = "hasta que esté completamente cargado";

/* Guest Driver Heading */
"gd.guestDriver" = "Conductores invitados";

/* Guest Driver Sub Heading */
"gd.guestDriverSubTitle" = "Comparta el monitoreo de límites de velocidad\n y servicios remotos";

/* Used to check profile activated as guestFour */
"gd.guestFour" = "Invitado 4";

/* Remote Share Heading */
"gd.remoteShareSubTitle" = "Comparte el control remoto de tu vehículo";

/* Secondary User Text */
"gd.secondaryUser" = "Eres una conductor secundario para este vehículo.";

/* Used to check profile activated as valet */
"gd.valet" = "Valet";

/* Guest Text */
"gd.guest" = "Invitado";

/* Driving Limits Title */
"gd.drivingLimitsTitle" = "Límites de Manejo del Vehículo";

/* Driving Limits Sub Title */
"gd.drivingLimitsSubTitle" = "Recibirás una notificación cuando tu conductor invitado supere los límites de conducción que estableciste.";

/* Driving Limits Cta */
"gd.drivingLimitCta" = "Definir límites de manejo";

/* Everyday */
"gd.everyday" = "Cada día";

/* Label or Textfield title */
"login.email" = "Email";

/* Manage Profile and preferences */
"manageProfilePreference.title" = "Administrar tu perfil y configuración";

/* Service History Card Description */
"noncv.appointmentsDescription" = "Próximo y último servicio";

/* Service History Card Description */
"noncv.appointmentsHeading" = "Citas";

/* Maintanance Schedule Card Description When Having Values */
"noncv.maintananceScheduleComingUp" = "Viene a las";

/* Maintanance Schedule Card Description */
"noncv.maintananceScheduleDescription" = "No se encontraron horarios";

/* Maintanance Schedule Card Description */
"noncv.maintananceScheduleHeading" = "Programa de mantenimiento";

/* Preferred Dealer Card Description */
"noncv.preferredDealerDescription" = "No se seleccionó ningún concesionario preferido";

/* Preferred Dealer Card Heading */
"noncv.preferredDealerHeading" = "Concesionario Preferido";

/* Service History Card Description */
"noncv.serviceHistoryDescription" = "No se encontró historial de servicio";

/* Service History Card Description */
"noncv.serviceHistoryHeading" = "Sin historial de servicio";

/* Service History Card Description When Having Values */
"noncv.serviceHistoryLastAppointment" = "Última Cita";

/* Odometer N/A text */
"odometer.notApplicable" = "N/A";

/* Odometer text */
"odometer.odometer" = "Odómetro";

/* Add Odometer Text */
"common.odometer" = "Odómetro";

/* Access Acount Card Text */
"pay.accessAccountCardText" = "Click below to access your %0\n Financial Services account.";

/* Access Account CardTitle */
"pay.accessAccountTitle" = "Access Account";

/* Account label */
"pay.account" = "Account";

/* Account closed */
"pay.accountClosed" = "The financial account associated with the selected vehicle is closed.";

/* Account Linking Card Text */
"pay.accountLinkCardText" = "Link your account to make a payment on\n your vehicle from the %0 app.";

/* Account Linking Flow Card Title */
"pay.accountLinkFlowTitle" = "Account Linking Required";

/* Account Locked */
"pay.accountLocked" = "Account Locked";

/* Account not found */
"pay.accountNotFound" = "We are unable to locate this account.";

/* Account Unverified */
"pay.accountUnverified" = "Account Unverified";

/* Make Payment All caught up! */
"pay.allCaughtUp" = "All caught up!";

/* And Text */
"pay.and" = "and";

/* Cancel button */
"pay.cancel" = "Cancel";

/* Continue Button */
"pay.continueBtn" = "Continue";

/* Accept Button */
"pay.iAccept" = "I Accept";

/* Create Account */
"pay.createAccount" = "to create an account.";

/* Disclosure title text */
"pay.disclouse" = "Disclosure";

/* Make Payment Due On */
"pay.dueOn" = "Due On";

/* Electronic Communications and Agreement */
"pay.electronicCommunicationAgreement" = "Consent to Electronic Communications and Agreement";

/* Financial Com */
"pay.financialCom" = " %0financial.com";

/* Including text */
"pay.includingThe" = "including the";

/* Disclosure New User latest Agreement Text */
"pay.newUserLatestDisclosureAgreement" = "By selecting the “I Accept” button, you direct and authorize %0 Motor North America to disclose your Vehicle Identification Number to %0 Financial Services, for the purpose of linking your accounts on this app. Select the “Cancel” button if you do not wish to do so. By selecting the “I Accept” button, you acknowledge that you have read and agreed to the";

/* Disclosure Existing User latest Agreement Text */
"pay.existingUserLatestDisclosureAgreement" = "By selecting “I Accept” below, I acknowledge that I have read and agreed to the";

/* Learn More Button */
"pay.learnMore" = "Learn More";

/* Learn More Card body text */
"pay.learnMoreCompareRateText" = "Apply for credit, compare rates from\n top insurance carriers, or make payments\n for a vehicle you own or lease.";

/* Learn More Flow Card Title */
"pay.learnMoreFlowTitle" = "Flexible Financing Options";

/* Learn More Card body text */
"pay.learnMoreMakePaymentText" = "make payments for a vehicle you own or lease.";

/* Link Account Button */
"pay.linkAccount" = "Link Account";

/* Make Payment Button */
"pay.makePayment" = "Make a Payment";

/* Manage Payments Button */
"pay.managePayments" = "Manage Payments";

/* Online Policies & Agreements */
"pay.onlinePoliciesAgreement" = "Online Policies & Agreements";

/* online registration */
"pay.onlineRegistration" = "online registration";

/* online self service */
"pay.onlineSelfService" = "online self-service";

/* Make Payment Past Due */
"pay.pastDue" = "Past Due";

/* Please visit */
"pay.pleaseVisit" = "Please visit";

/* Privacy Policy Text */
"pay.privacyPolicy" = "Privacy Policy";

/* Verify your Account */
"pay.registeredUser" = "If you're a %0 Financial Services Registered user, please visit";

/* Make Payment Scheduled */
"pay.scheduled" = "Scheduled";

/* Make Payment Scheduled for Text */
"pay.scheduledFor" = "Scheduled for";

/* Server Error */
"pay.serverError" = "It looks like there was an error on our end.";

/* tap To Refresh */
"pay.tapToRefresh" = "Tap to refresh";

/* TFS card title */
"pay.tfsCardTitle" = "%0 Financial Services";

/* tryAgain */
"pay.tryAgain" = "Please try again soon.";

/* Unlock Account Content */
"pay.unlockAccountContent" = "to unlock your account. If you're not a %0 Financial Services Registered user, please visit";

/* Verify your Account */
"pay.verifyAccount" = "to complete registration and verify your account";

/* Wallet card title */
"pay.wallet" = "Cartera";

/* Wallet card Set up Text */
"pay.walletSetup" = "Configurar";

/* Wallet Card Default Title */
"pay.walletCardDefaultTitle" = "Tarjeta predeterminada";

/* Wallet Card Four Digits */
"pay.walletCardFourDigits" = "•••• ";

/* Wallet Manage Payment for Vehicle Subscriptions */
"pay.walletManagePayment" = "Administrar pago\n métodos para su vehículo\n  suscripciones.";

/* TFS card Tfs Consent Text */
"pay.tfsConsentText" = "There are new TFS terms and conditions that need to be consented to before you can access the TFS feature.";

/* Action Button on submittingAction Button on submitting */
"register.register" = "Register";

/* Remote Button Text - Info */
"remote.Info" = "Info";

/* Text for heading */
"remote.advancedRemote" = "Advanced Remote";

/* AutoFix Popup Destructive Button */
"remote.autoFixDestructive" = "Cancel";

/* AutoFix Popup Message */
"remote.autoFixMessage" = "We were unable to start the engine because all of the vehicle’s doors are not locked. Would you like to lock your doors and resume your engine start request?";

/* AutoFix Popup Primary Button */
"remote.autoFixPrimary" = "Try Again";

/* AutoFix Popup Title */
"remote.autoFixTitle" = "Unable To Process Engine Start Command";

/* Remote Button Text - Buzzer */
"remote.buzzer" = "Zumbador";

/* Buzzer on */
"remote.buzzerOn" = "Alarma activada";

/* Remote Button Text - Climate */
"remote.climate" = "Clima";

/* Remote Button Text While Connecting */
"remote.connecting" = "Conectando";

/* Feature not supported */
"remote.featureNotSupported" = "Función no compatible";

/* Guest Driver Heading */
"remote.guestDriver" = "Conductores invitados";

/* Guest Driver Sub Heading */
"remote.guestDriverSubTitle" = "Comparta el monitoreo de límites de velocidad y servicios remotos";

/* Remote Button Text - Hazard */
"remote.hazards" = "Luces de Emergencia";

/* Hazards on */
"remote.hazardsOn" = "Luces de emergencia encendidas";

/* Remote Button Text - Horn */
"remote.horn" = "Bocina";

/* Horn on */
"remote.hornOn" = "Bocina encendida";

/* Remote Button Text - Info */
"remote.info" = "Info";

/* Remote Button Text - Lights */
"remote.lights" = "Luz";

/* Lights on */
"remote.lightsOn" = "Luces encendidas.";

/* Remote Button Text - Lock */
"remote.lock" = "Bloquear";

/* Remote Button Text - Lock Trunk */
"remote.lockTrunk" = "Bloquear";

/* Remote Button Text While Contacting - Lock */
"remote.locking" = "Cerrando";

/* Remote Button Text - Park */
"remote.park" = "Estacionar";

/* Toast for short press */
"remote.pressAndHold" = "Presione y Mantenga";

/* Remote Button Text - Remote */
"remote.remote" = "Remoto";

/* Remote Share Heading */
"remote.remoteShareSubTitle" = "Share your vehicle's remote";

/* Remote Command failure message */
"remote.requestFailed" = "Parece que algo salió mal. Por favor intente nuevamente en unos minutos";

/* Remote Button Text While Sending */
"remote.sending" = "Enviando";

/* Remote Button Text - Start */
"remote.start" = "Encender";

/* Toast for start command already execuiting */
"remote.startInProgress" = "El encendido remoto ya está en progreso";

/* Starting */
"remote.starting" = "Encendiendo";

/* Remote Button Text - Stop */
"remote.stop" = "Apagar";

/* Stopping */
"remote.stopping" = "Deteniendo";

/* Tailgate Locking */
"remote.tailgateLocking" = "Abriendo puerta de la caja";

/* Tailgate Unlocking */
"remote.tailgateUnlocking" = "Cerrando puerta de la caja";

/* Trunk Locking */
"remote.trunkLocking" = "Cerrando Baul";

/* Trunk Unlocking */
"remote.trunkUnlocking" = "Abriendo baul";

/* Vehicle is unable to perform this command */
"remote.unableToPerformCommand" = "El vehículo no puede ejecutar este comando";

/* Remote Button Text - Unlock */
"remote.unlock" = "Desbloquear";

/* Remote Button Text - Unlock Trunk */
"remote.unlockTrunk" = "Desbloquear";

/* Remote Button Text While Contacting - UnLock */
"remote.unlocking" = "Abriendo";

/* Activate - Remote State Button TextActivate - Remote State Button Text */
"remotestate.activate" = "Activar";

/* Activate Remote - Remote State BodyActivate Remote - Remote State Body */
"remotestate.activateRemoteBody" = "Para usar funciones remotas como encender, apagar, cerrar y abrir, necesitamos verificarlo como propietario del vehículo.";

/* Body text for activate remote ng86 */
"remotestate.activateRemoteBodyNG86" = "Para activar la funcionalidad remota, necesitamos validar que es el propietario del vehículo. Para autorizar su servicio de Remote Connect, presione el botón SOS en su vehículo.";

/* Activate Remote - Remote State TitleActivate Remote - Remote State Title */
"remotestate.activateRemoteTitle" = "Activar Remotamente";

/* Activation Error - Remote State BodyActivation Error - Remote State Body */
"remotestate.activationErrorBody" = "No se pudo procesar su solicitud de activación. Comuníquese con nosotros para obtener más ayuda.";

/* Activation Error - Remote State Button TextActivation Error - Remote State Button Text */
"remotestate.activationErrorBt" = "Contactar Asistencia";

/* Activation Error - Remote State TitleActivation Error - Remote State Title */
"remotestate.activationErrorTitle" = "Error de activación remota";

/* Activation Pending - Remote State BodyActivation Pending - Remote State Body */
"remotestate.activationPendingBody" = "Se están activando sus servicios remotos. El proceso de activación puede tardar hasta 24 horas.";

/* Activation Pending - Remote State Button TextActivation Pending - Remote State Button Text */
"remotestate.activationPendingBt" = "Actualizar estado";

/* Activation Pending - Remote State TitleActivation Pending - Remote State Title */
"remotestate.activationPendingTitle" = "Activación Remota Pendiente";

/* Button text for auth required LMEX */
"remotestate.authRequiredLmexBt" = "Ingrese el código de autorización";

/* Notification Disabled - Remote State BodyNotification Disabled - Remote State Body */
"remotestate.notificationDisabledBody" = "Para encender, apagar, cerrar y abrir su vehículo de forma remota, debe activar las notificaciones automáticas en este dispositivo.";

/* Notification Disabled - Remote State Button TextNotification Disabled - Remote State Button Text */
"remotestate.notificationDisabledBt" = "Encender";

/* Notification Disabled - Remote State TitleNotification Disabled - Remote State Title */
"remotestate.notificationDisabledTitle" = "Notificaciones Deshabilitadas";

/* Stolen vehicle sheet title */
"remotestate.reactivateRemoteHeading" = "¿Reactivar Remote Connect?";

/* Stolen vehicle sheet sub title */
"remotestate.reactivateRemoteSubHeading" = "Al seleccionar \"Confirmar\"; reconoce que su vehículo está en su poder. No podrá continuar con la Activación Remota sin el.";

/* Body text for remote shared */
"remotestate.remoteSharedBody" = "Para recuperar el control de sus servicios remotos y compartirlo con otra persona, elimine el conductor.";

/* Button text for remote shared */
"remotestate.remoteSharedBt" = "Quitar conductor";

/* Title text for remote shared */
"remotestate.remoteSharedTitle" = "Su invitado tiene acceso remoto";

/* Remote Share sheet Button */
"remotestate.remove" = "Eliminar";

/* Remote sheet Share Title */
"remotestate.removeDriver" = "Quitar conductor";

/* Remote Share sheet Sub Title */
"remotestate.removeDriverNotes" = "Elimine el conductor para recuperar el control de sus servicios remotos y poder compartirlos con otra persona.";

/* Renew Subscription - Remote State Button TextRenew Subscription - Remote State Button Text */
"remotestate.renew" = "Renovar la suscripción";

/* Subscription Cancelled - Remote State TitleSubscription Cancelled - Remote State Title */
"remotestate.subCancelledTitle" = "Suscripción Cancelada";

/* Subscription Expired - Remote State Title TextSubscription Expired - Remote State Title Text */
"remotestate.subExpiredTitle" = "Suscripción remota caducada";

/* Subscription Cancelled/Expired - Remote State BodySubscription Cancelled/Expired - Remote State Body */
"remotestate.subscriptionBody" = "Renueve su suscripción ahora para restaurar sus servicios remotos.";

/* Stolen vehicle sheet secondary btn */
"remotestate.textCancel" = "Cancelar";

/* Stolen vehicle sheet primary btn */
"remotestate.textConfirm" = "Confirmar";

/* Unable to Activate - Remote State BodyUnable to Activate - Remote State Body */
"remotestate.unableToActivateBody" = "Hubo un problema al procesar su solicitud. Intente activar los servicios remotos nuevamente.";

/* Unable to Activate - Remote State TitleUnable to Activate - Remote State Title */
"remotestate.unableToActivateTitle" = "No pudo hacerse la activación remota";

/* Body text for stolen vehicle */
"remotestate.vehicleStolenBody" = "Ciertas funciones de Remote Connect no están disponibles ahora. Cuando se recupere su vehículo, haga clic en \"Reactivar\" para restaurar sus servicios y funciones. Para obtener ayuda, contáctenos.";

/* Button text for stolen vehicle */
"remotestate.vehicleStolenBt" = "Contáctenos";

/* Secondary Button text for stolen vehicle */
"remotestate.vehicleStolenSecBt" = "Reactivar";

/* Title text for stolen vehicle */
"remotestate.vehicleStolenTitle" = "Vehículo denunciado como robado";

/* LMEX Auth Sheet Title */
"remotestate.verifyOwnership" = "Verificar propiedad";

/* LMEX Auth Sheet Sub Title */
"remotestate.verifyOwnershipDescription" = "Cuando su vehículo se encuentre en un área con buena cobertura celular, presione el botón SOS y siga las indicaciones para \"Activación remota\".";

/* See FAQs button textSee FAQs button text */
"service.SeeFAQs" = "Ver preguntas frecuentes";

/* Call Roadside button textCall Roadside button text */
"service.callRoadside" = "Llamar a asistencia en la carretera %0";

/* Roadside card content textRoadside card content text */
"service.connectWithRoadside" = "Conéctese con Asistencia en la Carretera de %0 para obtener respuestas a sus preguntas.";

/* SubTitleSubTitle */
"service.getHelpOnTheRoad" = "Obtenga ayuda en el camino";

/* buttonbutton */
"service.makeAnAppointment" = "Haga una cita";

/* TitleTitle */
"service.roadsideAssistance" = "Asistencia en Carretera";

/* TitleTitle */
"service.serviceAppointments" = "Cita de servicio";

/* Shop Announcements Title */
"shop.announcements.description" = "Ver anuncios %@";

/* Shop Announcements Title */
"shop.announcements.title" = "Anuncios de %@";

/* Shop issurance title */
"shop.insurance.offers" = "Insurance Offers";

/* Shop parts title */
"shop.parts.and.accessories" = "Parts and Accessories";

/* Shop parts subtitle */
"shop.shop.genuine.parts" = "Shop Genuine Parts";

/* Shop SXM title */
"shop.siriusXM" = "SiriusXM";

/* Shop SXM Active status */
"shop.siriusXM.active.status" = "Account status active";

/* Shop SXM InActive status */
"shop.siriusXM.inActive.status" = "Account status inactive";

/* Shop issurance subtitle */
"shop.view.insurance.offer" = "View your personalized offers";

/* Sign out */
"signOut.button.title" = "Desconectar";

/* Sign out cancel */
"signout.cancel" = "Cancelar";

/* Sign out message question */
"signout.message.question" = "¿Está seguro de que desea cerrar sesión?";

/* Sign out message */
"signout.message.text" = "Para iniciar sesión, deberá volver a ingresar su nombre de usuario y contraseña.";

/* closeclose */
"status.close" = "Cerca";

/* closedclosed */
"status.closed" = "Cerrado";

/* status constant Driver Side */
"status.constant.Driver.Side" = "Lado del conductor";

/* status constant Passenger Side */
"status.constant.Passenger.Side" = "Lado del Pasajero";

/* status constant Rear Door */
"status.constant.Rear.Door" = "Puerta trasera";

/* status constant Rear Window */
"status.constant.Rear.Window" = "Ventana trasera";

/* status constant door */
"status.constant.door" = "Puerta";

/* status constant hatch */
"status.constant.hatch" = "escotilla";

/* status constant hood */
"status.constant.hood" = "capó";

/* status constant moonroof */
"status.constant.moonroof" = "techo corredizo";

/* status constant open */
"status.constant.open" = "abrir";

/* status constant trunk */
"status.constant.trunk" = "baúl";

/* status constant unlocked */
"status.constant.unlocked" = "desbloquada";

/* status constant window */
"status.constant.window" = "ventana";

/* doordoor */
"status.door" = "Puerta";

/* doorsdoors */
"status.doors" = "Puertas";

/* getting status */
"status.getting.status.text" = "Obtener Estatus";

/* goodgood */
"status.good" = "Buena";

/* hatchhatch */
"status.hatch" = "Escotilla";

/* hoodhood */
"status.hood" = "Capó";

/* LockLock */
"status.lock" = "Bloquear";

/* lockedlocked */
"status.locked" = "Bloqueado";

/* lowlow */
"status.low" = "baja";

/* Moon roofMoon roof */
"status.moonroof" = "techo corredizo";

/* Moon roof openMoon roof open */
"status.moonroof.open" = "abierto + techo corredizo";

/* openopen */
"status.open" = "abierto";

/* request in progress */
"status.request.inProgress" = "Solicitud en Progreso";

/* tailgate */
"status.tailgate" = "Puerta de baul";

/* tailgate text */
"status.tailgate.text" = "Puerta de baul";

/* Tire PressureTire Pressure */
"status.tirePressure" = "Presión de las gomas";

/* Tire Status UpdateTire Status Update */
"status.tireStatusUpdate" = "Estado de las gomas actualizado: ";

/* Vehicle StatusVehicle Status */
"status.title" = "Estatus";

/* trunktrunk */
"status.trunk" = "Baúl";

/* UnitUnit */
"status.unit" = "psi";

/* unlockedunlocked */
"status.unlocked" = "Abierto";

/* vehicle information updatedvehicle information updated */
"status.vehicleInformation.Updated" = "Información del vehículo actualizada:";

/* windowwindow */
"status.window" = "Ventanas";

/* windowswindows */
"status.windows" = "Ventanas";

/* status disclaimer title*/
"status.disclaimer.title" = "Revisar ventanas trasera";

/* status disclaimer subtitle*/
"status.disclaimer.subtitle" = "El estado de la ventana trasera abierta o cerrada manualmente no se muestra en la aplicación.";

/* status disclaimer cta*/
"status.disclaimer.cta" = "Descartar";

/* Active TextActive Text */
"subscription.active" = "Activo";

/* Add Service Button TextAdd Service Button Text */
"subscription.addServices" = "Añadir Servicio";

/* Auto Renew Off TextAuto Renew Off Text */
"subscription.autorenewOff" = "Renovación Automática Desactivada";

/* Auto Renew On TextAuto Renew On Text */
"subscription.autorenewOn" = "Renovación Automática Activada";

/* Disclamier Description TextDisclamier Description Text */
"subscription.content" = "Services are dependent upon connection to a compatible wireless network, provided by a third-party wireless service provider.";

/* Disclamier Description Toyota text */
"subscription.content02" = "Content Toyota";

/* Disclamier Description Lexus TextDisclamier Description Lexus Text */
"subscription.content02Lexus" = "Lexus is not responsible for cellular network discontinuance and will not provide compensation for reduced service availability.";

/* Disclamier Description Toyota TextDisclamier Description Toyota Text */
"subscription.content02Toyota" = "Toyota is not responsible for cellular network discontinuance and will not provide compensation for reduced service availability.";

/* Disclamier Title TextDisclamier Title Text */
"subscription.disclamiertitle" = "Disclaimer";

/* Add Service Button Text */
"subscription.enableAllTrials" = "Habilitar todos los ensayos";

/* Exit Button Text */
"subscription.exit" = "Exit";

/* Paid Services TextPaid Services Text */
"subscription.servicePaidPackage" = "Servicios de pago";

/* Trial Services TextTrial Services Text */
"subscription.serviceTrialPackage" = "Servicios de Prueba";

/* Snippet Title */
"subscription.subscriptionExpiring" = "suscripción a punto de caducar";

/* Subscriptions Title Text */
"subscription.subscriptionTitle" = "Suscripciones";

/* Snippet Title */
"subscription.subscriptionsExpiring" = "suscripciones que caducan";

/* Trial Available TextTrial Available Text */
"subscription.trialAvailable" = "Prueba Disponible";

/* HealthHealth */
"tabWidget.health" = "Salud";

/* Remote TabRemote Tab */
"tabWidget.remote" = "Remoto";

/* StatusStatus */
"tabWidget.status" = "Estatus";

/* Find Tabbar button */
"tabbar.find" = "Encontrar";

/* Key Fob Text */
"tabbar.keyfob" = "Llave";

/* Pay Tabbar button */
"tabbar.pay" = "Pagar";

/* Safety Recalls Text */
"tabbar.safetyrecalls" = "Aviso Importante de Reparación";

/* Tabbar button */
"tabbar.service" = "Servicio";

/* Service Campaigns Text */
"tabbar.servicecampaigns" = "Acciones de Servicio";

/* Shop Tabbar button */
"tabbar.shop" = "Comprar";

/* Vehicle Alerts Text */
"tabbar.vehiclealerts" = "Alertas de vehículos";

/* Vehicle Health Report text */
"tabbar.vehiclehealthreport" = "Informe de estado del vehículo";

/* Cancel button */
"vehicleSwitcher.cancel" = "Cancelar";

/* Copy */
"vehicleSwitcher.copy" = "Copy";

/* Default button */
"vehicleSwitcher.defaultText" = "Predeterminado";

/* Disclaimer Text */
"vehicleSwitcher.disclaimerText" = "La imágen es mostrada para propósitos ilustrativos solamente. Es posible que el vehículo no sea como se muestra.";

/* Make Default button */
"vehicleSwitcher.makeDefault" = "Hágalo Predeterminado";

/* Remove button */
"vehicleSwitcher.remove" = "Eliminar";

/* Remove confirmation */
"vehicleSwitcher.removeConfirmation" = "¿Está seguro de que desea eliminar este vehículo de su cuenta?";

/* Remove Digital Key */
"vehicleSwitcher.removeDigitalKeyMessage" = "Usted y los que tienen llaves compartidos ya no podrán acceder a este vehículo con una Digital Key.";

/* Remove Digital Key */
"vehicleSwitcher.removeDigitalKeyTitle" = "¿Está seguro de que desea eliminar su Digital Key?";

/* Remove Vehicle */
"vehicleSwitcher.removeVehicle" = "Quitar Vehículo";

/* Remove Vehicle Subscriptions */
"vehicleSwitcher.removeVehicleAllSubscriptions" = "Se eliminarán todos los servicios a los que se suscribió y la información de suscripción de este vehículo.";

/* Remove Vehicle Confirmation when active padi subscription */
"vehicleSwitcher.removeVehicleConfirmation" = "Se enviará una confirmación a la dirección de correo electrónica de esta cuenta.";

/* All subscriptions cancel info */
"vehicleSwitcher.removeVehicleSubscriptions" = "Al seleccionar \"Eliminar vehículo\"; se cancelarán todas las suscripciones para el vehículo seleccionado.";

/* Select button */
"vehicleSwitcher.select" = "Seleccione";

/* Vehicle disclaimer */
"vehicleSwitcher.vehicleDisclaimer" = "Es posible que el vehículo no sea como se muestra.";

/* Vehicle Switch title */
"vehicleSwitcher.vehicles" = "Vehículos";

/* VIN */
"vehicleSwitcher.vin" = "VIN";

/* active alert text */
"vehiclehealth.activealert" = "alerta activa";

/* active alerts text */
"vehiclehealth.activealerts" = "alerta activas";

/* active service campaign text */
"vehiclehealth.activeservicecampaign" = "acción de servicio activa";

/* active service campaigns text */
"vehiclehealth.activeservicecampaigns" = "acciónes de servicio activas";

/* Call dealer text */
"vehiclehealth.callDealer" = "Llamar Concesionario";

/* Remedy tile header text */
"vehiclehealth.remedy" = "Recurso";

/* Overview tile header text */
"vehiclehealth.overview" = "Visión general";

/* Description tile header text */
"vehiclehealth.description" = "Descripción";

/* Dealer id tile sub text */
"vehiclehealth.dealerId" = "Identifiant du concessionnaire";

/* Nhtsa id tile sub text */
"vehiclehealth.nhtsaId" = "NHTSA ID";

/* Remedy Status tile sub header text */
"vehiclehealth.remedyStatus" = "Remedy Status: ";

/* Engine oil good text */
"vehiclehealth.engineoilgood" = "Suficiente aceite de motor";

/* Engine oil low text */
"vehiclehealth.engineoillow" = "Aceite de motor bajo";

/* good text */
"vehiclehealth.good" = "Buena";

/* Key fob good text */
"vehiclehealth.keyfobgood" = "El nivel de batería de la llave es bueno.";

/* Key fob low text */
"vehiclehealth.keyfoblow" = "El nivel de batería de la llave es bajo, reemplácela pronto.";

/* low text */
"vehiclehealth.low" = "Baja";

/* No Safety Recalls text */
"vehiclehealth.noSafetyRecalls" = "Sin Aviso Importante de Reparación";

/* No service campaigns text */
"vehiclehealth.noservicecampaigns" = "Sin Acciones de Servicio";

/* safety recall text */
"vehiclehealth.safetyRecall" = "aviso importante de reparación activo";

/* safety recalls text */
"vehiclehealth.safetyRecalls" = "avisos importantes de reparación activos";

/* See report text */
"vehiclehealth.seereport" = "Ver informe";

/* Vehicle alert text */
"vehiclehealth.vehicleAlertGood" = "Sin Alertas de vehículos";

/* App Suite Text */
"vehicleinfo.appsuite" = "App Suite";

/* Ask Siri to help you with your vehicle Text */
"vehicleinfo.asksiri" = "Solicite a Siri que te ayude con tu vehículo";

/* Connected services for your vehicle Text */
"vehicleinfo.connectedservicesforyourvehicle" = "Servicios Conectados para su vehículo";

/* Connected Services Support Text */
"vehicleinfo.connectedservicessupport" = "Apoyo a servicios conectados";

/* Connect to in-vehicle apps Text */
"vehicleinfo.connecttoinvehicleapps" = "Conéctese a las aplicaciones del vehículo";

/* VIN Copied Text */
"vehicleinfo.copied" = "copiado";

/* Customer Action Complete Text */
"vehicleinfo.customeractioncomplete" = "Acción del cliente completada";

/* Dynamic Navi Text */
"vehicleinfo.dynamicnavi" = "Dynamic Navi";

/* Enter a nickname Text */
"vehicleinfo.enteranickname" = "(Añadir un apodo)";

/* Get support Text */
"vehicleinfo.getsupport" = "Obtener apoyo";

/* glovebox Text */
"vehicleinfo.glovebox" = "Guantera";

/* New update available Text */
"vehicleinfo.newupdateavailable" = "New update available";

/* Remove Vehicle Button */
"vehicleinfo.removevehicle" = "Quitar Vehículo";

/* Save Button */
"vehicleinfo.save" = "Guardar";

/* Siri Shortcuts Text */
"vehicleinfo.sirishortcuts" = "Atajos de Siri";

/* Unable to update vehicle software Text */
"vehicleinfo.softwareerrornotification" = "No se puede actualizar el software del vehículo";

/* Software update available Text */
"vehicleinfo.softwareupdateavailable" = "Actualización disponible";

/* Up to date Text */
"vehicleinfo.softwareupdatecomplete" = "Está actualizado";

/* Software update initiated Text */
"vehicleinfo.softwareupdateprogress" = "Actualización de software inicializada";

/* Specs Text */
"vehicleinfo.specs" = "Especificaciones";

/* Capabilities Text */
"vehicleinfo.vehiclecapabilities" = "Capacidades del Vehículo";

/* Specs, manuals Text */
"vehicleinfo.specsmanuals" = "Especificaciones, manuales";

/* Specs, manuals, dashboard lights Text */
"vehicleinfo.specsmanualsdashboardlights" = "Especificaciones, manuales, luces del tablero...";

/* Subscriptions Text */
"vehicleinfo.subscriptions" = "Suscripciones";

/* Update Nickname Text */
"vehicleinfo.updatenickname" = "Actualizar apodo";

/* Update your renewal date Text */
"vehicleinfo.updateyourrenewaldate" = "Update your renewal date";

/* Vehicle Identification Number Text */
"vehicleinfo.vehicleidentificationnumber" = "Identificación del vehículo";

/* Vehicle Software Text */
"vehicleinfo.vehiclesoftware" = "Software para vehículos";

/* Update Initialized Text */
"vehicleinfo.updateInitialized" = "Actualización inicializada";

/* Glovebox header Text */
"glovebox.glovebox" = "Guantera";

/* specsAndCapabilities tile text */
"glovebox.specsAndCapabilities" = "Especificaciones y capacidades";

/* manualsAndWarranties tile text */
"glovebox.manualsAndWarranties" = "Manuales y Garantías";

/* howToVideos tile text */
"glovebox.howToVideos" = "How-To \nVideos";

/* dashboardLights tile text */
"glovebox.dashboardLights" = "Guía de luces del tablero";

/* toyotaForFamilies tile text */
"glovebox.toyotaForFamilies" = "Toyota para familias";

/* proXSeats tile text */
"glovebox.proXSeats" = "Asientos con función IsoDinámica";

/* seeFaq tile text */
"glovebox.seeFaq" = "Ver preguntas frecuentes";

/* noResults text */
"glovebox.noResults" = "No se han encontrado resultados";

/* search text */
"glovebox.search" = "Búsqueda";

/* noManualsAndWarranties text */
"glovebox.noManualsAndWarranties" = "No hay manuales y garantías";

/* noCapabilities text */
"glovebox.noCapabilities" = "Sin capacidades";

/* specifications text */
"glovebox.specifications" = "Especificaciones";

/* capabilities text */
"glovebox.capabilities" = "Capacidades";

/* noSpecifications text */
"glovebox.noSpecifications" = "Sin especificaciones";

/* vehicleInfo text */
"glovebox.vehicleInfo" = "Información del vehículo";

/* noDataFound text */
"glovebox.noDataFound" = "No se encuentran datos";
/* Charge history title */
"ev.history" = "Historial";

/*
 Clean assist description */
"ev.howCAworks" = "¿Cómo funciona Clean Assist?";

/*
 Start */
"ev.start" = "Encender";

/*
 Start Time */
"ev.startTime" = "Hora de Inicio";

/*
 startTime Sub Heading */
"ev.startTimeSubHeading" = "Cuando desee que el vehículo comience a cargarse";

/* end Time Sub Heading text */
"ev.endTimeSubHeading" = "When you want vehicle recharged by";

/*
 same Start End Time */
"ev.sameStartEndTime" = "La hora de inicio y finalización no puede ser la misma";

/*
 wattTime StartTime SubHeading */
"ev.wtStartTimeSubHeading" = "Establezca la hora a la que desea que su vehículo comience a cargarse";

/*
 wattTime EndTime Sub Heading */
"ev.wtEndTimeSubHeading" = "Establezca la hora en que desea que su vehículo sea recargado por";

/*
 statistics Info */
"ev.statisticsInfo" = "Las estadísticas de uso de energía estarán disponibles después de su primer mes completo de conducción.";

/*
 manual Schedule Heading */
"ev.manualScheduleHeading" = "Calendario de carga manual";

/*
 evgo Free One Year Text */
"ev.evgoFreeOneYearText" = "un año de cortesía!";

/* evgo Free One Year */
"ev.evgoFreeOneYear" = "un año de cortesía!";

/* learn more button text */
"ev.learnMore" = "Más información";

/* End Time Text */
"ev.endTime" = "Hora de finalización";

/* Off Peak Hours Text */
"ev.offPeakHours" = "Fuera de horas pico";

/* Off Peak Schedule Text */
"ev.offpeakSchedule" = "Horario fuera de pico";

/* No History Found text */
"ev.noCdrHistoryMessage" = "No se encontró historial";

/* evmc Departure Time Info Text */
"ev.evmcDepartureTimeInfoText" = "Establezca la hora en que desea que su vehículo deje de cargarse";

/* off-peak Description */
"ev.offpeakDescription" = "Recarga de vehículos fuera de las horas pico entre ";

/* and text */
"ev.and" = " y ";

/* find out more text */
"ev.findOutMore" = "Saber más";

/* no Schedules Title */
"ev.noSchedulesTitle" = "Aún no hay horarios de carga";

/* no Schedules Subtitle */
"ev.noSchedulesSubtitle" = "Establezca horarios de carga y controle cuándo carga su vehículo.";

/* no Schedules Subtitle for PHEV */
"ev.phevNoSchedulesSubtitle" = "To use ECO charging or in-app schedule charging, you need to set the schedule in the vehicle head unit first. The schedule will sync with the app after the set up.";

/* On text */
"onText" = "Encendido";

/* Off text */
"offText" = "Apagado";

/* harge ScheduleTile text */
"ev.evmcChargeSchedulePageTitleText" = "Horario de carga";

/* New ScheduleTile text */
"ev.evmcNewSchedulePageTitleText" = "Nuevo horario";

/* Home text */
"ev.home" = "Casa";

/* Charge Info text */
"ev.chargeInfo" = "Información de carga";

 /* EVgo text */
 "ev.evgo" = "EVgo";

 /* chargepoint text */
 "ev.chargePoint" = "Punto de recarga";

/* register text */
"ev.register" = "Registrate";

/* Sign In text */
"ev.signIn" = "Iniciar Sesión";

/* evGo Account Found Text */
"ev.evGoAccountFoundText" = "Si ya tiene una cuenta EVgo con este correo electrónico, continúe iniciando sesión:";

/* Forgot password text */
"ev.forgetPasswordText" = "¿Has olvidado tu contraseña?";

/* clean Energy Graph Title */
"ev.cleanEnergyGraphTitle" = "Carga de energía limpia (kWh)";

/* co2 Emissions Graph Title */
"ev.co2EmissionsGraphTitle" = "Emisiones de CO2 evitadas (lb)";

/* ca Consent Preface Text */
"ev.caConsentPrefaceText" = "¡Los clientes de %0 pueden conducir sin preocupaciones sabiendo que su vehículo está usando energía 100% renovable!\n\n";

/* what Is Clean Assist */
"ev.whatIsCleanAssist" = "¿Qué es Clean Assist?";

/* terms And Privacy */
"ev.termsAndPrivacy" = "Términos y privacidad";

/* Schedule text */
"ev.schedule" = "Programar";

/* Set timer in vehicle */
"ev.setTimerInVehicle" = "Establecer reloj automático en el vehículo";

/* Account Linked text */
"ev.accountLinked" = "Cuenta vinculada";

/* Account Linked Description text */
"ev.accountLinkedText" = "Ya puedes empezar a cobrar en cualquier socio\nestación con la aplicación.";

/* Account Created text */
"ev.accountCreated" = "Cuenta creada";

/* Account Created Description text */
"ev.evgoAccountCreatedText" = "Ahora puede comenzar a cargar en cualquier estación asociada con la aplicación.";

/* Vehicle software title text */
"vehicleSoftware.vehicleSoftware" = "Software para vehículos";

/* Installed detail text */
"vehicleSoftware.installed" = "Instalado";

/* Update failed view */
"vehicleSoftware.updateFailed" = "Actualización de software fallida";

/* Update failed view subtitle */
"vehicleSoftware.updateFailedSubtitle" = "El software se actualizará la próxima vez que arranque su vehículo.";

/* Back to Dashboard button */
"vehicleSoftware.backToDashboard" = "Volver al panel";

/* Up to date view */
"vehicleSoftware.upToDate" = "Su software está actualizado";

/* Up to date view subtitle */
"vehicleSoftware.upToDateSubtitle" = "No hay actualizaciones de software en este momento.";

/* Status expandable text */
"vehicleSoftware.status" = "Estatus";

/* Instructions expandable text */
"vehicleSoftware.instructions" = "Instrucciones";

/* What's New expandable text */
"vehicleSoftware.whatsNew" = "¿Qué hay de nuevo?";

/* Owner's Manual expandable text */
"vehicleSoftware.ownersManual" = "Manual del propietario";

/* Working Time expandable text */
"vehicleSoftware.workingTime" = "Tiempo de trabajo";

/* Previous update expandable text */
"vehicleSoftware.previousUpdate" = "Actualizaciones anteriores";

/* Software Versions expandable text */
"vehicleSoftware.softwareVersions" = "Version de Software";

/* Release Notes text */
"vehicleSoftware.releaseNotes" = "notas de lanzamiento";

/* Update Later button text */
"vehicleSoftware.updateLater" = "Actualiza luego";

/* updateLater description */
"vehicleSoftware.confirmUpdateLater" = "Si selecciona “Actualizar luego”, las características en “Que hay nuevo” no estaran disponibles hasta que se complete esta actualización de software.";

/* Agree button text */
"vehicleSoftware.agree" = "De acuerdo y actualiza";

/* Agree title */
"vehicleSoftware.agreeTitle" = "Actualización de software inicializada";

/* Agree description */
"vehicleSoftware.agreeDescription" = "La actualización de software comenzará después de que reinicie el vehículo. El vehículo puede ser conducido durante el proceso de actualización de software. Recibirá un mensaje cuando la actualización de software haya sido completada.";

/* Important Details expandable text */
"vehicleSoftware.importantDetails" = "Detalles importantes";

/* Caution expandable text */
"vehicleSoftware.caution" = "Precaución";

/* update button expandable text */
"vehicleSoftware.updateHistory" = "Actualizar historial";

/* update available expandable text */
"vehicleSoftware.updateAvailable" = "Actualización disponible";

/* Current Version text */
"vehicleSoftware.currentVersion" = "Versión actual:";

/* New Version text */
"vehicleSoftware.newVersion" = "Nueva versión:";

/* See Update button text */
"vehicleSoftware.seeUpdate" = "Ver actualización";

/* Version concatenated text */
"vehicleSoftware.version" = "Versión";

/* Continue button text */
"vehicleSoftware.continue" = "Continúar";

/* confirmUpdateLater button text */
"vehicleSoftware.updateLaterConfirm" = "¿Está seguro?";

/* sure button text */
"vehicleSoftware.sure" = "Estoy seguro";

/* go back button text */
"vehicleSoftware.goBack" = "Volver Atrás";

/* Go To Dashboard button text */
"vehicleSoftware.goToDashboard" = "Ir al panel de control";

/* Done text*/
"common.done" = "Completado";

/* Charging Interrupted text */
"charging.chargingInterrupted" = "Carga interrumpida";

/* charging Error Confirmation Text */
"charging.chargingErrorConfirmationText" = "Please check connection to the plug.";

/* Report Station text */
"charging.reportStationText" = "Estación de informes";

/* Energy update error message */
"charging.energyUpdateError" = "La información se actualizará automáticamente cuando la recibamos de la estación de carga.";

/* Energy update error title */
"charging.energyUpdateErrorTitle" = "Un momento";

/* charging session stopped title text */
"charging.chargingSessionStoppedTitle" = "Dejaste de cargar antes de que comenzara";

/* charging session stopped message */
"charging.chargingSessionStoppedMessage" = "Puede intentarlo de nuevo presionando Comenzar a cargar en la siguiente pantalla";

/* Time text */
"ev.time" = "Tiempo";
/* Description: Subtitle text for no schedule */
"ev.evmcNoScheduleSubtitleText" = "Cree un horario para cargar su vehículo.";

/* Description: Title text for no schedule */
"ev.evmcNoScheduleTitleText" = "Recarga en la aplicación";

/* Create Schedule button text */
"ev.createSchedule" = "Crear horario";

/* Create Schedule Toast text */
"ev.scheduleCreatedText" = "Su horario ha sido creado.";

/* Deleted Schedule Toast text */
"ev.scheduleDeletedText" = "Su horario ha sido eliminado.";

/* Updated Schedule Toast text */
"ev.scheduleUpdatedText" = "Su horario ha sido actualizado.";

/* guestDriver text */
"gd.guestDrivers" = "Conductores invitados";

/* guestDriver desc */
"gd.desc" = "Comparta los comandos remotos de su vehículo con otros y controle sus límites de velocidad en la aplicación.";

/* guestDriver text */
"gd.guestDrivers" = "Conductores invitados";

/* guestDriver desc */
"gd.desc" = "Comparta los comandos remotos de su vehículo con otros y controle sus límites de velocidad en la aplicación.";

/* guestDriver valet text */
"gd.valet" = "Valet";

/* guestDriver AlertsOn text */
"gd.alertsOn" = "Alertas Encendido";

/* guestDriver AlertsOff text */
"gd.alertsOff" = "Alertas Apagado";

/* guestDriver InviteDriver text */
"gd.inviteDriver" = "Invitar Conductor";

/* Share Remote Label */
"gd.shareRemote" = "Compartir servicios remotos";

/* Add Driver CTA */
"gd.addDriver" = "Añadir conductor";

/* Remote Access Title */
"gd.remoteAccessTitle" = "Acceso remoto";

/* Remote Access SubTitle */
"gd.remoteAccessSubTitle" = "Brinde al conductor acceso a comandos remotos con la aplicación móvil (motor, cerrar, luces de emergencia, ...)";

/* Search by Email or Phone */
"gd.searchHint" = "Buscar por correo electrónico o teléfono";

/* Hint For Toyota */
"gd.hintToyotaApp" = "El conductor necesita tener la aplicación Toyota.";

/* Hint For Lexus */
"gd.hintLexusApp" = "El conductor necesita tener la aplicación Lexus.";

/* Hint For Subaru */
"gd.hintSubaruApp" = "El conductor necesita tener la aplicación Subaru.";

/* Invite Driver CTA */
"gd.inviteDriver" = "Invitar Conductor";

/* No Result Title */
"gd.noResultTitle" = "No hay resultados";

/* No Result SubTitle */
"gd.noResultSubTitle" = "No pudimos encontrar a nadie por ese correo electrónico o número de teléfono. Inténtelo de nuevo.";

/* Invite Sent Title */
"gd.inviteSentTitle" = "Invitación enviada";

/* Invite Sent Toyota SubTitle */
"gd.inviteSentToyotaSubTitle" = "Para comenzar a usar los servivios remotos, su invitado deberá descargar la aplicación Toyota. También puede establecer sus límites de manejo en la siguiente pantalla.";

/* Invite Sent Lexus SubTitle */
"gd.inviteSentLexusSubTitle" = "Para comenzar a usar los servivios remotos, su invitado deberá descargar la aplicación Lexus. También puede establecer sus límites de manejo en la siguiente pantalla.";

/* Invite Sent Subaru SubTitle */
"gd.inviteSentSubaruSubTitle" = "Para comenzar a usar los servivios remotos, su invitado deberá descargar la aplicación Subaru. También puede establecer sus límites de manejo en la siguiente pantalla.";

/* guestDriver speed text */
"gd.speed" = "Velocidad";

/* guestDriver miles text */
"gd.milesText" = "Millas";

/* guestDriver miles unit */
"gd.milesUnit" = "Millas";

/* guest driver mileage text */
"gd.mileageText" = "Millaje";

/* guestDriver area text */
"gd.area" = "Área";

/* guestDriver curfew text */
"gd.curfew" = "Toque de queda";

/* guestDriver time text */
"gd.time" = "Tiempo";

/* guestDriver ignition text */
"gd.ignition" = "Encendido";

/* guestDriver profile updated success */
"gd.profileUpdatedSuccessfully" = "Perfil actualizado con éxito";

/* plugged in text */
"charging.pluggedIn" = "Conectado";
/* Okay text */
"common.okay" = "Okay";

/* Month text */
"common.month" = "Mes";
/* Delete Schedule Text */
"ev.deleteSchedule" = "Eliminar horario";

/* Delete Schedule Confirmation Text */
"ev.deleteScheduleConfirmation" = "¿Está seguro de que quiere eliminar este horario?";

/* Yes, Delete button text */
"ev.yesDeleteButtonText" = "Sí, eliminar";

/* EV schedule days of week validation text */
"ev.evmcDaysOfWeekValidationText" = "Por favor seleccione día(s) de la semana";

/* Monday text */
"common.monday" = "Lunes";
/* Tuesday text */
"common.tuesday" = "Martes";
/* Wednesday text */
"common.wednesday" = "Miércoles";
/* Thursday text */
"common.thursday" = "Jueves";
/* Friday text */
"common.friday" = "Viernes";
/* Saturday text */
"common.saturday" = "Sábado";
/* Sunday text */
"common.sunday" = "Domingo";

/* Enroll text */
"ev.enroll" = "inscribirse";

/* View text */
"ev.view" = "Vista";

/* Charging est text */
"ev.withoutAC" = "con el climatizador apagado";

/* Visa title */
"evWallet.visaTitle" = "Visa";

/* American Express title */
"evWallet.amexTitle" = "American Express";

/* Chase title */
"evWallet.chaseTitle" = "Chase";

/* Discover title */
"evWallet.discoverTitle" = "Discover";

/* JCB title */
"evWallet.jcbTitle" = "JCB";

/* MasterCard title */
"evWallet.mastercardTitle" = "MasterCard";

/* UnionPay title */
"evWallet.unionPayTitle" = "UnionPay";

/* Default Card text */
"evWallet.defaultCardText" = "Tarjeta predeterminada";

/* Exp text */
"evWallet.expText" = "Exp ";

/* Card Set to Default text */
"evWallet.cardAlreadyDefaultTitle" = "Tarjeta configurada por defecto";

/* cardAlreadyDefaultDescription */
"evWallet.cardAlreadyDefaultDescription" = "The %0 card ending in %1 is already set to default";

/* Done button */
"evWallet.doneButton" = "Completado";

/* Default title */
"evWallet.defaultTitle" = "Predeterminado";

/* Make Default Button */
"evWallet.makeDefaultButton" = "Hágalo Predeterminado";

/* setDefaultDescription */
"evWallet.setDefaultDescription" = "The %0 card ending in %1 was set default to your wallet.";

/* Add Card title */
"evWallet.addCardTitle" = "Agregar tarjeta";

/* Add Card agreement */
"evWallet.addCardAgreeement" = "Acepto que la aplicación de %0 almacene la información de mi tarjeta de crédito.";

/* Save button */
"evWallet.saveButton" = "Guardar";

/* Billing Address title */
"evWallet.billingAddressTitle" = "Dirección de Envio";

/* cityTextFieldPlaceholder */
"evWallet.cityTextFieldPlaceholder" = "Ciudad ( Plano )";

/* stateTextFieldPlaceholder */
"evWallet.stateTextFieldPlaceholder" = "Estado ( TX )";

/* zipCodeTextFieldPlaceholder */
"evWallet.zipCodeTextFieldPlaceholder" = "Código postal";

/* countryTextFieldPlaceholder */
"evWallet.countryTextFieldPlaceholder" = "País ( US )";

/* cardInfoTitle */
"evWallet.cardInfoTitle" = "Información de la tarjeta";

/* creditCardNumberTextFieldPlaceholder */
"evWallet.creditCardNumberTextFieldPlaceholder" = "Número de tarjeta de crédito";

/* firstNamePlaceholder */
"evWallet.firstNamePlaceholder" = "Primer nombre";

/* lastNamePlaceholder */
"evWallet.lastNamePlaceholder" = "Apellido";

/* expDatePlaceholder */
"evWallet.expDatePlaceholder" = "Exp ( 09/2030 )";

/* CVV Code placeholder */
"evWallet.cvvCodePlaceholder" = "codigo CVV";

/* Exp Date Dash */
"evWallet.expDateDash" = "/";

/* Charging Stations title */
"evWallet.chargingStationsTitle" = "Charging Stations";

/* Transactions title */
"evWallet.multipleTransactionsTitle" = "Actas";

/* No Transactions Found title */
"evWallet.noTransactionTitle" = "No Transactions Found";

/* Subscriptions title */
"evWallet.subscriptionsTitle" = "Suscripciones";

/* Subscriptions description */
"evWallet.subscriptionsDescription" = "Manage Payments";

/* Wallet title */
"evWallet.walletTitle" = "Cartera";

/* No Wallet Card title */
"evWallet.noWalletCardTitle" = "Set up your\npayment methods\nfor the app";

/* Remove Card title */
"evWallet.removeCardTitle" = "Quitar tarjeta";

/* Remove Card message */
"evWallet.removeCardMessage" = "¿Estás segura de que quieres eliminar esta tarjeta?";

/* Yes, Remove button */
"evWallet.yesRemoveCardButton" = "Sí, quitar";

/* Cancel button */
"evWallet.cancelRemoveCardButton" = "Cancelar";

/* Delete title */
"evWallet.successfulCardRemovalTitle" = "Borrar";

/* Successful card removal message */
"evWallet.successfulCardRemovalMessage" = "The %0 card ending in %1 was deleted from your wallet"; 

/* Unsuccessful card removal title */
"evWallet.unsuccessfulCardRemovalTitle" = "Añadir nuevo método de pago";

/* Unsuccessful card removal message */
"evWallet.unsuccessfulCardRemovalMessage" = "Se requiere mantener al menos una tarjeta en los archivos. Agregue una tarjeta de reemplazo para poder quitar esta.";

/* Transactions title */
"evWallet.transactionsTitle" = "Actas";

/* ChargePoint title */
"evWallet.chargepointTitle" = "Punto de recarga";

/* Ionna title */
"evWallet.ionnaTitle" = "IONNA";

/* Five dots placeholder */
"evWallet.fiveDots" = "•••••";

/* Card added title */
"evWallet.cardAddedTitle" = "Card Added";

/* Card added message */
"evWallet.cardAddedMessage" = "The card ending in %0 was added to your wallet";

/* Card add failed title */
"evWallet.cardAddFailedTitle" = "Algo salió mal";

/* Card add failed message */
"evWallet.cardAddFailedMessage" = "Error getting payment methods";

/* Card Transaction */
"evWallet.transaction" = "Transaction";

/* Card Transaction Product */
"evWallet.transactionProduct" = "Product";

/* Card Transaction Description */
"evWallet.transactionDescription" = "Description";

/* Card Transaction Funding */
"evWallet.transactionFunding" = "Funding";

/* Card Transaction Status */
"evWallet.transactionStatus" = "Status";

/* Card Transaction Total */
"evWallet.transactionTotal" = "Total";

/* Card Transaction Charging */
"evWallet.transactionCharging" = "Charging";

/* dsaPreferredDealer text */
"service.preferredDealer" = "Concesionario preferido";

/* Pick Up only instructions text */
"service.puoInstructions" = "The dealer will contact you regarding pick only details. Locations will need to be with in 15 miles of the delaership.";

/* Confirm button text */
"service.confirmBtn" = "Confirm";

/* Change Transportation Button text */
"service.changeTransportation" = "Change Transportation";

/* dsaNoPreferredDealerSelected text */
"service.dsaNoPreferredDealerSelected" = "No se seleccionó ningún concesionario preferido";

/* dsaVehicleDueForMaintenance text */
"service.dsaVehicleDueForMaintenance" = "Su vehículo tiene el mantenimiento vencido.";

/* dsaVehicleNotDueForMaintenance text */
"service.dsaVehicleDueForMaintenance" = "Su vehículo no necesita mantenimiento.";

/* dsaMakeAppointment text */
"service.dsaMakeAppointment" = "Haga una cita";

/* dsaDueForMaintenance text */
"service.dsaDueForMaintenance" = "Pendiente de mantenimiento";

/* dsaMaintenanceSchedule text */
"service.dsaMaintenanceSchedule" = "Programa de mantenimiento";

/* dsaAppointments text */
"service.dsaAppointments" = "Citas";

/* dsaUpcomingAndPastService text */
"service.dsaUpcomingAndPastService" = "Ver citas próximas y pasadas";

/* dsaChangePreferredDealer text */
"service.changePreferredDealer" = "Cambiar Concesionario Preferido";

/* dsaSetPreferredDealer */
"service.setPreferredDealer" = "Establecer como concesionario preferido";

/* dsaTransportation header */
"service.transportation" = "Transportación";

/* dsaAccessibility header */
"service.accessibility" = "Accesibilidad";

/* dsaPaymentMethods header */
"service.paymentMethods" = "Métodos de pago";

/* dsaAmenities header */
"service.amenities" = "Entretenimiento";

/* dsaServices header */
"service.services" = "Servicios";

/* dsaServiceHours header */
"service.serviceHours" = "Horas de servicio";

/* dsaCall text */
"service.call" = "Llamar";

/* dsaWebsite text */
"service.website" = "Sitio web";

/* dsaDirections text */
"service.directions" = "Direcciones";

/* dsaDealerDetails text */
"service.dealerDetails" = "Detalles del Concesionario";

/* dsaMonday text */
"service.monday" = "Lunes";
/* dsaTuesday text */
"service.tuesday" = "Martes";
/* dsaWednesday text */
"service.wednesday" = "Miércoles";
/* dsaThursday text */
"service.thursday" = "Jueves";
/* dsaFriday text */
"service.friday" = "Viernes";
/* dsaSaturday text */
"service.saturday" = "Sábado";
/* dsaSunday text */
"service.sunday" = "Domingo";
/* dsaClosed text */
"service.closed" = "cerrado";
/* dsaExitAppointment text */
"service.exitAppointment" = "Cita de salida";
/* dsaConfirmationExit text */
"service.confirmationExit" = "¿Está seguro de que quiere salir de esta \n cita?";
/* dsaYesExit text */
"service.yesExit" = "Si, salir";
/* dsaContinueAppointment text */
"dsaContinueAppointment" = "Continuar con cita";
/* dsaUpcoming */
"service.upcoming" = "Próximamente";
/* dsaPast */
"service.past" = "Pasado";
/* dsaNoUpcomingAppointment */
"service.noUpcomingAppointment" = "No tiene citas futuras";
/* dsaNoPastAppointment */
"service.noPastAppointment" = "Sin citas pasadas";
/* dsaNoUpcomingAppointmentDetail */
"service.noUpcomingAppointmentDetail" = "Citas que tú creas\n aparecerá aquí.";
/* dsaNoPastAppointmentDetail */
"service.noPastAppointmentDetail" = "Las citas completadas o canceladas aparecerán aquí.";

/* dsaDone text */
"service.done" = "Hecho";
/* dsaSuccessPreferredDealer text */
"service.successPreferredDealer" = "Ha establecido su concesionario.";
/* dsaSuccess */
"service.success" = "Éxito";
/* dsaSelectDealer */
"service.selectDealer" = "Seleccionar concesionario";
/* dsaDealrSearchPrompt */
"service.dealerSearchPrompt" = "Buscar nombre, ciudad o código postal";
/*dsaDealerFilters*/
"service.filters" = "Filtros";
/*dsaDealerApplyFilters*/
"service.applyFilters" = "Aplicar Filtros";
/*dsaDealerResetFilters*/
"service.reset" = "Restablecerla";
/*dsaSmartPathDealersOnly*/
"service.smartpathOnly" = "Smartpath solo para concesionarios";
/*dsaTenMiles*/
"service.tenMiles" = "10 Miles";
/*dsaTwentyFiveMiles*/
"service.twentyFiveMiles" = "25 Miles";
/*dsaFiftyMiles*/
"service.fiftyMiles" = "50 Miles";
/* dsaAddRecord */
"service.addrecord" = "Añadir registro";
/*dsaServiceAdvisor*/
"service.serviceAdvisor" = "Asesor de servicio";
/*dsaConfirmAppointment*/
"service.confirmAppointment" = "Confirmar cita";
/*dsaLastKnownMileage*/
"service.lastKnownMileage" = "Último millaje conocido";
/*dsaAppointmentConfirmDisclaimer*/
"service.appointmentConfirmDisclaimer" = "Al hacer clic en ”Confirmar cita”, acepta recibir llamadas de marketing o mensajes de texto marcados automáticamente en el número proporcionado. El consentimiento no es una condición de ninguna compra. Se aplican tarifas de mensajes y datos.";
/*dsaAdditionalComments*/
"service.additionalComments" = "Comentarios adicionales";
/*dsaConfirmingApopintment*/
"service.confirmingAppointment" = "Confirmación de cita";
/*dsaConfirmingAppointmentWait*/
"service.confirmingAppointmentWait" = "Espere mientras se confirma su cita.";
/*dsaAppointmentConfirmed*/
"service.appointmentConfirmed" = "¡Cita confirmada!";
/*dsaSomethingWentWrong*/
"service.somethingWentWrong" = "Algo salió mal";
/*dsaAppointmentError*/
"service.appointmentError" = "Hubo un problema para hacer una cita. Por favor, confirme de nuevo.";
/*dsaThreeAppointmentsMax*/
"service.threeAppointmentError" = "Lo sentimos, solo permitimos 3 citas abiertas por vehículo. Intente programar después de completar una cita.";
/*editAppointmentHeading*/
"service.editAppointmentHeading" = "Continuar Editando";
/*editAppointmentSubHeading*/
"editAppointmentSubHeading" = "La edición de servicios restablecerá las selecciones de cita.";
/*dsaCancelAppointment*/
"service.cancelAppointment" = "Cancelar cita";
/*dsaSureCancelAppointment*/
"service.sureCancelAppointment" = "¿Está seguro de que desea cancelar esta cita?";
/*dsaYesCancel*/
"service.yesCancel" = "Si, cancelar";
/*dsaWaitAppointmentCancel*/
"service.waitCancelAppointment" = "Espere mientras se cancela su cita.";
/*dsaCancelAppointmentError*/
"service.cancelAppointmentError" = "Hubo un problema al cancelar la cita. Confirme de nuevo.";
/*dsaAppointmentCancelled*/
"service.appointmentCancelled" = "¡Cita cancelada!";
/*dsaDealerTimezone*/
"service.dealerTimezone" = "*Todos los horarios disponibles están en la zona horaria del concesionario";

/* EV Schedule */
"ev.daysOfTheWeek" = "Días de la semana";
"ev.everyday" = "Cada día";
"ev.lastUpdated" = "Última actualización";
/* Station Details */
"findStations.InNetwork" = "Detalles de la estación de carga";
"findStations.partnerStationHeading" = "Dentro de la red";
"findStations.nonPartnerStationHeading" = "Fuera de la red";
"findStations.SendToCar" = "Enviar al coche";
"findStations.Pricing" = "Precios";
"findStations.Directions" = "Direcciones";
"findStations.UnlockStation" = "Desbloquear estación";
"findStations.StartCharging" = "Iniciar carga";
"findStations.PlugTypes" = "Tipos de enchufes";
"findStations.Favorites" = "Favoritos";

/* Available Plugs text */
"findStations.AvailablePlugs" = "Conectores disponibles";

/* Nearby Stations */
"findStations.NearbyStations" = "Estaciones Cercanas";

/* Partners Filter */
"findStations.partners" = "Socios";

/* Charge management within the app is only available for network stations. */
"findStations.ChargingNotAvailable" = "Esta estación está sin conexión, por lo que la carga no está disponible en la aplicación.";

/* Clear All filter button */
"findStations.ClearAll" = "Limpiar todo";

/* Partners filter button */
"findStations.Partners" = "Socios";

/* Tesla Stations */
"tesla.station.adaptor.warning" = "Adaptor may be needed to charge at this station.";
