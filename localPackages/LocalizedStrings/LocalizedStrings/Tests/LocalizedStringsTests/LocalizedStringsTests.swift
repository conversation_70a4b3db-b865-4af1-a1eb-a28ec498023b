// Copyright © 2024 Toyota. All rights reserved.

import XCTest
@testable import LocalizedStrings

final class LocalizedStringsTests: XCTestCase {

    override func setUp() {
        super.setUp()
    }

    override func tearDown() {
        super.tearDown()
    }

    func testAdaptorMayBeNeededWarning_DefaultValue() {
        let expectedString = "Adaptor may be needed to charge at this station"

        let actualString = Strings.EvTeslaStations.adaptorMayBeNeededWarning

        XCTAssertEqual(actualString, expectedString, "The adaptor warning string should match the default value.")
    }

    func testAdaptorMayBeNeededWarning_LocalizationKey() {
        let expectedKey = "tesla.station.adaptor.warning"

        let warningString = Strings.EvTeslaStations.adaptorMayBeNeededWarning

        XCTAssertFalse(warningString.isEmpty, "The adaptor warning string should not be empty.")
    }
}
