// Copyright © 2023 Toyota. All rights reserved.

import Foundation

public enum Feature: String, Equatable {
    case achPayment
    case addServiceRecord
    case autoDrive
    case cerence
    case chargeAssist
    case collisionAssistance
    case connectedCard
    case connectedSupport
    case criticalAlert
    case dashboardLights
    case dealerAppointment
    case digitalKey
    case dkChat
    case doorLockCapable
    case driverCompanion
    case driverScore
    case dtcAccess
    case dynamicNavi
    case ecoHistory
    case ecoRanking
    case evBattery
    case evChargeStation
    case evPublicChargingControl
    case evRemoteServices
    case evTimerSettings
    case evVehicleStatus
    case financialServices
    case financialServicesV2
    case flexRental
    case howToVideos
    case hybridPulse
    case hybridTrips
    case importantMessage
    case insurance
    case lastParked
    case lcfs
    case linkedAccounts
    case maintenanceTimeline
    case marketingCard
    case marketingConsent
    case mainConsentEditable = "masterConsentEditable"
    case multiDayCharging
    case myDestination
    case ownersManual
    case paidProduct
    case parking
    case personalizedSettings
    case proXSeats
    case recentTrip
    case remoteDtc
    case remoteParking
    case remoteService
    case roadsideAssistance
    case safetyRecall
    case scheduleMaintenance
    case serviceHistory
    case serviceShopAdvancedSearch
    case shopGenuineParts
    case ssaDownload
    case sxmRadio
    case telemetry
    case toyotaForFamily = "tff"
    case tirePressure
    case v1g
    case vaSetting
    case vehicleDiagnostic
    case vehicleHealthReport
    case vehicleSpecifications
    case vehicleStatus
    case wallet
    case wattTime
    case wifi
    case wifiTrialReminderCard
    case xcapp
    case xcappV2
    case evSwap
    case cdas
    case evGo
    case evIonna
    case teslaSuperChargerNetwork
}
