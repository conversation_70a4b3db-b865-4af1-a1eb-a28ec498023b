// Copyright © 2023 Toyota. All rights reserved.

enum Paths {
    // MARK: - OneapiClient
    /// DeviceStatusAPI
    enum DeviceStatus {
        static let getAppVersion = "/oneapi/v1/mobile-utilites/app-details/ios"
    }

    enum Account {
        static let accountUpdate = "/oneapi/v4/account"
    }

    enum ChargeAssist {
        static let checkProgramAvailability =
            "/charging/v2/driver/program-availability"
        static let checkEnrollment = "/charging/v2/driver/enrollment-check"
        static let enrollment = "/charging/v2/driver/enrollment"
        static let unenrollment = "/charging/v2/driver/unenroll"
        static let processStatus = "/charging/v2/driver/process-status"
        static let enrollmentToggle = "/charging/v2/driver/enrollment/toggle"
    }

    enum Consent {
        static let customerConsent = "/v1/customerconsent"
    }

    enum Vehicle {
        static let vehicleList = "/oneapi/v2/vehicle/guid"
        static let howToVideos = "/oneapi/v1/videos"
        static let telemetry = "/oneapi/v2/telemetry"
        static let electricStatusV2 = "/oneapi/v2/electric/status"
        static let electricStatusV3 = "/oneapi/v3/electric/status"
        static let realTimeStatus = "/oneapi/v2/electric/realtime-status"
        static let electricChargeSession = "/charging/v2/charger/session"
        static let preferredVehicle = "/oneapi/v1/preferred/vehicle"
        static let removeVehicle = "/oneapi/v1/vehicle-association/vehicle/sold"
        static let vehicleSubscriptions = "/oneapi/v2/vehicle-subscriptions"
        static let enrollmentCheck = "/charging/v2/driver/enrollment-check"
        static let enrollment = "/charging/driver/enrollment"
        static let createModifySchedules = "/oneapi/v1/electric/charging"
        static let createPHEVSchedule = "v2/electric/command"
        static let legalContent = "/charging/driver/legal-content"
        static let chargeStatistics = "/charging/vehicle/charge-statistics"
        static let ecoSchedules = "/charging/vehicle/eco-schedules"
        static let ecoEnrollment = "/charging/vehicle/mc-eco-enrollment"
        static let ecoEnrollmentV2 = "/charging/v2/vehicle/mc-eco-enrollment"
        static let chargeHistory = "/charging/vehicle/charge-history"
        static let multidaySchedule = "oneapi/v3/electric/status"
    }

    enum VehicleHealth {
        static let vehicleHealthReport = "/oneapi/v1/vehiclehealth/report"
        static let serviceCampaigns = "/oneapi/v2/service-campaign"
        static let vehicleDiagnostics = "/oneapi/v1/vehiclehealth/status"
    }

    enum Remote {
        static let cy17Remote = "/oneapi/v1/legacy/remote/command"
        static let cy17PlusRemote = "/oneapi/v1/global/remote/command"
        static let ng86Remote = "/oneapi/v2/remote/ng86/command"

        static let cy17EngineStatus = "/oneapi/v1/legacy/remote/engine-status"
        static let cy17PlusEngineStatus =
            "/oneapi/v1/global/remote/engine-status"
        static let ng86EngineStatus = "/oneapi/v1/remote/ng86/engine-status"
    }

    enum VehicleNickName {
        static let vehicleNickNameCY17Plus =
            "/oneapi/v1/vehicle-association/vehicle"
        static let vehicleNickNameCY17 = "/oneapi/v1/legacy/oneaccount/vehicles"
    }

    enum VehicleSoftware {
        static let otaUpdateDetails = "/oneapi/v1/ota/update"
        static let otaUpdateVersions = "/oa21mm/v1/ota/update/versions"
        static let otaUpdateDetails21MM = "/oa21mm/v1/ota/notification"
        static let otaAuthorize21MM = "/oa21mm/v1/ota/update/authorize"
    }

    enum VehicleInfo {
        static let vehicleSoftwareUpdateCheck = "/oa21mm/v1/ota/update/check"
        static let vehicleSpecifications = "/oneapi/v1/vehicle/vehicle-spec"
        static let vehicleDetails = "/oneapi/v1/one/vehicle"
        static let vehicleManuals = "/oneapi/v2/manuals"
        static let vehicleManualPDF = "/oneapi/v2/manual/pdf/"
        static let DashboardLights = "/oneapi/v1/dashboardlights"
    }

    enum Subscription {
        static let getSubscription = "/oneapi/v2/vehicle-subscriptions"
    }

    enum AccountSettings {
        static let getUserAvatar = "/oa21mm/v2/profile/picture/"
        static let vehicleAlerts = "/oneapi/v1/vehicle-alerts"
        static let notificationHistory = "/oneapi/v2/notification/history"
        static let getUserDetails = "/oneapi/v4/account"
        static let getRemoteUserDetails = "/oneapi/v3/account/secondaryUserName"
        static let getUserDetailsByEmail = "/oneapi/v1/account/emailsearch"
    }

    enum Dashboard {
        static let notificationHistory = "/oneapi/v2/notification/history"
    }

    enum GuestDriver {
        static let cy17GuestDriver = "/oneapi/v1/legacy/remote/profile-settings"
        static let cy17PlusGuestDriver =
            "/oneapi/v1/global/remote/profile-settings"
        static let ng86GuestDriver = "/oneapi/v1/remote/ng86/profile-settings"
        static let searchDriver = "/oneapi/v2/account/redact/search"
        static let inviteDriver = "/oneapi/v1/subscription/remoteguid"
        static func getGuestActivationUrl(with path: String) -> String {
            String(format: "/oneapi/v1/\(path)")
        }
    }

    enum VehicleShop {
        static let ubiOffers = "/oneapi/v2/ubi/offers"
        static let sxmAccount = "/oneapi/v1/radio"
    }

    enum RemoteDetail {
        static let inviteRemoteDriver = "/oneapi/v1/subscription/remoteguid"
    }

    enum VehicleStatus {
        static let statusNG86 = "/oneapi/v1/remote/ng86/status"
        static let statusCY17 = "/oneapi/v2/legacy/remote/status"
        static let statusCY17Plus = "/oneapi/v1/global/remote/status"
    }

    enum ReconsentStatus {
        static let reconsentEligibility = "/oneapi/v1/reconsent/eligibility"
    }

    enum Dealer {
        static let preferredDealer = "/oneapi/v1/preferred-dealer"
        static let getAppointments =
            "/serviceshop/oneapi/scheduler/v1/appointments"
        static let getAppointmentDetails =
            "/serviceshop/oneapi/scheduler/v1/appointment/"
        // Get Dealer Information based on dealerCode from Preferred Dealer Api
        static let dealerDetails =
            "/serviceshop/oneapi/v1/service-shop/oneapp/dealers/toyotaCode/"
        static let pastAndUpcomingAppointments =
            "/serviceshop/oneapi/scheduler/v1/appointments"
        //        static let dealerData = "/serviceshop/oneapi/v1/service-shop/oneapp/dealers/toyotaCode/"
        // Make An Appointment for schedulingAvailable = true, Call Dealer for schedulingAvailable = false
        static let appointmentInitialize =
            "/serviceshop/oneapi/scheduler/v1/appointment/initialize"
        static let dealerServices = "/serviceshop/oneapi/scheduler/v1/services"
        static let advisorList = "/serviceshop/oneapi/scheduler/v1/advisors"
        static let transportationDetails =
            "/serviceshop/oneapi/scheduler/v1/transportations"
        static let selectDealerServiceDate =
            "/serviceshop/oneapi/scheduler/v1/timeslots/availability"
        static let availableTimeSlot =
            "/serviceshop/oneapi/scheduler/v1/timeslots"
        static let requestAppointment =
            "/serviceshop/oneapi/scheduler/v1/appointment"
        static let advisorDetail =
            "/serviceshop/oneapi/scheduler/v1/advisors/vcard"
        // Maintenance
        static let maintenanceTimeline =
            "/oneapi/v2/vehicle/maintenance-schedule"
        // Dealer Search
        static let dealerSearch =
            "/serviceshop/oneapi/v1/service-shop/oneapp/dealers"
        static let dealerSearchMap =
            "/serviceshop/oneapi/v1/service-shop/oneapp/dealers/map"
        static let dealerFilters = "/serviceshop/oneapi/scheduler/v1/filters"
    }

    enum Wallet {
        static let walletInfo = "/oneapi/v1/payment/wallet"
        static let transactions = "/oneapi/v1/payment/wallet/transactions"
        static let walletConfig = "/oneapi/v1/payment/config"
        static let paymentMethod = "/oneapi/v1/payment/wallet/payment-method"
        static let deleteCard =
            "/oneapi/v1/payment/wallet/payment-method/paymentMethodId"
        static let makeCardDefault =
            "/oneapi/v1/payment/wallet/payment-method/paymentMethodId/default"
    }

    enum NonCV {
        static let preferredDealer = "/oneapi/v1/preferred-dealer"
        static let serviceHistory = "/oneapi/v1/servicehistory/vehicle/summary"
        static let addServiceHistoryrecord =
            "/oneapi/v1/servicehistory/vehicle/createServiceHistory"
    }

    enum DashboardVehicleStatus {
        // Tire pressure data
        static let tirePressure = "/oneapi/v1/telemetry/tires/pressure"

        // Vehicle status
        static let cy17Status = "/oneapi/v2/legacy/remote/status"
        static let ng86Status = "/oneapi/v1/remote/ng86/status"
        static let cy17PlusStatus = "/oneapi/v1/global/remote/status"

        // refresh
        static let cy17Refresh = "/oneapi/v1/legacy/remote/refresh-status"
        static let ng86Refresh = "/oneapi/v1/remote/ng86/refresh-status"
        static let cy17PlusRefresh = "/oneapi/v1/global/remote/refresh-status"
    }

    enum Announcements {
        static let eligibility = "/oneapi/v1/vgi/eligibility"
        static let marketingBanners = "/oneapi/v2/marketing/banner"
        static let wifiReminder = "/oneapi/v1/wifi/expiration-reminder"
        static let dataConsent = "/oneapi/v2/dataconsent"
        static let acceptDataConsent = "/oneapi/v1/customerconsent"
        static let wifiAcknowlegement = "/oneapi/v1/wifi/reminder-ack"
        static let evSwap = "/oneapi/fleet/v2/balance"
        static let lcfsDashboard = "/oneapi/v1/vgi/lcfs/dashboard"
        static let installUpdate = "/oneapi/v1/customer/ota"
    }

    enum AnnouncementCenter {
        static let acknowledgeMessage = "/oneapi/v1/acknowledge/message"
    }

    enum DigitalKey {
        // Key status
        static let keyStatus = "v1/digital-key/status"

        // Tokens
        static let keyGenralToken = "/auth/token"
        static let keyOwnerToken = "v1/digital-key/ownertoken"
        static let deleteKeyToken = "v1/digital-key/token"
        static let lukToken = "v1/digital-key/luktoken"
        static let rotateToken = "v1/digital-key/rotatetoken"

        // Share invite
        static let invite = "v1/digital-key/invite"
        static let pendingInvite = "v1/digital-key/pendinginvite"
        static let luk = "v1/digital-key/luks"

        // HSM unlock
        static let hsmUnlock = "v1/digital-key/hsmunlock"

        // Delete
        static let deleteDigitalKey = "v1/digital-key/token"
        static let vehicleId = "v1/digital-key/vehicleid"
    }

    enum ConnectedVehicle {
        static let casStatus = "/v1/vehicle/remote/cdas/status"
    }

    enum CabinAwareness {
        static let casFeedback = "/v1/vehicle/remote/cdas/report"
    }

    enum FindStations {
        static let nearestFuelStations =
            "/oneapi/v2/electric/nearest-fuel-stations"
        static let evChargingStations = "/charging/v2/locations"
    }

    enum Climate {
        static let climateSettings = "/oneapi/v1/global/remote/climate-settings"
        static let ng86ClimateSettings =
            "/oneapi/v1/remote/ng86/climate-settings"
        static let postClimateCommand = "/oneapi/v2/electric/command"
        static let scheduleList = "/oneapi/v2/electric/ac-reservation"
    }

    enum ChargeManagement {
        static let startCharge = "/charging/v2/charger/start"
        static let stopCharge = "/charging/v2/charger/stop"
        static let chargeSession = "/charging/v2/charger/session"
    }

    enum DrivePulse {
        static let driverScore = "/oneapi/v3/canbus/score"
        static let tripListDetails = "/oneapi/v3/canbus/trip"
        static let tripListEvents = "/oneapi/v1/canbus/trip/events"
        static let clearHistory = "/oneapi/v1/preference/clear-history"
        static let optIn = "/oneapi/v1/telemetry/product-registration"
        static let optOut = "/oneapi/v1/telemetry/product-unregistration"
    }
}
