// Copyright © 2023 Toyota. All rights reserved.

/// Factory methods to get the all the Api request instance. from this only other module will access
public enum OneapiContainer {
    /// Device status request instance
    public static var deviceStatus: DeviceStatusRequestProvider { DeviceStatusRequest() }
    public static var vehicle: VehicleRequestProvider { VehicleRequest() }
    public static var chargeAssist: ChargeAssistProvider { ChargeAssistRequest() }
    public static var remote: RemoteRequestProvider { RemoteRequest() }
    public static var vehicleNickName: VehicleNickNameRequestProvider { VehicleNickNameRequest() }
    public static var vehicleInfo: VehicleInfoRequestProvide { VehicleInfoRequest() }
    public static var vehicleSoftware: VehicleSoftwareRequestProvide { VehicleSoftwareRequest() }
    public static var vehicleShop: VehicleShopRequestProvider { VehicleShopRequest() }
    public static var subscription: SubscriptionRequestProvider { SubscriptionRequest() }
    public static var accountSettings: AccountSettingsRequestProvider { AccountSettingsRequest() }
    public static var dashboardNotifications: DashboardRequestProvider { DashboardRequest() }
    public static var guestDriver: GuestDriverRequestProvider { GuestDriverRequest() }
    public static var remoteDetail: RemoteDetailRequestProvider { RemoteDetailRequest() }
    public static var nonCV: NonCVRequestProvider { NonCVRequest() }
    public static var vehicleStatus: VehicleStatusRequestProvider { VehicleStatusRequest() }
    public static var dealer: DealerRequestProvider { DealerRequest() }
    public static var wallet: WalletRequestProvider { WalletRequest() }
    public static var dashboardVehicleStatus: DashboardVehicleStatusRequestProvider { DashboardVehicleStatusRequest() }
    public static var financialService: FinancialServiceRequestProvider { FinancialServiceRequest() }
    public static var vehicleHealth: VehicleHealthRequestProvider { VehicleHealthRequest() }
    public static var announcements: AnnouncementsRequestProvider { AnnouncementsRequest() }
    public static var consentStatus: ConsentStatusRequestProvider { ConsentStatusRequest() }
    public static var connectedVehicle: ConnectedVehicleRequestProvider { ConnectedVehicleRequest() }
    public static var cabinAwareness: CabinAwarenessRequestProvider { CabinAwarenessRequest() }
    public static var findStations: FindStationsRequestProvider { FindStationsRequest() }
    public static var climateSettings: ClimateRequestProvider { ClimateRequest() }
    public static var publicCharging: PublicChargingRequestProvider { PublicChargingRequest() }
    public static var drivePulseAndTrips: DrivePulseAndTripsRequestProvider { DrivePulseAndTripsRequest() }
}
